# راهنمای تنظیم کرون سرور برای افزونه SETIA

## مشکل یافت نشدن فایل wp-load.php

اگر هنگام اجرای کرون با خطای زیر مواجه شدید:

```
خطا: فایل wp-load.php یافت نشد. لطفاً مسیر فایل را بررسی کنید.
```

این خطا به این معنی است که اسکریپت کرون نمی‌تواند مسیر فایل wp-load.php را پیدا کند. برای رفع این مشکل، راه‌حل‌های زیر را امتحان کنید:

## راه حل 1: استفاده از ابزار مسیریاب

1. فایل `cron-path-finder.php` را از طریق مرورگر اجرا کنید:
   ```
   https://yourdomain.com/wp-content/plugins/setia-content-generator/cron-path-finder.php
   ```

2. این ابزار مسیرهای مختلف را بررسی می‌کند و مسیر صحیح wp-load.php را به شما نشان می‌دهد.

3. بر اساس نتایج، فایل‌های `cron-trigger.php` و `internal-cron.php` را ویرایش کنید.

## راه حل 2: تنظیم مسیر دستی

1. از طریق FTP یا کنترل پنل هاست، مسیر دقیق فایل wp-load.php را پیدا کنید.
   - معمولاً این فایل در ریشه نصب وردپرس قرار دارد.

2. فایل `cron-trigger.php` را ویرایش کرده و مسیر دقیق را به صورت زیر وارد کنید:

```php
// بارگذاری وردپرس - مسیر دقیق
$wp_load_path = '/home/<USER>/public_html/wp-load.php'; // مسیر را با مسیر واقعی جایگزین کنید

if (file_exists($wp_load_path)) {
    require_once($wp_load_path);
} else {
    die('خطا: فایل wp-load.php در مسیر ' . $wp_load_path . ' یافت نشد.');
}
```

3. همین تغییر را در فایل `internal-cron.php` نیز اعمال کنید.

## راه حل 3: استفاده از مسیر نسبی

اگر افزونه در مسیر استاندارد وردپرس نصب شده است، می‌توانید از مسیر نسبی استفاده کنید:

```php
// بارگذاری وردپرس با مسیر نسبی
$wp_load_path = dirname(dirname(dirname(dirname(__FILE__)))) . '/wp-load.php';

if (file_exists($wp_load_path)) {
    require_once($wp_load_path);
} else {
    die('خطا: فایل wp-load.php در مسیر ' . $wp_load_path . ' یافت نشد.');
}
```

## راه حل 4: استفاده از ABSPATH

اگر می‌توانید فایل wp-config.php را پیدا کنید، می‌توانید از ABSPATH استفاده کنید:

1. ابتدا wp-config.php را بارگذاری کنید:

```php
// بارگذاری wp-config.php
$wp_config_path = '/home/<USER>/public_html/wp-config.php'; // مسیر را با مسیر واقعی جایگزین کنید
require_once($wp_config_path);

// سپس از ABSPATH استفاده کنید
require_once(ABSPATH . 'wp-load.php');
```

## تنظیم کرون جاب در سی‌پنل

1. به کنترل پنل هاست خود وارد شوید و به بخش کرون جاب (Cron Jobs) بروید.

2. یک کرون جاب جدید با فرمت زیر ایجاد کنید:
   ```
   wget -q -O /dev/null "https://yourdomain.com/wp-content/plugins/setia-content-generator/cron-trigger.php?key=YOUR_SECURITY_KEY"
   ```
   یا
   ```
   curl --silent "https://yourdomain.com/wp-content/plugins/setia-content-generator/cron-trigger.php?key=YOUR_SECURITY_KEY" > /dev/null
   ```

3. تناوب اجرا را بر اساس نیاز خود تنظیم کنید (مثلاً هر ساعت).

4. کلید امنیتی را از بخش تنظیمات کرون در داشبورد وردپرس کپی کنید و در URL بالا جایگزین کنید.

## عیب‌یابی بیشتر

اگر همچنان با مشکل مواجه هستید، موارد زیر را بررسی کنید:

1. **دسترسی فایل‌ها**: مطمئن شوید که فایل‌های `cron-trigger.php` و `internal-cron.php` دسترسی اجرا (755) دارند.

2. **لاگ خطاها**: لاگ خطاهای PHP را بررسی کنید تا پیام‌های خطای دقیق‌تر را مشاهده کنید.

3. **تست دستی**: فایل کرون را به صورت دستی از طریق مرورگر اجرا کنید تا خطاهای احتمالی را مشاهده کنید.

4. **مسیر PHP**: اگر از خط فرمان برای اجرای کرون استفاده می‌کنید، مطمئن شوید که مسیر PHP صحیح است:
   ```
   /usr/bin/php /home/<USER>/public_html/wp-content/plugins/setia-content-generator/cron-trigger.php
   ```

## تماس با پشتیبانی

اگر همچنان با مشکل مواجه هستید، لطفاً با تیم پشتیبانی تماس بگیرید و اطلاعات زیر را ارائه دهید:
- خطای دقیق دریافت شده
- نتایج ابزار مسیریاب
- ساختار پوشه‌های سایت وردپرس شما
- نوع هاست و کنترل پنل مورد استفاده 