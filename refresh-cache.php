<?php
/**
 * Cache Refresh Script for SETIA Plugin
 * This script forces a cache refresh for CSS and JS files
 */

// Security check
if (!defined('ABSPATH')) {
    // Try to load WordPress
    $wp_load_paths = [
        '../../../wp-load.php',
        '../../../../wp-load.php',
        '../../../../../wp-load.php',
        dirname(dirname(dirname(__DIR__))) . '/wp-load.php',
        dirname(dirname(dirname(dirname(__DIR__)))) . '/wp-load.php'
    ];
    
    $wp_loaded = false;
    foreach ($wp_load_paths as $path) {
        if (file_exists($path)) {
            require_once $path;
            $wp_loaded = true;
            break;
        }
    }
    
    if (!$wp_loaded) {
        die('WordPress not found. Please run this script from WordPress admin or ensure wp-load.php is accessible.');
    }
}

// Force cache refresh
$current_time = time();
update_option('setia_asset_version', $current_time);
update_option('setia_css_version', $current_time);
update_option('setia_js_version', $current_time);

// Clear WordPress cache if possible
if (function_exists('wp_cache_flush')) {
    wp_cache_flush();
}

echo "Cache refreshed successfully! New version: " . $current_time;
?>
