/**
 * اصلاح مستقیم و فوری مشکلات صفحه زمانبندی
 * این اسکریپت با اولویت بسیار بالا اجرا می‌شود
 */

// اجرای فوری - بدون تاخیر
(function() {
    // تابع اصلی برای اعمال اصلاحات
    function applyDirectFixes() {
        console.log("اعمال اصلاحات مستقیم...");
        
        // حذف کامل استایل‌های قبلی و اعمال استایل‌های جدید
        var styleElement = document.createElement("style");
        styleElement.id = "setia-direct-fixes-style";
        styleElement.innerHTML = `
            /* حذف کامل آیکون از کنار عنوان "تکرار زمانبندی" */
            .setia-form-group.description label[for="repeat_status"]:before {
                display: none !important;
                content: none !important;
                visibility: hidden !important;
                opacity: 0 !important;
                width: 0 !important;
                height: 0 !important;
                background: none !important;
                margin: 0 !important;
                padding: 0 !important;
                position: absolute !important;
                left: -9999px !important;
            }
            
            .setia-form-group.description label[for="repeat_status"] {
                padding-right: 0 !important;
                padding-left: 0 !important;
                background: none !important;
                background-image: none !important;
                color: #333 !important;
                font-weight: 600 !important;
                display: block !important;
                margin-bottom: 10px !important;
                border: none !important;
            }
            
            /* فیکس کامل مشکل متن سفید در بخش تکرار */
            .setia-repeat-options,
            .setia-repeat-options *,
            .setia-repeat-toggle,
            .setia-repeat-toggle *,
            .repeat-option-card,
            .repeat-option-card *,
            #repeat_options,
            #repeat_options * {
                color: #333 !important;
                text-shadow: none !important;
            }
            
            /* اطمینان از نمایش تنظیمات تصویر شاخص */
            #setia-image-options-container {
                display: block !important;
                visibility: visible !important;
                opacity: 1 !important;
                height: auto !important;
                overflow: visible !important;
                max-height: none !important;
                position: static !important;
            }
            
            #setia-image-options-container * {
                display: block !important;
                visibility: visible !important;
                opacity: 1 !important;
            }
        `;
        
        // اضافه کردن استایل به هدر صفحه
        document.head.appendChild(styleElement);
        
        // اعمال مستقیم استایل‌ها به عناصر DOM
        function applyDirectStyles() {
            // حذف آیکون از کنار عنوان "تکرار زمانبندی"
            var repeatLabels = document.querySelectorAll('.setia-form-group.description label[for="repeat_status"]');
            for (var i = 0; i < repeatLabels.length; i++) {
                var label = repeatLabels[i];
                label.setAttribute("style", "padding-right: 0 !important; background: none !important; color: #333 !important; font-weight: 600 !important;");
            }
            
            // فیکس متن سفید در بخش تکرار
            var allElements = document.querySelectorAll('.setia-repeat-options *, .repeat-option-card *, #repeat_options *');
            for (var j = 0; j < allElements.length; j++) {
                allElements[j].style.color = "#333";
            }
            
            // اطمینان از نمایش تنظیمات تصویر شاخص
            var imageContainer = document.getElementById('setia-image-options-container');
            if (imageContainer) {
                imageContainer.style.display = "block";
                imageContainer.style.visibility = "visible";
                imageContainer.style.opacity = "1";
                
                var allChildren = imageContainer.querySelectorAll('*');
                for (var k = 0; k < allChildren.length; k++) {
                    allChildren[k].style.display = "block";
                    allChildren[k].style.visibility = "visible";
                    allChildren[k].style.opacity = "1";
                }
            }
            
            // اصلاح مشکل متن سفید در چک‌باکس تکرار
            var repeatCheckbox = document.getElementById('repeat_enabled');
            if (repeatCheckbox) {
                // اضافه کردن رویداد change به چک‌باکس تکرار
                if (!repeatCheckbox.hasEvent) {
                    repeatCheckbox.addEventListener('change', function() {
                        setTimeout(function() {
                            var repeatOptions = document.getElementById('repeat_options');
                            if (repeatOptions) {
                                // اعمال استایل مستقیم به همه المان‌های داخلی
                                var allTexts = repeatOptions.querySelectorAll('*');
                                for (var i = 0; i < allTexts.length; i++) {
                                    allTexts[i].style.color = "#333";
                                }
                                
                                // اعمال استایل به کارت‌های تکرار
                                var cards = repeatOptions.querySelectorAll('.repeat-option-card');
                                for (var j = 0; j < cards.length; j++) {
                                    cards[j].style.backgroundColor = "#fff";
                                    cards[j].style.border = "1px solid #ddd";
                                    cards[j].style.borderRadius = "5px";
                                    cards[j].style.marginBottom = "8px";
                                    
                                    var label = cards[j].querySelector('label');
                                    if (label) {
                                        label.style.color = "#333";
                                        label.style.display = "flex";
                                        label.style.alignItems = "center";
                                        label.style.padding = "10px";
                                        
                                        var icon = label.querySelector('.repeat-icon');
                                        if (icon) icon.style.color = "#666";
                                        
                                        var text = label.querySelector('.repeat-text');
                                        if (text) text.style.color = "#333";
                                    }
                                }
                                
                                // اعمال استایل به گزینه انتخاب شده
                                var selectedRadio = repeatOptions.querySelector('input[type="radio"]:checked');
                                if (selectedRadio) {
                                    var selectedLabel = selectedRadio.nextElementSibling;
                                    if (selectedLabel) {
                                        selectedLabel.style.backgroundColor = "rgba(25, 103, 210, 0.1)";
                                        selectedLabel.style.borderColor = "#1967d2";
                                        selectedLabel.style.color = "#1967d2";
                                        
                                        var selectedIcon = selectedLabel.querySelector('.repeat-icon');
                                        if (selectedIcon) selectedIcon.style.color = "#1967d2";
                                        
                                        var selectedText = selectedLabel.querySelector('.repeat-text');
                                        if (selectedText) selectedText.style.color = "#1967d2";
                                    }
                                }
                            }
                        }, 50);
                    });
                    repeatCheckbox.hasEvent = true;
                }
            }
            
            // اضافه کردن رویداد به کارت‌های تکرار
            var repeatCards = document.querySelectorAll('.repeat-option-card');
            for (var c = 0; c < repeatCards.length; c++) {
                var card = repeatCards[c];
                if (!card.hasClickEvent) {
                    card.addEventListener('click', function(e) {
                        // حذف انتخاب از همه رادیو باتن‌ها
                        var allRadios = document.querySelectorAll('.repeat-option-card input[type="radio"]');
                        for (var r = 0; r < allRadios.length; r++) {
                            allRadios[r].checked = false;
                        }
                        
                        // انتخاب رادیو باتن کلیک شده
                        var radio = this.querySelector('input[type="radio"]');
                        if (radio) radio.checked = true;
                        
                        // حذف استایل انتخاب از همه لیبل‌ها
                        var allLabels = document.querySelectorAll('.repeat-option-card label');
                        for (var l = 0; l < allLabels.length; l++) {
                            allLabels[l].style.backgroundColor = "";
                            allLabels[l].style.borderColor = "#ddd";
                            allLabels[l].style.color = "#333";
                            
                            var icon = allLabels[l].querySelector('.repeat-icon');
                            if (icon) icon.style.color = "#666";
                            
                            var text = allLabels[l].querySelector('.repeat-text');
                            if (text) text.style.color = "#333";
                        }
                        
                        // اعمال استایل به لیبل انتخاب شده
                        var selectedLabel = this.querySelector('label');
                        if (selectedLabel) {
                            selectedLabel.style.backgroundColor = "rgba(25, 103, 210, 0.1)";
                            selectedLabel.style.borderColor = "#1967d2";
                            selectedLabel.style.color = "#1967d2";
                            
                            var selectedIcon = selectedLabel.querySelector('.repeat-icon');
                            if (selectedIcon) selectedIcon.style.color = "#1967d2";
                            
                            var selectedText = selectedLabel.querySelector('.repeat-text');
                            if (selectedText) selectedText.style.color = "#1967d2";
                        }
                    });
                    card.hasClickEvent = true;
                }
            }
        }
        
        // اجرای مستقیم استایل‌ها
        applyDirectStyles();
        
        // اجرای مجدد با تاخیر
        setTimeout(applyDirectStyles, 100);
        setTimeout(applyDirectStyles, 500);
        setTimeout(applyDirectStyles, 1000);
        
        // نظارت بر تغییرات DOM
        var observer = new MutationObserver(function(mutations) {
            applyDirectStyles();
        });
        
        // شروع نظارت بر کل بدنه سند
        observer.observe(document.body, {
            childList: true,
            subtree: true,
            attributes: true,
            attributeFilter: ['style', 'class']
        });
        
        // اضافه کردن رویدادهای لازم
        document.addEventListener('click', function(e) {
            setTimeout(applyDirectStyles, 100);
        });
        
        document.addEventListener('change', function(e) {
            setTimeout(applyDirectStyles, 100);
        });
        
        // اجرای مجدد هر ثانیه
        setInterval(applyDirectStyles, 1000);
        
        // اضافه کردن رویداد برای باز شدن مودال
        var newScheduleBtn = document.getElementById('setia-new-schedule');
        if (newScheduleBtn) {
            newScheduleBtn.addEventListener('click', function() {
                setTimeout(applyDirectStyles, 300);
                setTimeout(applyDirectStyles, 600);
                setTimeout(applyDirectStyles, 1000);
            });
        }
        
        var editButtons = document.querySelectorAll('.setia-action-edit');
        for (var e = 0; e < editButtons.length; e++) {
            editButtons[e].addEventListener('click', function() {
                setTimeout(applyDirectStyles, 300);
                setTimeout(applyDirectStyles, 600);
                setTimeout(applyDirectStyles, 1000);
            });
        }
    }
    
    // اجرای اصلاحات به محض بارگذاری اسکریپت
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', applyDirectFixes);
    } else {
        applyDirectFixes();
    }
})(); 