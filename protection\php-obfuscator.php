<?php
/**
 * SETIA PHP Code Obfuscator
 * Advanced PHP code protection system
 */

if (!defined('ABSPATH')) {
    exit;
}

class SETIA_PHP_Obfuscator {
    
    private $protected_files = [];
    private $obfuscation_key;
    private $license_key;
    
    public function __construct($license_key = '') {
        $this->license_key = $license_key;
        $this->obfuscation_key = $this->generate_obfuscation_key();
        $this->init_protected_files();
    }
    
    /**
     * Initialize list of files to protect
     */
    private function init_protected_files() {
        $this->protected_files = [
            // Core functionality
            'setia-content-generator.php',
            'ajax-handlers.php',
            
            // AI Integration
            'gemini-ai-plugin.php',
            
            // Database operations
            'includes/class-content-generator.php',
            'includes/scheduler.php',
            
            // Admin functionality
            'includes/class-loader.php',
            
            // Critical templates
            'templates/main-page.php',
            'templates/settings-page.php'
        ];
    }
    
    /**
     * Generate unique obfuscation key
     */
    private function generate_obfuscation_key() {
        $site_url = get_site_url();
        $license = $this->license_key;
        return hash('sha256', $site_url . $license . 'SETIA_PROTECTION_2024');
    }
    
    /**
     * Obfuscate PHP code
     */
    public function obfuscate_code($code) {
        // Remove comments and unnecessary whitespace
        $code = $this->remove_comments($code);
        $code = $this->minify_php($code);
        
        // Variable name obfuscation
        $code = $this->obfuscate_variables($code);
        
        // Function name obfuscation
        $code = $this->obfuscate_functions($code);
        
        // String obfuscation
        $code = $this->obfuscate_strings($code);
        
        // Add anti-tampering checks
        $code = $this->add_integrity_checks($code);
        
        return $code;
    }
    
    /**
     * Remove comments from PHP code
     */
    private function remove_comments($code) {
        // Remove single line comments
        $code = preg_replace('/\/\/.*$/m', '', $code);
        
        // Remove multi-line comments
        $code = preg_replace('/\/\*.*?\*\//s', '', $code);
        
        // Remove PHP doc comments
        $code = preg_replace('/\/\*\*.*?\*\//s', '', $code);
        
        return $code;
    }
    
    /**
     * Minify PHP code
     */
    private function minify_php($code) {
        // Remove extra whitespace
        $code = preg_replace('/\s+/', ' ', $code);
        
        // Remove whitespace around operators
        $code = preg_replace('/\s*([=+\-*\/\.,;{}()\[\]])\s*/', '$1', $code);
        
        return trim($code);
    }
    
    /**
     * Obfuscate variable names
     */
    private function obfuscate_variables($code) {
        $variable_map = [];
        
        // Find all variables
        preg_match_all('/\$([a-zA-Z_][a-zA-Z0-9_]*)/', $code, $matches);
        
        $protected_vars = ['_POST', '_GET', '_SESSION', '_COOKIE', '_SERVER', '_FILES', 'GLOBALS'];
        
        foreach (array_unique($matches[1]) as $var) {
            if (!in_array($var, $protected_vars)) {
                $obfuscated = '_' . substr(md5($var . $this->obfuscation_key), 0, 8);
                $variable_map[$var] = $obfuscated;
            }
        }
        
        // Replace variables
        foreach ($variable_map as $original => $obfuscated) {
            $code = preg_replace('/\$' . preg_quote($original) . '\b/', '$' . $obfuscated, $code);
        }
        
        return $code;
    }
    
    /**
     * Obfuscate function names
     */
    private function obfuscate_functions($code) {
        $function_map = [];
        
        // Find custom functions (not WordPress or PHP built-ins)
        preg_match_all('/function\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*\(/', $code, $matches);
        
        $protected_functions = ['__construct', '__destruct', '__call', '__get', '__set'];
        
        foreach (array_unique($matches[1]) as $func) {
            if (!in_array($func, $protected_functions) && !function_exists($func)) {
                $obfuscated = '_f' . substr(md5($func . $this->obfuscation_key), 0, 8);
                $function_map[$func] = $obfuscated;
            }
        }
        
        // Replace function definitions and calls
        foreach ($function_map as $original => $obfuscated) {
            $code = preg_replace('/\b' . preg_quote($original) . '\b/', $obfuscated, $code);
        }
        
        return $code;
    }
    
    /**
     * Obfuscate strings
     */
    private function obfuscate_strings($code) {
        // Encode sensitive strings
        $code = preg_replace_callback('/"([^"]*)"/', function($matches) {
            $string = $matches[1];
            if (strlen($string) > 5 && !$this->is_protected_string($string)) {
                $encoded = base64_encode($string);
                return 'base64_decode("' . $encoded . '")';
            }
            return $matches[0];
        }, $code);
        
        return $code;
    }
    
    /**
     * Check if string should not be obfuscated
     */
    private function is_protected_string($string) {
        $protected_patterns = [
            'wp_',
            'admin_',
            'setia_',
            'action',
            'nonce',
            'POST',
            'GET'
        ];
        
        foreach ($protected_patterns as $pattern) {
            if (strpos($string, $pattern) !== false) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * Add integrity checks to code
     */
    private function add_integrity_checks($code) {
        $integrity_check = '
        if (!defined("SETIA_INTEGRITY_CHECK")) {
            $file_hash = md5_file(__FILE__);
            $expected_hash = "' . md5($code) . '";
            if ($file_hash !== $expected_hash) {
                wp_die("File integrity check failed. Plugin may be corrupted.");
            }
            define("SETIA_INTEGRITY_CHECK", true);
        }';
        
        // Insert after opening PHP tag
        $code = preg_replace('/(<\?php)/', '$1' . $integrity_check, $code, 1);
        
        return $code;
    }
    
    /**
     * Process and obfuscate a file
     */
    public function obfuscate_file($file_path) {
        if (!file_exists($file_path)) {
            return false;
        }
        
        $original_code = file_get_contents($file_path);
        $obfuscated_code = $this->obfuscate_code($original_code);
        
        // Create backup
        $backup_path = $file_path . '.backup';
        file_put_contents($backup_path, $original_code);
        
        // Write obfuscated code
        file_put_contents($file_path, $obfuscated_code);
        
        return true;
    }
    
    /**
     * Obfuscate all protected files
     */
    public function obfuscate_all_files($plugin_dir) {
        $results = [];
        
        foreach ($this->protected_files as $file) {
            $file_path = $plugin_dir . '/' . $file;
            $result = $this->obfuscate_file($file_path);
            $results[$file] = $result;
        }
        
        return $results;
    }
    
    /**
     * Restore original files from backup
     */
    public function restore_files($plugin_dir) {
        foreach ($this->protected_files as $file) {
            $file_path = $plugin_dir . '/' . $file;
            $backup_path = $file_path . '.backup';
            
            if (file_exists($backup_path)) {
                copy($backup_path, $file_path);
                unlink($backup_path);
            }
        }
    }
}
