<?php
/**
 * SETIA Cron Path Finder
 * این فایل برای پیدا کردن مسیر دقیق wp-load.php استفاده می‌شود
 */

echo '<h1>SETIA Cron Path Finder</h1>';
echo '<p>این ابزار برای پیدا کردن مسیر دقیق wp-load.php استفاده می‌شود.</p>';

// نمایش اطلاعات سیستم
echo '<h2>اطلاعات سیستم:</h2>';
echo '<ul>';
echo '<li>مسیر فعلی: ' . __DIR__ . '</li>';
echo '<li>DOCUMENT_ROOT: ' . $_SERVER['DOCUMENT_ROOT'] . '</li>';
echo '<li>SCRIPT_FILENAME: ' . $_SERVER['SCRIPT_FILENAME'] . '</li>';
echo '</ul>';

// بررسی مسیرهای مختلف
$possible_paths = array(
    dirname(dirname(dirname(__FILE__))) . '/wp-load.php',
    dirname(__FILE__, 4) . '/wp-load.php',
    dirname(dirname(dirname(dirname(__FILE__)))) . '/wp-load.php',
    dirname(dirname(__FILE__)) . '/wp-load.php',
    dirname(__FILE__) . '/../wp-load.php',
    dirname(__FILE__) . '/../../wp-load.php',
    dirname(__FILE__) . '/../../../wp-load.php',
    dirname(__FILE__) . '/../../../../wp-load.php',
    $_SERVER['DOCUMENT_ROOT'] . '/wp-load.php',
    realpath($_SERVER['DOCUMENT_ROOT'] . '/../wp-load.php'),
);

echo '<h2>بررسی مسیرهای ممکن:</h2>';
echo '<table border="1" cellpadding="10" style="border-collapse: collapse;">';
echo '<tr><th>مسیر</th><th>وضعیت</th></tr>';

$found = false;
foreach ($possible_paths as $path) {
    echo '<tr>';
    echo '<td>' . $path . '</td>';
    if (file_exists($path)) {
        echo '<td style="background-color: #dff0d8; color: #3c763d;">یافت شد ✓</td>';
        $found = true;
    } else {
        echo '<td style="background-color: #f2dede; color: #a94442;">یافت نشد ✗</td>';
    }
    echo '</tr>';
}
echo '</table>';

// نمایش راه حل
echo '<h2>راه حل:</h2>';
if ($found) {
    echo '<p style="color: green;">حداقل یک مسیر معتبر برای wp-load.php پیدا شد. لطفاً فایل cron-trigger.php و internal-cron.php را با مسیر صحیح ویرایش کنید.</p>';
} else {
    echo '<p style="color: red;">هیچ مسیر معتبری برای wp-load.php پیدا نشد. لطفاً مسیر دقیق را به صورت دستی پیدا کرده و در فایل‌های cron-trigger.php و internal-cron.php وارد کنید.</p>';
    
    echo '<h3>راهنمای پیدا کردن مسیر دقیق:</h3>';
    echo '<ol>';
    echo '<li>به هاست خود وارد شوید و مسیر دقیق فایل wp-load.php را پیدا کنید.</li>';
    echo '<li>فایل‌های cron-trigger.php و internal-cron.php را ویرایش کرده و مسیر دقیق را در آنها وارد کنید.</li>';
    echo '<li>مثال:<br><code>$wp_load_path = \'/home/<USER>/public_html/wp-load.php\';</code></li>';
    echo '</ol>';
}

echo '<h2>کد پیشنهادی برای فایل cron-trigger.php:</h2>';
echo '<pre style="background-color: #f5f5f5; padding: 10px; border: 1px solid #ccc;">';
echo htmlspecialchars('<?php
/**
 * SETIA Cron Trigger
 * این فایل برای فراخوانی کرون وردپرس از طریق کرون سرور واقعی استفاده می‌شود
 */

// بارگذاری وردپرس - مسیر دقیق
$wp_load_path = \'' . $_SERVER['DOCUMENT_ROOT'] . '/wp-load.php\';

if (file_exists($wp_load_path)) {
    require_once($wp_load_path);
} else {
    die(\'خطا: فایل wp-load.php در مسیر \' . $wp_load_path . \' یافت نشد.\');
}

// ادامه کد...
');
echo '</pre>';

// تلاش برای پیدا کردن wp-config.php
echo '<h2>جستجوی wp-config.php:</h2>';
$config_paths = array(
    dirname(dirname(dirname(__FILE__))) . '/wp-config.php',
    dirname(__FILE__, 4) . '/wp-config.php',
    dirname(dirname(dirname(dirname(__FILE__)))) . '/wp-config.php',
    dirname(dirname(__FILE__)) . '/wp-config.php',
    dirname(__FILE__) . '/../wp-config.php',
    dirname(__FILE__) . '/../../wp-config.php',
    dirname(__FILE__) . '/../../../wp-config.php',
    dirname(__FILE__) . '/../../../../wp-config.php',
    $_SERVER['DOCUMENT_ROOT'] . '/wp-config.php',
    realpath($_SERVER['DOCUMENT_ROOT'] . '/../wp-config.php'),
);

echo '<table border="1" cellpadding="10" style="border-collapse: collapse;">';
echo '<tr><th>مسیر</th><th>وضعیت</th></tr>';

$config_found = false;
foreach ($config_paths as $path) {
    echo '<tr>';
    echo '<td>' . $path . '</td>';
    if (file_exists($path)) {
        echo '<td style="background-color: #dff0d8; color: #3c763d;">یافت شد ✓</td>';
        $config_found = true;
        $config_path = $path;
    } else {
        echo '<td style="background-color: #f2dede; color: #a94442;">یافت نشد ✗</td>';
    }
    echo '</tr>';
}
echo '</table>';

if ($config_found) {
    echo '<p>فایل wp-config.php در مسیر <code>' . $config_path . '</code> یافت شد. احتمالاً wp-load.php در همان مسیر قرار دارد.</p>';
    
    // پیشنهاد مسیر wp-load.php بر اساس مسیر wp-config.php
    $suggested_wp_load = dirname($config_path) . '/wp-load.php';
    echo '<p>مسیر پیشنهادی برای wp-load.php: <code>' . $suggested_wp_load . '</code></p>';
    
    if (file_exists($suggested_wp_load)) {
        echo '<p style="color: green;">فایل wp-load.php در مسیر پیشنهادی یافت شد! ✓</p>';
    } else {
        echo '<p style="color: red;">فایل wp-load.php در مسیر پیشنهادی یافت نشد. ✗</p>';
    }
} 