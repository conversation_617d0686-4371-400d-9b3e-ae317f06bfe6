<?php
/**
 * SETIA JavaScript Minifier and Obfuscator
 * Advanced JavaScript code protection system
 */

if (!defined('ABSPATH')) {
    exit;
}

class SETIA_JS_Obfuscator {
    
    private $protected_js_files = [];
    private $obfuscation_key;
    
    public function __construct() {
        $this->obfuscation_key = hash('sha256', get_site_url() . 'SETIA_JS_PROTECTION_2024');
        $this->init_protected_files();
    }
    
    /**
     * Initialize list of JavaScript files to protect
     */
    private function init_protected_files() {
        $this->protected_js_files = [
            'assets/js/main-page-enhanced.js',
            'assets/js/admin.js',
            'assets/js/settings-enhanced.js'
        ];
    }
    
    /**
     * Minify JavaScript code
     */
    public function minify_js($code) {
        // Remove comments
        $code = $this->remove_js_comments($code);
        
        // Remove unnecessary whitespace
        $code = $this->remove_whitespace($code);
        
        // Remove console.log statements
        $code = $this->remove_debug_statements($code);
        
        return $code;
    }
    
    /**
     * Remove JavaScript comments
     */
    private function remove_js_comments($code) {
        // Remove single line comments
        $code = preg_replace('/\/\/.*$/m', '', $code);
        
        // Remove multi-line comments
        $code = preg_replace('/\/\*.*?\*\//s', '', $code);
        
        return $code;
    }
    
    /**
     * Remove unnecessary whitespace
     */
    private function remove_whitespace($code) {
        // Remove extra whitespace
        $code = preg_replace('/\s+/', ' ', $code);
        
        // Remove whitespace around operators and punctuation
        $code = preg_replace('/\s*([=+\-*\/\.,;{}()\[\]:])\s*/', '$1', $code);
        
        // Remove leading/trailing whitespace
        $code = trim($code);
        
        return $code;
    }
    
    /**
     * Remove debug statements
     */
    private function remove_debug_statements($code) {
        // Remove console.log statements
        $code = preg_replace('/console\.log\([^)]*\);?/', '', $code);
        
        // Remove console.warn statements
        $code = preg_replace('/console\.warn\([^)]*\);?/', '', $code);
        
        // Remove console.error statements
        $code = preg_replace('/console\.error\([^)]*\);?/', '', $code);
        
        // Remove alert statements (optional)
        $code = preg_replace('/alert\([^)]*\);?/', '', $code);
        
        return $code;
    }
    
    /**
     * Obfuscate JavaScript code
     */
    public function obfuscate_js($code) {
        // First minify
        $code = $this->minify_js($code);
        
        // Obfuscate variable names
        $code = $this->obfuscate_js_variables($code);
        
        // Obfuscate function names
        $code = $this->obfuscate_js_functions($code);
        
        // Obfuscate strings
        $code = $this->obfuscate_js_strings($code);
        
        // Add anti-tampering
        $code = $this->add_js_protection($code);
        
        return $code;
    }
    
    /**
     * Obfuscate JavaScript variable names
     */
    private function obfuscate_js_variables($code) {
        $variable_map = [];
        
        // Find variable declarations
        preg_match_all('/(?:var|let|const)\s+([a-zA-Z_$][a-zA-Z0-9_$]*)/i', $code, $matches);
        
        $protected_vars = ['jQuery', '$', 'window', 'document', 'console', 'setiaParams'];
        
        foreach (array_unique($matches[1]) as $var) {
            if (!in_array($var, $protected_vars)) {
                $obfuscated = '_' . substr(md5($var . $this->obfuscation_key), 0, 6);
                $variable_map[$var] = $obfuscated;
            }
        }
        
        // Replace variables
        foreach ($variable_map as $original => $obfuscated) {
            $code = preg_replace('/\b' . preg_quote($original) . '\b/', $obfuscated, $code);
        }
        
        return $code;
    }
    
    /**
     * Obfuscate JavaScript function names
     */
    private function obfuscate_js_functions($code) {
        $function_map = [];
        
        // Find function declarations
        preg_match_all('/function\s+([a-zA-Z_$][a-zA-Z0-9_$]*)\s*\(/', $code, $matches);
        
        $protected_functions = ['ready', 'click', 'submit', 'ajax', 'each', 'on'];
        
        foreach (array_unique($matches[1]) as $func) {
            if (!in_array($func, $protected_functions)) {
                $obfuscated = '_f' . substr(md5($func . $this->obfuscation_key), 0, 6);
                $function_map[$func] = $obfuscated;
            }
        }
        
        // Replace function names
        foreach ($function_map as $original => $obfuscated) {
            $code = preg_replace('/\b' . preg_quote($original) . '\b/', $obfuscated, $code);
        }
        
        return $code;
    }
    
    /**
     * Obfuscate JavaScript strings
     */
    private function obfuscate_js_strings($code) {
        // Encode non-critical strings
        $code = preg_replace_callback('/"([^"]*)"/', function($matches) {
            $string = $matches[1];
            if (strlen($string) > 3 && !$this->is_protected_js_string($string)) {
                $encoded = base64_encode($string);
                return 'atob("' . $encoded . '")';
            }
            return $matches[0];
        }, $code);
        
        return $code;
    }
    
    /**
     * Check if JavaScript string should not be obfuscated
     */
    private function is_protected_js_string($string) {
        $protected_patterns = [
            'setia',
            'ajax',
            'POST',
            'GET',
            'wp_',
            'admin_',
            'nonce',
            'action',
            'click',
            'submit',
            'ready'
        ];
        
        foreach ($protected_patterns as $pattern) {
            if (stripos($string, $pattern) !== false) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * Add JavaScript protection mechanisms
     */
    private function add_js_protection($code) {
        $protection = '
        (function(){
            var _0x1a2b=["SETIA_PROTECTED"];
            if(typeof window[_0x1a2b[0]]==="undefined"){
                window[_0x1a2b[0]]=true;
            } else {
                return;
            }
            
            // Anti-debugging
            setInterval(function(){
                if(window.console && (window.console.firebug || window.console.table && /firebug/i.test(window.console.table()))){
                    window.location.href="about:blank";
                }
            }, 1000);
            
            // Disable right-click context menu
            document.addEventListener("contextmenu", function(e){
                e.preventDefault();
                return false;
            });
            
            // Disable F12, Ctrl+Shift+I, Ctrl+U
            document.addEventListener("keydown", function(e){
                if(e.keyCode === 123 || (e.ctrlKey && e.shiftKey && e.keyCode === 73) || (e.ctrlKey && e.keyCode === 85)){
                    e.preventDefault();
                    return false;
                }
            });
        })();';
        
        return $protection . $code;
    }
    
    /**
     * Process and obfuscate a JavaScript file
     */
    public function obfuscate_js_file($file_path) {
        if (!file_exists($file_path)) {
            return false;
        }
        
        $original_code = file_get_contents($file_path);
        $obfuscated_code = $this->obfuscate_js($original_code);
        
        // Create backup
        $backup_path = $file_path . '.backup';
        file_put_contents($backup_path, $original_code);
        
        // Write obfuscated code
        file_put_contents($file_path, $obfuscated_code);
        
        return true;
    }
    
    /**
     * Obfuscate all protected JavaScript files
     */
    public function obfuscate_all_js_files($plugin_dir) {
        $results = [];
        
        foreach ($this->protected_js_files as $file) {
            $file_path = $plugin_dir . '/' . $file;
            $result = $this->obfuscate_js_file($file_path);
            $results[$file] = $result;
        }
        
        return $results;
    }
    
    /**
     * Restore original JavaScript files from backup
     */
    public function restore_js_files($plugin_dir) {
        foreach ($this->protected_js_files as $file) {
            $file_path = $plugin_dir . '/' . $file;
            $backup_path = $file_path . '.backup';
            
            if (file_exists($backup_path)) {
                copy($backup_path, $file_path);
                unlink($backup_path);
            }
        }
    }
    
    /**
     * Create minified versions for production
     */
    public function create_minified_versions($plugin_dir) {
        foreach ($this->protected_js_files as $file) {
            $file_path = $plugin_dir . '/' . $file;
            if (file_exists($file_path)) {
                $code = file_get_contents($file_path);
                $minified = $this->minify_js($code);
                
                // Save as .min.js version
                $min_path = str_replace('.js', '.min.js', $file_path);
                file_put_contents($min_path, $minified);
            }
        }
    }
}
