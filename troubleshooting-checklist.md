# WooCommerce Product Generation Troubleshooting Checklist

## 🔍 Prerequisites Check

### 1. WooCommerce Installation
- [ ] WooCommerce plugin is installed and activated
- [ ] Go to WooCommerce > Status to verify no critical issues
- [ ] Check if you can create products manually in WooCommerce

### 2. API Keys Configuration
- [ ] Go to SETIA Settings page
- [ ] Verify Gemini API key is entered and saved
- [ ] Verify Imagine Art API key is entered and saved
- [ ] Test API connections using the test buttons

### 3. User Permissions
- [ ] Current user has 'edit_posts' capability
- [ ] User can access WooCommerce products section
- [ ] User can create/edit posts in WordPress

## 🐛 JavaScript Console Debugging

### Expected Console Messages (when working):
```
SETIA DEBUG: Initializing product generation functionality
SETIA DEBUG: Form exists: true
SETIA DEBUG: Button exists: true
SETIA DEBUG: Button click event triggered
SETIA DEBUG: generateProduct function called
SETIA DEBUG: Form validation passed
SETIA DEBUG: Sending AJAX request...
SETIA DEBUG: AJAX Success Response: {success: true, data: {...}}
```

### Common Error Messages:
1. **"Form exists: false"** → Form element not found in DOM
2. **"Button exists: false"** → Button element not found in DOM
3. **"setiaParams is undefined"** → JavaScript parameters not loaded
4. **"Form validation failed"** → Product name is empty or too short
5. **"AJAX Error"** → Server-side issue or network problem

## 🔧 Common Issues & Solutions

### Issue 1: Button Not Responding
**Symptoms:** No console messages when clicking button
**Solutions:**
- Check if you're on the correct tab (WooCommerce Product Generation)
- Verify WooCommerce is installed and activated
- Clear browser cache and reload page
- Check for JavaScript errors in console

### Issue 2: Form Validation Errors
**Symptoms:** "Form validation failed" in console
**Solutions:**
- Enter a product name with at least 3 characters
- Make sure all required fields are filled

### Issue 3: API Key Issues
**Symptoms:** Products created but no descriptions/images
**Solutions:**
- Go to SETIA Settings → API Configuration
- Re-enter and save API keys
- Test API connections
- Check WordPress error logs

### Issue 4: AJAX Errors
**Symptoms:** "AJAX Error" in console
**Solutions:**
- Check WordPress error logs
- Verify nonce is valid
- Check server PHP error logs
- Ensure AJAX URL is correct

## 📋 Testing Steps

### Step 1: Basic Functionality Test
1. Go to SETIA → Content Generation
2. Click on "WooCommerce Product Generation" tab
3. Enter product name: "Test Product"
4. Click "Generate Product" button
5. Check console for debug messages

### Step 2: API Keys Test
1. Go to SETIA → Settings
2. Click "Test Connection" for Gemini API
3. Click "Test Connection" for Imagine Art API
4. Both should show success messages

### Step 3: WooCommerce Test
1. Go to WooCommerce → Products
2. Try creating a product manually
3. Verify WooCommerce is working properly

## 🚨 Emergency Fixes

### If Nothing Works:
1. **Deactivate and reactivate SETIA plugin**
2. **Clear all caches** (WordPress, browser, server)
3. **Check WordPress error logs** in wp-content/debug.log
4. **Verify file permissions** for plugin directory
5. **Test with default WordPress theme** to rule out theme conflicts

### Quick Debug Commands:
Add this to your theme's functions.php temporarily:
```php
// Enable WordPress debugging
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
define('WP_DEBUG_DISPLAY', false);
```

## 📞 Support Information

If issues persist, provide this information:
- WordPress version
- WooCommerce version
- PHP version
- Browser console errors
- WordPress error log entries
- Screenshots of the issue

## 🔄 Recent Changes Made

1. Fixed nonce conflict between form and AJAX
2. Added missing showFieldError function
3. Enhanced debugging throughout the process
4. Added backup button click handler
5. Improved error handling and logging
