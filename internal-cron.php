<?php
/**
 * SETIA Internal Cron
 * سیستم کرون داخلی برای اجرای زمانبندی‌ها بدون نیاز به کرون سرور واقعی
 */

// بارگذاری وردپرس - روش اول
$wp_load_path = dirname(dirname(dirname(__FILE__))) . '/wp-load.php';
if (!file_exists($wp_load_path)) {
    // روش دوم - بررسی مسیر نسبی
    $wp_load_path = dirname(__FILE__, 4) . '/wp-load.php';
}

if (!file_exists($wp_load_path)) {
    // روش سوم - بررسی مسیر مطلق
    $possible_paths = array(
        dirname(dirname(dirname(dirname(__FILE__)))) . '/wp-load.php',
        dirname(dirname(__FILE__)) . '/wp-load.php',
        dirname(__FILE__) . '/../wp-load.php',
        dirname(__FILE__) . '/../../wp-load.php',
        dirname(__FILE__) . '/../../../wp-load.php',
        dirname(__FILE__) . '/../../../../wp-load.php',
        $_SERVER['DOCUMENT_ROOT'] . '/wp-load.php',
        realpath($_SERVER['DOCUMENT_ROOT'] . '/../wp-load.php'),
    );
    
    foreach ($possible_paths as $path) {
        if (file_exists($path)) {
            $wp_load_path = $path;
            break;
        }
    }
}

if (file_exists($wp_load_path)) {
    require_once($wp_load_path);
} else {
    // نمایش خطا با جزئیات بیشتر
    die('خطا: فایل wp-load.php یافت نشد. مسیرهای بررسی شده: ' . implode(', ', $possible_paths) . 
        '<br>مسیر فعلی: ' . __DIR__ . 
        '<br>DOCUMENT_ROOT: ' . $_SERVER['DOCUMENT_ROOT']);
}

// بررسی امنیتی
$security_key = isset($_GET['key']) ? $_GET['key'] : '';
$valid_key = get_option('setia_internal_cron_key', '');

// اگر کلید امنیتی تنظیم نشده، یک کلید جدید ایجاد می‌کنیم
if (empty($valid_key)) {
    $valid_key = wp_generate_password(32, false);
    update_option('setia_internal_cron_key', $valid_key);
}

// اگر کلید ارسالی نامعتبر است، خطا نمایش داده شود
if ($security_key !== $valid_key) {
    header('HTTP/1.0 403 Forbidden');
    die('دسترسی غیرمجاز');
}

// تنظیم محدودیت زمانی اجرا
ignore_user_abort(true);
set_time_limit(300); // 5 دقیقه زمان اجرا

// لاگ برای عیب‌یابی
error_log('SETIA INTERNAL CRON: شروع اجرای کرون داخلی');

// دریافت زمانبندی‌های فعال
$schedules = get_option('setia_content_schedules', array());
$now = current_time('timestamp');
$executed = false;

foreach ($schedules as $schedule_id => $schedule) {
    // بررسی فقط زمانبندی‌های فعال
    if ($schedule['status'] !== 'active') {
        continue;
    }
    
    // تبدیل زمان آخرین اجرا به timestamp
    $last_run = !empty($schedule['last_run']) ? strtotime($schedule['last_run']) : 0;
    
    // محاسبه فاصله زمانی بر اساس تناوب
    $interval = 3600; // پیش‌فرض: هر ساعت (3600 ثانیه)
    
    switch ($schedule['frequency']) {
        case 'minutely':
            $interval = 60; // هر دقیقه
            break;
        case 'every5minutes':
            $interval = 300; // هر 5 دقیقه
            break;
        case 'every15minutes':
            $interval = 900; // هر 15 دقیقه
            break;
        case 'every30minutes':
            $interval = 1800; // هر 30 دقیقه
            break;
        case 'hourly':
            $interval = 3600; // هر ساعت
            break;
        case 'twicedaily':
            $interval = 43200; // دو بار در روز (هر 12 ساعت)
            break;
        case 'daily':
            $interval = 86400; // روزانه
            break;
        case 'weekly':
            $interval = 604800; // هفتگی
            break;
        case 'monthly':
            $interval = 2592000; // ماهانه (30 روز)
            break;
    }
    
    // بررسی آیا زمان اجرای زمانبندی رسیده است
    if ($now - $last_run >= $interval) {
        error_log("SETIA INTERNAL CRON: اجرای زمانبندی با شناسه {$schedule_id}");
        
        // اجرای زمانبندی
        if (class_exists('SETIA_Scheduler')) {
            $setia = new SETIA();
            $scheduler = new SETIA_Scheduler($setia->content_generator);
            $scheduler->generate_scheduled_content($schedule_id);
            $executed = true;
            
            error_log("SETIA INTERNAL CRON: زمانبندی با شناسه {$schedule_id} با موفقیت اجرا شد");
        } else {
            error_log("SETIA INTERNAL CRON: خطا - کلاس SETIA_Scheduler یافت نشد");
        }
    }
}

// بروزرسانی زمان آخرین بررسی
update_option('setia_internal_cron_last_check', $now);

if ($executed) {
    echo 'SETIA INTERNAL CRON: حداقل یک زمانبندی با موفقیت اجرا شد';
} else {
    echo 'SETIA INTERNAL CRON: هیچ زمانبندی برای اجرا یافت نشد';
}

// بررسی کلاس SETIA
if (!class_exists('SETIA')) {
    class SETIA {
        public $content_generator;
        
        public function __construct() {
            // بارگذاری کلاس تولید محتوا
            require_once(plugin_dir_path(__FILE__) . 'includes/class-content-generator.php');
            $this->content_generator = new SETIA_Content_Generator();
        }
    }
} 