# SETIA Content Generator - Code Protection System

## Overview

This comprehensive protection system safeguards the SETIA WordPress plugin against reverse engineering, code theft, and unauthorized usage. The system implements multiple layers of security to protect your intellectual property while maintaining optimal performance for legitimate users.

## Protection Features

### 🔐 Multi-Layer Security Architecture

1. **PHP Code Obfuscation**
   - Variable name obfuscation
   - Function name scrambling
   - String encoding (Base64)
   - Comment removal and code minification
   - Integrity checks embedded in code

2. **JavaScript Protection**
   - Code minification and obfuscation
   - Variable and function name scrambling
   - String encoding
   - Anti-debugging measures
   - Developer tools detection

3. **License Verification System**
   - Server-side license validation
   - Hardware fingerprinting
   - Activation limits enforcement
   - Automatic license checks
   - Secure API communication

4. **Anti-Tampering Measures**
   - File integrity monitoring
   - Real-time tamper detection
   - Automatic security responses
   - Code signature verification
   - Emergency shutdown capabilities

## Protected Files

### Critical PHP Files
- `setia-content-generator.php` - Main plugin file
- `ajax-handlers.php` - AJAX request handlers
- `gemini-ai-plugin.php` - AI integration
- `includes/class-content-generator.php` - Core functionality
- `includes/scheduler.php` - Scheduling system
- `includes/class-loader.php` - Plugin loader

### JavaScript Files
- `assets/js/main-page-enhanced.js` - Main page functionality
- `assets/js/admin.js` - Admin interface
- `assets/js/settings-enhanced.js` - Settings page

### Template Files
- `templates/main-page.php` - Main interface
- `templates/settings-page.php` - Settings interface

## Installation & Setup

### 1. Initial Setup

```bash
# Navigate to plugin directory
cd /path/to/setia-plugin

# Ensure protection directory exists
mkdir -p protection

# Set proper permissions
chmod 755 protection/
chmod 644 protection/*.php
```

### 2. License Configuration

1. Access WordPress Admin → SETIA → Security
2. Enter your license key and email
3. Click "Activate License"
4. Verify license status shows "Active"

### 3. Apply Protection

#### Via WordPress Admin
1. Go to SETIA → Security
2. Click "Create Protected Package"
3. Download the protected distribution

#### Via Command Line
```bash
# Create protected distribution
php protection/deploy-protected.php /path/to/plugin /path/to/output

# Create development version (unprotected)
php protection/deploy-protected.php /path/to/plugin /path/to/output --dev-only
```

## Usage Guide

### Creating Protected Distribution

```php
// Initialize protection manager
$protection_manager = new SETIA_Protection_Manager('/path/to/plugin');

// Apply full protection
$results = $protection_manager->apply_full_protection();

// Create distribution package
$package = $protection_manager->create_distribution_package('/output/dir');
```

### Verifying Protection Status

```php
// Check protection status
$status = $protection_manager->verify_protection();

if ($status['overall_status'] === 'protected') {
    echo "Plugin is fully protected";
} else {
    echo "Protection compromised";
}
```

### License Management

```php
// Check license status
$license_system = new SETIA_License_System();
$is_licensed = $license_system->is_licensed();

// Get license details
$license_data = $license_system->get_license_data();
```

## Security Features

### File Integrity Monitoring

- **Real-time Checks**: Continuous monitoring of critical files
- **Hash Verification**: MD5 and SHA256 checksums
- **Tamper Detection**: Immediate alerts on file modifications
- **Automatic Response**: Plugin disabling on security breaches

### Anti-Debugging Measures

- **Developer Tools Detection**: Blocks F12, Ctrl+Shift+I
- **Right-click Disable**: Prevents context menu access
- **Console Protection**: Detects and blocks debugging attempts
- **Source Code Hiding**: Obfuscated code prevents easy analysis

### License Enforcement

- **Server Validation**: Remote license verification
- **Hardware Binding**: Site-specific license activation
- **Usage Limits**: Controlled number of activations
- **Expiration Handling**: Automatic license renewal reminders

## Configuration Options

### Security Settings

```php
// Auto-protection on updates
update_option('setia_auto_protection', true);

// Integrity check frequency
update_option('setia_integrity_frequency', 'daily');

// Security notifications
update_option('setia_security_notifications', true);
```

### Advanced Configuration

```php
// Custom obfuscation key
define('SETIA_OBFUSCATION_KEY', 'your-custom-key');

// License server URL
define('SETIA_LICENSE_SERVER', 'https://your-server.com/api');

// Emergency contact email
define('SETIA_SECURITY_EMAIL', '<EMAIL>');
```

## Deployment Workflow

### Development Phase
1. Work with unprotected source code
2. Test all functionality thoroughly
3. Prepare for production deployment

### Protection Phase
1. Run protection system on source code
2. Verify all protection measures applied
3. Test protected version functionality

### Distribution Phase
1. Create final protected package
2. Generate deployment report
3. Distribute to customers/market

### Maintenance Phase
1. Monitor security status
2. Update protection as needed
3. Handle license renewals

## Troubleshooting

### Common Issues

**Protection Not Applied**
- Verify file permissions (644 for PHP files)
- Check license activation status
- Ensure all dependencies are loaded

**License Activation Failed**
- Verify internet connectivity
- Check license key format
- Confirm email address accuracy

**File Integrity Alerts**
- Restore from backup if available
- Regenerate integrity hashes
- Contact support if persistent

**Performance Issues**
- Monitor obfuscation impact
- Optimize protection settings
- Consider selective protection

### Debug Mode

```php
// Enable debug logging
define('SETIA_PROTECTION_DEBUG', true);

// Check debug logs
tail -f /path/to/wordpress/wp-content/debug.log | grep SETIA
```

## Security Best Practices

### For Developers
1. Keep source code in secure repository
2. Use version control for protection settings
3. Regular security audits
4. Monitor for unauthorized distributions

### For Deployment
1. Use HTTPS for license server communication
2. Implement rate limiting on license API
3. Monitor license usage patterns
4. Regular backup of license database

### For Maintenance
1. Regular integrity checks
2. Monitor security logs
3. Update protection measures
4. Respond quickly to security alerts

## API Reference

### Protection Manager
```php
$manager = new SETIA_Protection_Manager($plugin_dir);
$manager->apply_full_protection();
$manager->remove_protection();
$manager->verify_protection();
```

### License System
```php
$license = new SETIA_License_System();
$license->activate_license($key, $email);
$license->is_licensed();
$license->get_license_data();
```

### Anti-Tampering
```php
$tampering = new SETIA_Anti_Tampering();
$tampering->generate_integrity_hashes($dir);
$tampering->check_file_integrity();
$tampering->verify_code_signature($file);
```

## Support & Contact

For security-related issues or questions about the protection system:

- **Email**: <EMAIL>
- **Documentation**: https://docs.setia-team.com/protection
- **Emergency**: Use emergency disable feature in admin panel

## License

This protection system is proprietary software. Unauthorized modification, distribution, or reverse engineering is strictly prohibited.

Copyright © 2024 SETIA Team. All rights reserved.
