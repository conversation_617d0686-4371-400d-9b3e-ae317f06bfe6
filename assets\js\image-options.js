/**
 * اسکریپت مدیریت تنظیمات تصویر شاخص
 */
(function($) {
    'use strict';
    
    // تابع اصلی مدیریت تنظیمات تصویر
    function initImageOptions() {
        console.log('اسکریپت تنظیمات تصویر بارگذاری شد');
        
        var $checkbox = $('#generate_image');
        var $container = $('#setia-image-options-container');
        
        // بررسی وجود عناصر مورد نیاز
        if (!$checkbox.length || !$container.length) {
            console.log('عناصر مورد نیاز پیدا نشدند - ممکن است هنوز بارگذاری نشده باشند');
            return;
        }
        
        // بررسی و اضافه کردن عنوان اگر وجود نداشته باشد
        if ($container.find('h3').length === 0) {
            $container.prepend('<h3>تنظیمات تصویر شاخص</h3>');
        }
        
        // حذف رویدادهای قبلی برای جلوگیری از تداخل
        $checkbox.off('change.imageOptions');
        
        // اضافه کردن رویداد جدید
        $checkbox.on('change.imageOptions', function() {
            if ($(this).is(':checked')) {
                $container.css('opacity', 0);
                $container.slideDown(300, function() {
                    $container.animate({ opacity: 1 }, 200);
                    
                    // بعد از نمایش، تصاویر پیش‌نمایش را آماده می‌کنیم
                    updateAspectRatioPreviews();
                });
            } else {
                $container.animate({ opacity: 0 }, 200, function() {
                    $container.slideUp(300);
                });
            }
        });
        
        // تنظیم وضعیت اولیه
        if ($checkbox.is(':checked')) {
            $container.show();
            updateAspectRatioPreviews();
        } else {
            $container.hide();
        }
        
        // مدیریت انتخاب نسبت ابعاد
        initAspectRatioSelector();
        
        // بهبود سلکت باکس‌ها
        enhanceSelects();
    }
    
    // بروزرسانی پیش‌نمایش نسبت‌ها
    function updateAspectRatioPreviews() {
        $('.setia-aspect-ratio-option').each(function() {
            var ratio = $(this).data('ratio');
            if (ratio) {
                var parts = ratio.split(':');
                if (parts.length === 2) {
                    var width = parseInt(parts[0]);
                    var height = parseInt(parts[1]);
                    var percentage = (height / width) * 100;
                    
                    if (!isNaN(percentage)) {
                        $(this).find('.aspect-preview').css({
                            'padding-bottom': percentage + '%',
                            'height': 0,
                            'width': '100%',
                            'position': 'relative'
                        });
                    }
                }
            }
        });
    }
    
    // تابع مدیریت انتخابگر نسبت ابعاد
    function initAspectRatioSelector() {
        // حذف رویدادهای قبلی
        $('.setia-aspect-ratio-option').off('click.aspectRatio');
        
        // اضافه کردن رویداد جدید
        $('.setia-aspect-ratio-option').on('click.aspectRatio', function(e) {
            e.preventDefault();
            
            // حذف کلاس انتخاب شده از همه گزینه‌ها
            $('.setia-aspect-ratio-option').removeClass('selected');
            
            // اضافه کردن کلاس به گزینه انتخاب شده
            $(this).addClass('selected');
            
            // انتخاب رادیو باتن
            var ratio = $(this).data('ratio');
            $(this).find('input[type="radio"]').prop('checked', true);
            
            // تنظیم مقدار در سلکت باکس اصلی
            $('#aspect_ratio').val(ratio);
            
            // اضافه کردن افکت انیمیشن برای نمایش بهتر انتخاب
            $(this).css('transform', 'translateY(-5px)');
            setTimeout(function() {
                $('.setia-aspect-ratio-option.selected').css('transform', 'translateY(-3px)');
            }, 300);
        });
        
        // انتخاب گزینه پیش‌فرض
        setTimeout(function() {
            var defaultRatio = $('#aspect_ratio').val() || '1:1';
            $('.setia-aspect-ratio-option[data-ratio="' + defaultRatio + '"]').trigger('click.aspectRatio');
        }, 100);
        
        // بروزرسانی پیش‌نمایش‌ها
        updateAspectRatioPreviews();
    }
    
    // بهبود ظاهر سلکت باکس‌ها
    function enhanceSelects() {
        $('#setia-image-options-container select').each(function() {
            var $select = $(this);
            
            // اضافه کردن کلاس سفارشی
            $select.addClass('setia-enhanced-select');
            
            // اضافه کردن رویداد تغییر برای انیمیشن
            $select.off('change.animation').on('change.animation', function() {
                var $this = $(this);
                $this.addClass('pulse-animation');
                setTimeout(function() {
                    $this.removeClass('pulse-animation');
                }, 500);
            });
        });
    }
    
    // ثبت تابع برای اجرا در زمان‌های مختلف
    window.setiaInitImageOptions = initImageOptions;
    
    // اجرای اولیه بعد از بارگذاری صفحه
    $(document).ready(function() {
        setTimeout(initImageOptions, 100);
        
        // اجرای مجدد بعد از باز شدن مودال
        $(document).on('click', '#setia-new-schedule, .setia-action-edit', function() {
            setTimeout(initImageOptions, 300);
        });
    });
    
})(jQuery); 