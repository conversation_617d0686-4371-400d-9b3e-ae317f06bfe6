# SETIA Content Generator

افزونه تولید محتوای هوشمند برای وردپرس با استفاده از هوش مصنوعی

## ویژگی‌ها

- 🤖 تولید محتوای فارسی با استفاده از Gemini AI
- 🎨 تولید تصاویر با Imagine Art API
- 📝 تولید خلاصه و متادیتا
- ⏰ زمان‌بندی انتشار خودکار
- 📱 رابط کاربری responsive
- 🔒 امنیت بالا

## نصب و راه‌اندازی

### پیش‌نیازها
- WordPress 5.0 یا بالاتر
- PHP 7.4 یا بالاتر
- کلید API Gemini
- کلید API Imagine Art

## نحوه نصب

1. فایل‌های افزونه را در پوشه `wp-content/plugins/SETIA` آپلود کنید.
2. از منوی افزونه‌های وردپرس، افزونه SETIA Content Generator را فعال کنید.
3. به بخش "تولید محتوا SETIA" در منوی وردپرس بروید.
4. API Key های مورد نیاز را در صفحه تنظیمات وارد کنید.

## حل مشکلات رایج

### مشکل: استایل‌ها به درستی اعمال نمی‌شوند

در صورتی که استایل‌ها به درستی اعمال نمی‌شوند، می‌توانید از یکی از روش‌های زیر استفاده کنید:

1. **پاکسازی کش مرورگر**:
   - کلید‌های `Ctrl+F5` یا `Ctrl+Shift+R` را در مرورگر خود فشار دهید.
   
2. **بروزرسانی نسخه دارایی‌ها**:
   - به صفحه تنظیمات افزونه بروید و روی دکمه "پاکسازی کش" کلیک کنید.
   - یا فایل `refresh.php` را در ریشه سایت اجرا کنید.

3. **بررسی فایل‌ها**:
   - اطمینان حاصل کنید که فایل `assets/css/admin.css` وجود دارد و محتوی دارد.
   - اطمینان حاصل کنید که فایل `assets/js/admin.js` وجود دارد و محتوی دارد.

4. **بررسی کنسول مرورگر**:
   - کنسول مرورگر خود را باز کنید (معمولاً با کلید F12) و خطاهای احتمالی را بررسی کنید.

### مشکل: فایل‌های CSS یا JS بارگذاری نمی‌شوند

1. **بررسی مسیرها**:
   - اطمینان حاصل کنید که ساختار پوشه‌ها به صورت زیر است:
     ```
     /assets
       /css
         admin.css
       /js
         admin.js
       /images
         sample-image.jpg
     ```

2. **بررسی مجوزها**:
   - اطمینان حاصل کنید که فایل‌ها و پوشه‌ها دارای مجوز خواندن هستند (معمولاً 644 برای فایل‌ها و 755 برای پوشه‌ها).

3. **بررسی تابع enqueue**:
   - بررسی کنید که تابع `enqueue_admin_assets` در فایل `setia-content-generator.php` به درستی فراخوانی می‌شود.

4. **تست با استایل‌های درون خطی**:
   - اگر فایل CSS بارگذاری نمی‌شود، استایل‌های درون خطی در انتهای صفحه `templates/settings-page.php` به طور خودکار اجرا می‌شوند.

### مشکل: خطای 404 برای فایل‌ها

1. **بررسی ساختار پرمالینک‌ها**:
   - به بخش تنظیمات > پرمالینک‌ها بروید و ساختار پرمالینک‌ها را ذخیره کنید.

2. **بررسی ساختار فایل `.htaccess`**:
   - اگر از Apache استفاده می‌کنید، فایل `.htaccess` باید به درستی پیکربندی شده باشد.

3. **بروزرسانی کد اصلی**:
   - مسیر دسترسی به فایل‌ها را با استفاده از تابع مطلق `plugin_dir_url(__FILE__)` بررسی کنید.

## تماس و پشتیبانی

برای راهنمایی بیشتر یا گزارش مشکلات میتوانید به آدرس ایمیل `<EMAIL>` پیام ارسال کنید. 