<?php
/*
Plugin Name: Gemini AI Integration
Description: اتصال وردپرس به هوش مصنوعی Gemini
Version: 1.0
Author: Your Name
*/

// فرم ساده در پنل مدیریت
function gemini_admin_menu() {
    add_menu_page('Gemini AI', 'Gemini AI', 'manage_options', 'gemini-ai', 'gemini_ai_page');
}
add_action('admin_menu', 'gemini_admin_menu');

function gemini_ai_page() {
    ?>
    <div class="wrap">
        <h2>ارتباط با Gemini AI</h2>
        <form method="post">
            <textarea name="user_prompt" rows="5" cols="60" placeholder="سؤالت رو بنویس..."></textarea><br>
            <input type="submit" name="submit_prompt" value="ارسال به Gemini">
        </form>
    </div>
    <?php

    if (isset($_POST['submit_prompt'])) {
        $prompt = sanitize_text_field($_POST['user_prompt']);
        $response = gemini_send_prompt($prompt);
        echo "<h3>پاسخ:</h3><pre>" . esc_html($response) . "</pre>";
    }
}

// تابع ارسال به Gemini
function gemini_send_prompt($prompt) {
    // بررسی اعتبار API key
    $api_key = 'AIzaSyBdjcXxXe6xMzled4KtfN8icdtRSSoZeYI';
    if (empty($api_key)) {
        return 'خطا: کلید API تنظیم نشده است.';
    }
    
    // استفاده از مدل gemini-pro به جای gemini-2.0-flash (که ممکن است وجود نداشته باشد)
    $url = "https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent?key=$api_key";

    $body = json_encode([
        "contents" => [
            ["parts" => [["text" => $prompt]]]
        ],
        "generationConfig" => [
            "temperature" => 0.7,
            "topK" => 40,
            "topP" => 0.95,
            "maxOutputTokens" => 2048
        ]
    ]);

    $response = wp_remote_post($url, [
        'headers' => [
            'Content-Type' => 'application/json'
        ],
        'body' => $body,
        'timeout' => 30 // افزایش تایم‌اوت به 30 ثانیه
    ]);

    if (is_wp_error($response)) {
        $error_message = $response->get_error_message();
        error_log("GEMINI API ERROR: " . $error_message);
        return 'خطا در اتصال به Gemini: ' . $error_message;
    }

    $response_code = wp_remote_retrieve_response_code($response);
    $response_body = wp_remote_retrieve_body($response);
    $data = json_decode($response_body, true);
    
    // ثبت لاگ برای دیباگ
    error_log("GEMINI API RESPONSE CODE: " . $response_code);
    
    if ($response_code != 200) {
        return 'خطای HTTP: ' . $response_code . ' - ' . wp_remote_retrieve_response_message($response) . 
               '<br>پاسخ: ' . $response_body;
    }
    
    if (empty($data)) {
        return 'پاسخ خالی یا نامعتبر دریافت شد. پاسخ خام: ' . $response_body;
    }
    
    // بررسی ساختار پاسخ API
    if (isset($data['candidates'][0]['content']['parts'][0]['text'])) {
        return $data['candidates'][0]['content']['parts'][0]['text'];
    } elseif (isset($data['error'])) {
        $error_msg = isset($data['error']['message']) ? $data['error']['message'] : 'خطای نامشخص';
        return 'خطای API: ' . $error_msg;
    } else {
        return 'ساختار پاسخ نامعتبر است: <pre>' . print_r($data, true) . '</pre>';
    }
}

// Function to enqueue scripts and styles
function setia_ai_enqueue_scripts() {
    // Enqueue your plugin's styles and scripts
    $ver = time();
    wp_enqueue_style('setia-ai-style', plugin_dir_url(__FILE__) . 'assets/css/style.css', array(), $ver);
    wp_enqueue_style('setia-ai-modal-improved', plugin_dir_url(__FILE__) . 'assets/css/modal-improved.css', array('setia-ai-style'), $ver);
    wp_enqueue_style('setia-ai-custom-styles', plugin_dir_url(__FILE__) . 'assets/css/custom-styles.css', array('setia-ai-style'), $ver);
    wp_enqueue_style('setia-ai-full-width-fix', plugin_dir_url(__FILE__) . 'assets/css/full-width-fix.css', array('setia-ai-custom-styles'), $ver);
    
    // Enqueue scripts
    wp_enqueue_script('setia-ai-script', plugin_dir_url(__FILE__) . 'assets/js/script.js', array('jquery'), '1.0', true);
    
    // Add your AJAX URL as a JavaScript variable
    wp_localize_script('setia-ai-script', 'setia_ai_ajax', array(
        'ajax_url' => admin_url('admin-ajax.php'),
        'nonce' => wp_create_nonce('setia_ai_nonce')
    ));
}
add_action('admin_enqueue_scripts', 'setia_ai_enqueue_scripts', 999);
