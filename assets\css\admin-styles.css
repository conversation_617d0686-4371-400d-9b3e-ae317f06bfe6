:root {
    --setia-primary: #4a6bef;
    --setia-primary-dark: #3854c8;
    --setia-secondary: #6c49b8;
    --setia-accent: #ff7043;
    --setia-success: #43a047;
    --setia-warning: #ff9800;
    --setia-error: #f44336;
    --setia-gray-100: #f5f5f5;
    --setia-gray-200: #eeeeee;
    --setia-gray-300: #e0e0e0;
    --setia-gray-400: #bdbdbd;
    --setia-text-dark: #202124;
    --setia-text-medium: #5f6368;
    --setia-text-light: #80868b;
    --setia-gradient-primary: linear-gradient(135deg, var(--setia-primary) 0%, var(--setia-secondary) 100%);
    --setia-box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    --setia-border-radius: 8px;
}

/* اطمینان از نمایش صحیح آیکون‌ها */
.dashicons, .dashicons-before:before {
    font-family: dashicons !important;
    display: inline-block;
    line-height: 1;
    font-weight: 400;
    font-style: normal;
    text-decoration: inherit;
    text-transform: none;
    text-rendering: auto;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    width: 20px;
    height: 20px;
    font-size: 20px;
    vertical-align: middle;
    text-align: center;
}

.wrap.setia-history-page, .wrap.setia-scheduler-page {
    max-width: 1300px;
    margin: 20px auto;
    background-color: white;
    padding: 25px;
    border-radius: var(--setia-border-radius);
    box-shadow: var(--setia-box-shadow);
    position: relative;
    overflow: hidden;
}

.wrap.setia-history-page::before, .wrap.setia-scheduler-page::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 5px;
    background: var(--setia-gradient-primary);
}

.setia-history-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
    padding-bottom: 15px;
    border-bottom: 1px solid var(--setia-gray-300);
}

.setia-history-header h1 {
    color: var(--setia-primary);
    display: flex;
    align-items: center;
    gap: 10px;
    margin: 0;
    font-size: 24px;
}

.setia-history-header h1 .dashicons {
    font-size: 28px;
    width: 28px;
    height: 28px;
}

.setia-card {
    background-color: white;
    border-radius: var(--setia-border-radius);
    box-shadow: var(--setia-box-shadow);
    margin-bottom: 20px;
    overflow: hidden;
}

.setia-card-header {
    padding: 15px 20px;
    background: var(--setia-gray-100);
    border-bottom: 1px solid var(--setia-gray-300);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.setia-card-title {
    margin: 0;
    color: var(--setia-text-dark);
    font-size: 16px;
    font-weight: 600;
}

.setia-bulk-actions {
    display: flex;
    align-items: center;
    gap: 10px;
}

.setia-history-table {
    width: 100%;
    border-collapse: collapse;
}

.setia-history-table th {
    background-color: var(--setia-gray-100);
    color: var(--setia-text-dark);
    text-align: right;
    padding: 12px 15px;
    font-weight: 600;
    border-bottom: 2px solid var(--setia-gray-300);
}

.setia-history-table td {
    padding: 12px 15px;
    border-bottom: 1px solid var(--setia-gray-200);
    vertical-align: middle;
}

.setia-history-table tr:hover {
    background-color: rgba(74, 107, 239, 0.05);
}

.setia-history-table tr:last-child td {
    border-bottom: none;
}

.setia-checkbox {
    position: relative;
    width: 18px;
    height: 18px;
    cursor: pointer;
    margin: 0 auto;
    display: block;
}

.setia-checkbox:checked {
    accent-color: var(--setia-primary);
}

.setia-history-row-id {
    text-align: center;
    font-weight: bold;
    color: var(--setia-primary);
}

.setia-tag {
    display: inline-block;
    padding: 3px 8px;
    border-radius: 15px;
    font-size: 12px;
    background-color: var(--setia-gray-200);
    color: var(--setia-text-medium);
    margin-right: 5px;
    margin-bottom: 5px;
}

.setia-tag-tone {
    background-color: rgba(74, 107, 239, 0.1);
    color: var(--setia-primary);
}

.setia-tag-length {
    background-color: rgba(108, 73, 184, 0.1);
    color: var(--setia-secondary);
}

.setia-tag-category {
    background-color: rgba(255, 112, 67, 0.1);
    color: var(--setia-accent);
}

.setia-status {
    display: inline-flex;
    align-items: center;
    padding: 3px 8px;
    border-radius: 15px;
    font-size: 12px;
    font-weight: 600;
    gap: 5px;
}

.setia-status-published {
    background-color: rgba(67, 160, 71, 0.1);
    color: var(--setia-success);
}

.setia-status-draft {
    background-color: rgba(255, 152, 0, 0.1);
    color: var(--setia-warning);
}

.setia-status-unpublished {
    background-color: rgba(189, 189, 189, 0.3);
    color: var(--setia-text-medium);
}

.setia-actions {
    display: flex;
    gap: 8px;
    justify-content: flex-end;
}

.setia-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 20px;
    height: 20px;
}

.setia-icon svg {
    width: 14px;
    height: 14px;
    fill: currentColor;
}

.setia-btn {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    padding: 6px 12px;
    border: 1px solid var(--setia-gray-300);
    background-color: white;
    color: var(--setia-text-dark);
    border-radius: var(--setia-border-radius);
    cursor: pointer;
    transition: all 0.2s ease-in-out;
    text-decoration: none;
    font-size: 13px;
}

.setia-btn:hover {
    background-color: var(--setia-gray-100);
    border-color: var(--setia-gray-400);
}

.setia-btn-primary {
    background-color: var(--setia-primary);
    border-color: var(--setia-primary);
    color: white;
}

.setia-btn-primary:hover {
    background-color: var(--setia-primary-dark);
    border-color: var(--setia-primary-dark);
}

.setia-btn-delete {
    color: var(--setia-error);
}

.setia-btn-delete:hover {
    background-color: rgba(244, 67, 54, 0.1);
    border-color: var(--setia-error);
}

.setia-btn-view {
    color: var(--setia-primary);
}

.setia-btn-view:hover {
    background-color: rgba(74, 107, 239, 0.1);
    border-color: var(--setia-primary);
}

.setia-btn-tooltip {
    position: relative;
}

.setia-btn-tooltip::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background-color: var(--setia-text-dark);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: all 0.2s;
    margin-bottom: 5px;
}

.setia-btn-tooltip:hover::after {
    opacity: 1;
    visibility: visible;
}

.setia-bulk-btn {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    padding: 8px 14px;
    border: 1px solid var(--setia-gray-300);
    background-color: white;
    color: var(--setia-text-dark);
    border-radius: var(--setia-border-radius);
    cursor: pointer;
    transition: all 0.2s;
    font-size: 13px;
}

.setia-bulk-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.setia-bulk-delete {
    color: var(--setia-error);
}

.setia-bulk-delete:not(:disabled):hover {
    background-color: rgba(244, 67, 54, 0.1);
    border-color: var(--setia-error);
}

.setia-select-all {
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 13px;
    color: var(--setia-text-medium);
    cursor: pointer;
}

.setia-select-all:hover {
    color: var(--setia-primary);
}

.setia-pagination {
    margin-top: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    background-color: var(--setia-gray-100);
    border-radius: var(--setia-border-radius);
}

.setia-pagination-info {
    color: var(--setia-text-medium);
    font-size: 14px;
}

.setia-pagination-links {
    display: flex;
    gap: 5px;
}

.setia-pagination-links a,
.setia-pagination-links span {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border-radius: var(--setia-border-radius);
    color: var(--setia-text-medium);
    background-color: white;
    border: 1px solid var(--setia-gray-300);
    text-decoration: none;
    transition: all 0.2s;
}

.setia-pagination-links a:hover {
    background-color: var(--setia-gray-200);
}

.setia-pagination-links .current {
    background-color: var(--setia-primary);
    color: white;
    border-color: var(--setia-primary);
}

.setia-no-history {
    padding: 40px;
    text-align: center;
    background-color: var(--setia-gray-100);
    border-radius: var(--setia-border-radius);
    color: var(--setia-text-medium);
}

.setia-no-history p {
    margin: 0;
    font-size: 16px;
}

.setia-no-history .dashicons {
    font-size: 48px;
    width: 48px;
    height: 48px;
    margin-bottom: 10px;
    color: var(--setia-gray-400);
}

/* Modal styles */
.setia-modal {
    position: fixed;
    z-index: 99999; /* افزایش z-index برای اطمینان از نمایش روی همه المان‌ها */
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
}

.setia-modal-content {
    background-color: white;
    margin: 5% auto;
    padding: 25px;
    border-radius: var(--setia-border-radius);
    box-shadow: var(--setia-box-shadow);
    width: 80%;
    max-width: 1000px;
    max-height: 80vh;
    overflow-y: auto;
    position: relative;
    animation: fadeIn 0.3s ease-in-out;
}

.setia-modal-close {
    position: absolute;
    top: 15px;
    right: 20px;
    color: var(--setia-text-light);
    font-size: 24px;
    cursor: pointer;
    transition: all 0.2s;
}

.setia-modal-close:hover {
    color: var(--setia-error);
}

#setia-modal-title {
    color: var(--setia-primary);
    font-size: 24px;
    margin-top: 0;
    margin-bottom: 20px;
    border-bottom: 1px solid var(--setia-gray-300);
    padding-bottom: 15px;
}

#setia-modal-content {
    background-color: var(--setia-gray-100);
    padding: 20px;
    border-radius: var(--setia-border-radius);
    margin-bottom: 20px;
    max-height: 300px;
    overflow-y: auto;
    line-height: 1.6;
}

.setia-modal-section {
    margin-top: 25px;
    background-color: white;
    border: 1px solid var(--setia-gray-300);
    border-radius: var(--setia-border-radius);
    overflow: hidden;
}

.setia-modal-section-header {
    background-color: var(--setia-gray-100);
    padding: 10px 15px;
    border-bottom: 1px solid var(--setia-gray-300);
    display: flex;
    align-items: center;
    gap: 10px;
}

.setia-modal-section-header h3 {
    margin: 0;
    font-size: 16px;
    color: var(--setia-text-dark);
}

.setia-modal-section-content {
    padding: 15px;
}

.setia-modal-image-content img {
    max-width: 100%;
    max-height: 300px;
    display: block;
    margin: 0 auto;
    border-radius: var(--setia-border-radius);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.setia-fade-in {
    animation: fadeIn 0.3s ease-in-out;
}

.setia-slide-in {
    animation: slideIn 0.5s ease-out;
}

@keyframes slideIn {
    from { 
        opacity: 0;
        transform: translateY(20px);
    }
    to { 
        opacity: 1;
        transform: translateY(0);
    }
}
