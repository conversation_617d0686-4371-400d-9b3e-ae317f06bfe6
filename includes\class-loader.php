<?php
class SETIA_Loader {
    private static $instance = null;
    private $classes = array();
    
    private function __construct() {
        $this->register_classes();
    }
    
    public static function get_instance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    private function register_classes() {
        // مسیر اصلی پلاگین
        $plugin_dir = plugin_dir_path(dirname(__FILE__));
        
        // ثبت کلاس‌های اصلی
        $this->classes = array(
            'SETIA_Logger' => $plugin_dir . 'includes/class-setia-logger.php'
            // کلاس زمانبندی حذف شده است
        );
        
        // اضافه کردن اتولودر
        spl_autoload_register(array($this, 'autoload'));
    }
    
    public function autoload($class_name) {
        // بررسی وجود کلاس در لیست ثبت شده
        if (isset($this->classes[$class_name])) {
            $file = $this->classes[$class_name];
            if (file_exists($file)) {
                require_once $file;
            }
        }
    }
    
    public function init() {
        // اطمینان از لود شدن کلاس‌های مورد نیاز
        if (!class_exists('SETIA_Logger')) {
            require_once $this->classes['SETIA_Logger'];
        }
        
        // کلاس زمانبندی حذف شده است
        
        return true;
    }
} 