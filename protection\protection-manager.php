<?php
/**
 * SETIA Protection Manager
 * Coordinates all protection systems
 */

if (!defined('ABSPATH')) {
    exit;
}

// Load protection classes
require_once __DIR__ . '/php-obfuscator.php';
require_once __DIR__ . '/js-obfuscator.php';
require_once __DIR__ . '/license-system.php';
require_once __DIR__ . '/anti-tampering.php';

class SETIA_Protection_Manager {
    
    private $php_obfuscator;
    private $js_obfuscator;
    private $license_system;
    private $anti_tampering;
    private $plugin_dir;
    
    public function __construct($plugin_dir) {
        $this->plugin_dir = $plugin_dir;
        $this->init_protection_systems();
    }
    
    /**
     * Initialize all protection systems
     */
    private function init_protection_systems() {
        $this->license_system = new SETIA_License_System();
        $this->anti_tampering = new SETIA_Anti_Tampering();
        $this->php_obfuscator = new SETIA_PHP_Obfuscator($this->get_license_key());
        $this->js_obfuscator = new SETIA_JS_Obfuscator();
    }
    
    /**
     * Get license key from system
     */
    private function get_license_key() {
        $license_data = $this->license_system->get_license_data();
        return isset($license_data['license_key']) ? $license_data['license_key'] : '';
    }
    
    /**
     * Apply all protection measures
     */
    public function apply_full_protection() {
        $results = array(
            'php_obfuscation' => false,
            'js_obfuscation' => false,
            'integrity_hashes' => false,
            'license_check' => false,
            'errors' => array()
        );
        
        try {
            // Check license first
            if (!$this->license_system->is_licensed()) {
                $results['errors'][] = 'Valid license required for protection';
                return $results;
            }
            $results['license_check'] = true;
            
            // Apply PHP obfuscation
            $php_results = $this->php_obfuscator->obfuscate_all_files($this->plugin_dir);
            $results['php_obfuscation'] = !in_array(false, $php_results);
            
            if (!$results['php_obfuscation']) {
                $results['errors'][] = 'PHP obfuscation failed for some files';
            }
            
            // Apply JavaScript obfuscation
            $js_results = $this->js_obfuscator->obfuscate_all_js_files($this->plugin_dir);
            $results['js_obfuscation'] = !in_array(false, $js_results);
            
            if (!$results['js_obfuscation']) {
                $results['errors'][] = 'JavaScript obfuscation failed for some files';
            }
            
            // Generate integrity hashes
            $hashes = $this->anti_tampering->generate_integrity_hashes($this->plugin_dir);
            $results['integrity_hashes'] = !empty($hashes);
            
            if (!$results['integrity_hashes']) {
                $results['errors'][] = 'Failed to generate integrity hashes';
            }
            
            // Add code signatures
            $this->add_code_signatures();
            
        } catch (Exception $e) {
            $results['errors'][] = 'Protection failed: ' . $e->getMessage();
        }
        
        return $results;
    }
    
    /**
     * Remove all protection (for development)
     */
    public function remove_protection() {
        $results = array(
            'php_restored' => false,
            'js_restored' => false,
            'errors' => array()
        );
        
        try {
            // Restore PHP files
            $this->php_obfuscator->restore_files($this->plugin_dir);
            $results['php_restored'] = true;
            
            // Restore JavaScript files
            $this->js_obfuscator->restore_js_files($this->plugin_dir);
            $results['js_restored'] = true;
            
        } catch (Exception $e) {
            $results['errors'][] = 'Restoration failed: ' . $e->getMessage();
        }
        
        return $results;
    }
    
    /**
     * Create production-ready distribution
     */
    public function create_distribution_package($output_dir) {
        if (!is_dir($output_dir)) {
            wp_mkdir_p($output_dir);
        }
        
        $dist_dir = $output_dir . '/setia-protected';
        
        // Copy plugin files
        $this->copy_directory($this->plugin_dir, $dist_dir);
        
        // Apply protection to distribution
        $protection_manager = new self($dist_dir);
        $results = $protection_manager->apply_full_protection();
        
        // Remove development files
        $this->remove_development_files($dist_dir);
        
        // Create ZIP package
        $zip_file = $output_dir . '/setia-content-generator-protected.zip';
        $this->create_zip_package($dist_dir, $zip_file);
        
        return array(
            'distribution_dir' => $dist_dir,
            'zip_file' => $zip_file,
            'protection_results' => $results
        );
    }
    
    /**
     * Copy directory recursively
     */
    private function copy_directory($src, $dst) {
        if (!is_dir($src)) {
            return false;
        }
        
        if (!is_dir($dst)) {
            wp_mkdir_p($dst);
        }
        
        $iterator = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator($src, RecursiveDirectoryIterator::SKIP_DOTS),
            RecursiveIteratorIterator::SELF_FIRST
        );
        
        foreach ($iterator as $item) {
            $target = $dst . DIRECTORY_SEPARATOR . $iterator->getSubPathName();
            
            if ($item->isDir()) {
                wp_mkdir_p($target);
            } else {
                copy($item, $target);
            }
        }
        
        return true;
    }
    
    /**
     * Remove development files from distribution
     */
    private function remove_development_files($dist_dir) {
        $dev_files = array(
            '.git',
            '.gitignore',
            'README.md',
            'TROUBLESHOOTING-*.md',
            'debug-*.php',
            'test-*.php',
            'layout-test.html',
            'error_log',
            'tmp',
            '*.backup'
        );
        
        foreach ($dev_files as $pattern) {
            $files = glob($dist_dir . '/' . $pattern);
            foreach ($files as $file) {
                if (is_dir($file)) {
                    $this->remove_directory($file);
                } else {
                    unlink($file);
                }
            }
        }
    }
    
    /**
     * Remove directory recursively
     */
    private function remove_directory($dir) {
        if (!is_dir($dir)) {
            return false;
        }
        
        $files = array_diff(scandir($dir), array('.', '..'));
        
        foreach ($files as $file) {
            $path = $dir . DIRECTORY_SEPARATOR . $file;
            if (is_dir($path)) {
                $this->remove_directory($path);
            } else {
                unlink($path);
            }
        }
        
        return rmdir($dir);
    }
    
    /**
     * Create ZIP package
     */
    private function create_zip_package($source_dir, $zip_file) {
        if (!class_exists('ZipArchive')) {
            return false;
        }
        
        $zip = new ZipArchive();
        
        if ($zip->open($zip_file, ZipArchive::CREATE | ZipArchive::OVERWRITE) !== TRUE) {
            return false;
        }
        
        $iterator = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator($source_dir, RecursiveDirectoryIterator::SKIP_DOTS),
            RecursiveIteratorIterator::SELF_FIRST
        );
        
        foreach ($iterator as $file) {
            $file_path = $file->getRealPath();
            $relative_path = substr($file_path, strlen($source_dir) + 1);
            
            if ($file->isDir()) {
                $zip->addEmptyDir($relative_path);
            } else {
                $zip->addFile($file_path, $relative_path);
            }
        }
        
        $zip->close();
        return true;
    }
    
    /**
     * Add code signatures to protected files
     */
    private function add_code_signatures() {
        $protected_files = [
            'setia-content-generator.php',
            'ajax-handlers.php',
            'gemini-ai-plugin.php'
        ];
        
        foreach ($protected_files as $file) {
            $file_path = $this->plugin_dir . '/' . $file;
            if (file_exists($file_path)) {
                $this->anti_tampering->add_code_signature($file_path);
            }
        }
    }
    
    /**
     * Verify all protection systems are working
     */
    public function verify_protection() {
        $status = array(
            'license_system' => $this->license_system->is_licensed(),
            'anti_tampering' => !$this->anti_tampering->is_security_disabled(),
            'integrity_status' => $this->anti_tampering->get_integrity_status(),
            'overall_status' => 'protected'
        );
        
        if (!$status['license_system'] || !$status['anti_tampering']) {
            $status['overall_status'] = 'compromised';
        }
        
        return $status;
    }
    
    /**
     * Get protection status for admin display
     */
    public function get_protection_status_display() {
        $status = $this->verify_protection();
        
        $display = array(
            'license' => array(
                'status' => $status['license_system'] ? 'active' : 'inactive',
                'color' => $status['license_system'] ? 'green' : 'red',
                'message' => $status['license_system'] ? 'License Active' : 'License Required'
            ),
            'integrity' => array(
                'status' => $status['anti_tampering'] ? 'protected' : 'compromised',
                'color' => $status['anti_tampering'] ? 'green' : 'red',
                'message' => $status['anti_tampering'] ? 'Files Protected' : 'Security Alert'
            ),
            'overall' => array(
                'status' => $status['overall_status'],
                'color' => $status['overall_status'] === 'protected' ? 'green' : 'red',
                'message' => $status['overall_status'] === 'protected' ? 'Fully Protected' : 'Protection Compromised'
            )
        );
        
        return $display;
    }
    
    /**
     * Emergency disable all functionality
     */
    public function emergency_disable() {
        // Disable all AJAX handlers
        remove_all_actions('wp_ajax_setia_generate_content');
        remove_all_actions('wp_ajax_setia_publish_content');
        remove_all_actions('wp_ajax_setia_test_connection');
        
        // Set emergency flag
        update_option('setia_emergency_disabled', true);
        
        // Log incident
        error_log('SETIA EMERGENCY: Plugin functionality disabled due to security breach');
    }
    
    /**
     * Check if emergency disabled
     */
    public function is_emergency_disabled() {
        return get_option('setia_emergency_disabled', false);
    }
}
