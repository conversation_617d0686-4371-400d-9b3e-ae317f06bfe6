/* Custom styles to override default font for specific elements */

/* Override IRANSans font for labels with for="keywords" */
label[for="keywords"] {
    font-family: <PERSON><PERSON><PERSON>, <PERSON><PERSON>, sans-serif !important;
}

/* Fix for dashicons in the setia-scheduler page */
body.wp-admin .setia-scheduler-wrap .dashicons,
body.wp-admin .setia-scheduler-wrap .dashicons-before:before,
body.wp-admin .setia-scheduler-wrap [class^="dashicons-"]:before {
    font-family: dashicons !important;
    display: inline-block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

/* Additional fixes for form field icons */
.setia-form-group label:before {
    font-family: dashicons !important;
    display: inline-block !important;
    visibility: visible !important;
    opacity: 1 !important;
    position: absolute !important;
    right: 0 !important;
    top: 1px !important;
    font-size: 20px !important;
    color: #0073aa !important;
}

/* Enhanced scheduler form styles */
#setia-schedule-modal {
    backdrop-filter: blur(5px);
}

.setia-modal-content {
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2);
    transform-origin: top center;
    max-width: 900px;
    width: 90%;
    margin: 0 auto;
}

/* Improved form layout with grid - all fields full width */
#setia-schedule-form {
    display: block !important;
    width: 100% !important;
    padding: 10px;
}

/* Full width form groups - now all form groups are full width by default */
.setia-form-group {
    width: 100% !important;
    max-width: 100% !important;
    position: relative;
    margin-bottom: 15px !important;
    padding: 0;
    transition: all 0.3s ease;
}

.setia-form-group:hover {
    transform: translateY(-2px);
}

/* Label styling with proper icon display */
.setia-form-group label {
    display: block;
    margin-bottom: 6px;
    font-weight: 600;
    padding-right: 35px;
    position: relative;
    color: #333;
}

/* اجبار برای تمام عرض بودن */
#title, #topic, #keywords, #category, #tone, #length, #frequency, #status,
input[name="title"], input[name="topic"], input[name="keywords"], select[name="category"], 
select[name="tone"], select[name="length"], select[name="frequency"], select[name="status"] {
    width: 100% !important;
    max-width: 100% !important;
    padding-right: 40px !important;
    box-sizing: border-box !important;
    display: block !important;
}

/* Specific icon assignments with !important to ensure they display */
.setia-form-group.topic label:before { 
    content: "\f109" !important; 
    display: inline-block !important;
    visibility: visible !important;
    opacity: 1 !important;
}
.setia-form-group.date label:before { 
    content: "\f508" !important;
    display: inline-block !important;
    visibility: visible !important;
    opacity: 1 !important;
}
.setia-form-group.time label:before { 
    content: "\f469" !important;
    display: inline-block !important;
    visibility: visible !important;
    opacity: 1 !important;
}
.setia-form-group.category label:before { 
    content: "\f318" !important;
    display: inline-block !important;
    visibility: visible !important;
    opacity: 1 !important;
}
.setia-form-group.status label:before {
    content: "\f173";
    font-family: dashicons;
    position: absolute;
    right: 0;
    top: 0;
    color: var(--primary);
    font-size: 20px;
}
.setia-form-group.description label:before {
    content: "\f547";
    font-family: dashicons;
    position: absolute;
    right: 0;
    top: 0;
    color: var(--primary);
    font-size: 20px;
}

/* آیکون برای لحن محتوا */
.setia-form-group.tone label:before {
    content: "\f473";
    font-family: dashicons;
    position: absolute;
    right: 0;
    top: 0;
    color: var(--primary);
    font-size: 20px;
}

/* آیکون برای طول محتوا */
.setia-form-group.length label:before {
    content: "\f478";
    font-family: dashicons;
    position: absolute;
    right: 0;
    top: 0;
    color: var(--primary);
    font-size: 20px;
}

/* Enhanced input styling */
.setia-form-group input[type="text"],
.setia-form-group input[type="number"],
.setia-form-group select,
.setia-form-group textarea {
    width: 100% !important;
    max-width: 100% !important;
    padding: 8px 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    box-shadow: inset 0 1px 2px rgba(0,0,0,0.07);
    transition: all 0.3s ease;
    background-color: #fff;
    margin: 0;
    line-height: 1.4;
    display: block !important;
    box-sizing: border-box !important;
}

.setia-form-group input[type="text"]:focus,
.setia-form-group input[type="number"]:focus,
.setia-form-group select:focus,
.setia-form-group textarea:focus {
    border-color: #0073aa;
    box-shadow: 0 0 0 1px #0073aa;
    outline: none;
}

/* Checkbox styling */
.setia-form-check {
    position: relative;
    display: flex;
    align-items: center;
    padding: 8px 12px;
    background-color: #f8f9fa;
    border-radius: 6px;
    margin-bottom: 5px;
    transition: all 0.3s ease;
    border: 1px solid #e2e4e7;
}

.setia-form-check:hover {
    background-color: #f0f0f1;
}

.setia-form-check input[type="checkbox"] {
    margin-left: 12px;
    cursor: pointer;
    width: 16px;
    height: 16px;
}

/* Help text styling */
.setia-form-group small {
    display: block;
    color: #666;
    font-size: 11px;
    margin-top: 3px;
    line-height: 1.3;
}

/* Button styling */
.setia-form-buttons {
    grid-column: 1 / -1;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 10px;
}

.setia-button {
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.2s ease;
    border: none;
}

.setia-button-primary {
    background-color: #0073aa;
    color: white;
}

.setia-button-primary:hover {
    background-color: #005d8c;
}

.setia-button-secondary {
    background-color: #f0f0f1;
    color: #ffffff;
}

.setia-button-secondary:hover {
    background-color: #ddd;
}

/* Time input styling */
.setia-time-group {
    display: flex;
    align-items: center;
    gap: 5px;
}

.setia-time-group select {
    width: calc(50% - 10px);
}

.setia-time-separator {
    font-weight: bold;
    font-size: 18px;
}

.setia-time-labels {
    display: flex;
    justify-content: space-between;
    margin-top: 5px;
    padding: 0 5px;
}

.setia-time-label {
    font-size: 12px;
    color: #666;
}

/* Repeat options styling */
.setia-repeat-options {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
    border: 1px solid #e0e6f7;
    margin-top: 5px;
}

.setia-repeat-toggle {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
}

.setia-repeat-toggle input[type="checkbox"] {
    margin-left: 10px;
    width: 18px;
    height: 18px;
}

#repeat_options {
    border-top: 1px dashed #e0e6f7;
    padding-top: 15px;
    margin-top: 10px;
}

.setia-repeat-type {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 10px;
}

.setia-repeat-type input[type="radio"] {
    position: absolute;
    opacity: 0;
}

.setia-repeat-type label {
    display: block;
    text-align: center;
    padding: 8px 5px;
    background-color: #f0f0f1;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
    border: 1px solid #ddd;
}

.setia-repeat-type input[type="radio"]:checked + label {
    background-color: #0073aa;
    color: white;
    border-color: #0073aa;
}

.setia-repeat-type label:hover {
    background-color: #e2e4e7;
}

/* Modal styling */
.setia-modal-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #e2e4e7;
    padding: 15px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.setia-modal-title {
    margin: 0;
    font-size: 18px;
    color: #1d2327;
}

.setia-modal-close {
    background: none;
    border: none;
    font-size: 20px;
    cursor: pointer;
    color: #666;
    width: 30px;
    height: 30px;
    position: relative;
}

.setia-modal-close:before,
.setia-modal-close:after {
    content: '';
    position: absolute;
    width: 20px;
    height: 2px;
    background-color: #666;
    top: 50%;
    left: 50%;
}

.setia-modal-close:before {
    transform: translate(-50%, -50%) rotate(45deg);
}

.setia-modal-close:after {
    transform: translate(-50%, -50%) rotate(-45deg);
}

.setia-modal-body {
    padding: 20px;
    max-height: 70vh;
    overflow-y: auto;
}

/* Mobile responsiveness */
@media (max-width: 768px) {
    /* Already single column layout now */
    
    .setia-repeat-type {
        grid-template-columns: 1fr;
    }
    
    .setia-modal-content {
        width: 95%;
        max-height: 90vh;
    }
}

/* Apply IRANSans font specifically to setia-scheduler-wrap */
.setia-scheduler-wrap,
.setia-scheduler-wrap * {
    font-family: 'IRANSans', Tahoma, Arial, sans-serif !important;
}

/* Preserve dashicons */
.setia-scheduler-wrap .dashicons,
.setia-scheduler-wrap .dashicons-before:before,
.setia-scheduler-wrap [class^="dashicons-"]:before {
    font-family: dashicons !important;
}

/* حذف هر گونه محدودیت عرض */
.setia-form, 
.setia-form-group, 
.setia-form * {
    max-width: none !important;
}

/* اطمینان از عدم استفاده از ستون‌بندی */
.setia-form-row, 
.setia-form-group, 
.setia-form {
    display: block !important;
    float: none !important;
    width: 100% !important;
}

/* فیکس مشکل مودال */
.setia-modal-content,
.setia-modal-body {
    width: 100% !important;
    max-width: 900px !important;
    margin: 0 auto !important;
    box-sizing: border-box !important;
}

/* استایل اضافی برای فیکس عرض کامل */
body .setia-scheduler-wrap {
    --wp-admin-theme-color: #0073aa !important;
}

body .setia-scheduler-wrap #setia-schedule-form {
    display: block !important;
    grid-template-columns: unset !important;
    -ms-grid-columns: unset !important;
    grid-template-rows: unset !important;
    -ms-grid-rows: unset !important;
    grid: unset !important;
    gap: 0 !important;
    grid-gap: 0 !important;
}

body .setia-scheduler-wrap #setia-schedule-form > * {
    grid-column: unset !important;
    -ms-grid-column: unset !important;
    grid-row: unset !important;
    -ms-grid-row: unset !important;
    width: 100% !important;
    max-width: 100% !important;
    min-width: 100% !important;
    margin-bottom: 15px !important;
}

/* -------------------------------------------------- */
/* فاصله گذاری آیکون و فیلدها */
.setia-input-container {
    display: flex !important;
    align-items: center !important;
    gap: 10px !important; /* فاصله بین آیکون و فیلد */
}

.setia-input-icon {
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
    flex-shrink: 0 !important;
}
