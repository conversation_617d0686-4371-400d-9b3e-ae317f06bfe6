<?php
/**
 * SETIA Cron Trigger
 * این فایل برای فراخوانی کرون وردپرس از طریق کرون سرور واقعی استفاده می‌شود
 */

// جلوگیری از خروجی قبل از header
ob_start();

// لاگ برای عیب‌یابی - شروع (فقط در صورت فعال بودن دیباگ)
if (defined('WP_DEBUG') && WP_DEBUG) {
    error_log("SETIA CRON DEBUG: شروع اجرای cron-trigger.php در " . date('Y-m-d H:i:s'));
}

// مسیر دقیق wp-load.php - این مسیر را با مسیر واقعی در سیستم خود جایگزین کنید
$wp_load_path = $_SERVER['DOCUMENT_ROOT'] . '/wp-load.php';

if (!file_exists($wp_load_path)) {
    // بررسی مسیرهای دیگر
    $possible_paths = array(
        dirname(dirname(dirname(__FILE__))) . '/wp-load.php',
        dirname(__FILE__, 4) . '/wp-load.php',
        dirname(dirname(dirname(dirname(__FILE__)))) . '/wp-load.php',
        dirname(dirname(__FILE__)) . '/wp-load.php',
        dirname(__FILE__) . '/../wp-load.php',
        dirname(__FILE__) . '/../../wp-load.php',
        dirname(__FILE__) . '/../../../wp-load.php',
        dirname(__FILE__) . '/../../../../wp-load.php',
        realpath($_SERVER['DOCUMENT_ROOT'] . '/../wp-load.php'),
    );
    
    foreach ($possible_paths as $path) {
        if (file_exists($path)) {
            $wp_load_path = $path;
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log("SETIA CRON DEBUG: wp-load.php در مسیر " . $path . " یافت شد");
            }
            break;
        }
    }
}

if (file_exists($wp_load_path)) {
    error_log("SETIA CRON DEBUG: بارگذاری wp-load.php از مسیر: " . $wp_load_path);
    
    try {
        require_once($wp_load_path);
        error_log("SETIA CRON: wp-load.php با موفقیت بارگذاری شد");
    } catch (Exception $e) {
        error_log("SETIA CRON ERROR: خطا در بارگذاری wp-load.php: " . $e->getMessage());
        die('خطا در بارگذاری wp-load.php: ' . $e->getMessage());
    }
} else {
    $error_message = 'خطا: فایل wp-load.php یافت نشد.';
    error_log("SETIA CRON ERROR: " . $error_message);
    die($error_message);
}

// بررسی امنیتی
$security_key = isset($_GET['key']) ? $_GET['key'] : '';
$valid_key = get_option('setia_cron_security_key', '');

// اگر کلید امنیتی تنظیم شده و کلید ارسالی نامعتبر است، خطا نمایش داده شود
if (!empty($valid_key) && $security_key !== $valid_key) {
    $error_message = 'دسترسی غیرمجاز: کلید امنیتی نامعتبر';
    error_log("SETIA CRON ERROR: " . $error_message);
    header('HTTP/1.0 403 Forbidden');
    die($error_message);
}

// تنظیم محدودیت زمانی اجرا
ignore_user_abort(true);
set_time_limit(300); // 5 دقیقه زمان اجرا

// لاگ برای عیب‌یابی
error_log('SETIA CRON: شروع اجرای کرون از طریق cron-trigger.php');

// اجرای مستقیم زمانبندی‌های SETIA
try {
    // بررسی وجود کلاس زمانبندی
    if (!class_exists('SETIA_Scheduler')) {
        $scheduler_path = plugin_dir_path(__FILE__) . 'includes/scheduler.php';
        error_log("SETIA CRON DEBUG: تلاش برای بارگذاری فایل scheduler.php از مسیر: " . $scheduler_path);
        
        if (file_exists($scheduler_path)) {
            require_once($scheduler_path);
            error_log("SETIA CRON DEBUG: فایل scheduler.php با موفقیت بارگذاری شد");
        } else {
            error_log("SETIA CRON ERROR: فایل scheduler.php یافت نشد");
        }
    }
    
    // بررسی وجود کلاس SETIA
    if (!class_exists('SETIA')) {
        $setia_path = plugin_dir_path(__FILE__) . 'setia-content-generator.php';
        error_log("SETIA CRON DEBUG: تلاش برای بارگذاری فایل setia-content-generator.php از مسیر: " . $setia_path);
        
        if (file_exists($setia_path)) {
            require_once($setia_path);
            error_log("SETIA CRON DEBUG: فایل setia-content-generator.php با موفقیت بارگذاری شد");
        } else {
            error_log("SETIA CRON ERROR: فایل setia-content-generator.php یافت نشد");
            
            // تلاش برای بارگذاری کلاس‌های مورد نیاز
            $generator_path = plugin_dir_path(__FILE__) . 'includes/class-content-generator.php';
            if (file_exists($generator_path)) {
                require_once($generator_path);
                
                class SETIA {
                    public $content_generator;
                    
                    public function __construct() {
                        $this->content_generator = new SETIA_Content_Generator();
                    }
                }
                
                error_log("SETIA CRON DEBUG: کلاس SETIA به صورت دستی ایجاد شد");
            } else {
                error_log("SETIA CRON ERROR: فایل class-content-generator.php یافت نشد");
            }
        }
    }
    
    // اجرای زمانبندی‌ها
    if (class_exists('SETIA_Scheduler') && class_exists('SETIA')) {
        $setia = new SETIA();
        $scheduler = new SETIA_Scheduler($setia->content_generator);
        
        // دریافت زمانبندی‌های فعال
        $schedules = get_option('setia_content_schedules', array());
        $now = current_time('timestamp');
        $executed = false;
        
        error_log('SETIA CRON: تعداد زمانبندی‌ها: ' . count($schedules));
        
        foreach ($schedules as $schedule_id => $schedule) {
            // بررسی فقط زمانبندی‌های فعال
            if ($schedule['status'] !== 'active') {
                error_log('SETIA CRON: زمانبندی ' . $schedule_id . ' غیرفعال است، رد شد');
                continue;
            }
            
            // تبدیل زمان آخرین اجرا به timestamp
            $last_run = !empty($schedule['last_run']) ? strtotime($schedule['last_run']) : 0;
            
            // محاسبه فاصله زمانی بر اساس تناوب
            $interval = 3600; // پیش‌فرض: هر ساعت (3600 ثانیه)
            
            switch ($schedule['frequency']) {
                case 'minutely':
                    $interval = 60; // هر دقیقه
                    break;
                case 'every5minutes':
                    $interval = 300; // هر 5 دقیقه
                    break;
                case 'every15minutes':
                    $interval = 900; // هر 15 دقیقه
                    break;
                case 'every30minutes':
                    $interval = 1800; // هر 30 دقیقه
                    break;
                case 'hourly':
                    $interval = 3600; // هر ساعت
                    break;
                case 'twicedaily':
                    $interval = 43200; // دو بار در روز (هر 12 ساعت)
                    break;
                case 'daily':
                    $interval = 86400; // روزانه
                    break;
                case 'weekly':
                    $interval = 604800; // هفتگی
                    break;
                case 'monthly':
                    $interval = 2592000; // ماهانه (30 روز)
                    break;
            }
            
            error_log('SETIA CRON: بررسی زمانبندی ' . $schedule_id . ' - آخرین اجرا: ' . date('Y-m-d H:i:s', $last_run) . ', اکنون: ' . date('Y-m-d H:i:s', $now) . ', فاصله: ' . ($now - $last_run) . ' ثانیه، حداقل فاصله لازم: ' . $interval . ' ثانیه');
            
            // بررسی آیا زمان اجرای زمانبندی رسیده است
            if ($now - $last_run >= $interval) {
                error_log("SETIA CRON: اجرای زمانبندی با شناسه {$schedule_id}");
                
                // اجرای زمانبندی
                $scheduler->generate_scheduled_content($schedule_id);
                $executed = true;
                
                error_log("SETIA CRON: زمانبندی با شناسه {$schedule_id} با موفقیت اجرا شد");
            } else {
                error_log("SETIA CRON: زمان اجرای زمانبندی {$schedule_id} هنوز نرسیده است");
            }
        }
        
        if ($executed) {
            echo 'SETIA CRON: حداقل یک زمانبندی با موفقیت اجرا شد';
        } else {
            echo 'SETIA CRON: هیچ زمانبندی برای اجرا یافت نشد';
        }
    } else {
        $error_message = 'خطا: کلاس‌های مورد نیاز یافت نشدند';
        error_log("SETIA CRON ERROR: " . $error_message);
        echo $error_message;
    }
} catch (Exception $e) {
    $error_message = 'خطا در اجرای زمانبندی‌ها: ' . $e->getMessage();
    error_log("SETIA CRON ERROR: " . $error_message);
    echo $error_message;
}

// اجرای کرون وردپرس برای سایر افزونه‌ها
if (function_exists('wp_cron')) {
    do_action('wp_cron');
    error_log('SETIA CRON: کرون وردپرس با موفقیت اجرا شد');
} else {
    error_log('SETIA CRON WARNING: تابع wp_cron یافت نشد');
}

// لاگ موفقیت
error_log('SETIA CRON: کرون با موفقیت اجرا شد');
echo 'SETIA CRON: کرون با موفقیت اجرا شد'; 