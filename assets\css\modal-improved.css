/**
 * استایل‌های بهبود یافته برای مودال زمانبندی
 */

/* بهبود کلی مودال */
.setia-modal {
    z-index: 9999999 !important;
}

.setia-modal-content {
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
}

.setia-fullwidth-modal {
    width: 90%;
    max-width: 1200px;
    margin: 0 auto;
}

.setia-modal-header {
    background: linear-gradient(135deg, #0d47a1 0%, #1976d2 100%);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px 12px 0 0;
    padding: 20px 25px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    position: relative;
    z-index: 1;
}

.setia-modal-header:after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, rgba(255,255,255,0.1), rgba(255,255,255,0.3), rgba(255,255,255,0.1));
}

.setia-modal-title {
    font-size: 18px;
    font-weight: 700;
    color: white;
}

.setia-modal-close {
    color: white;
    background: rgba(255, 255, 255, 0.2);
    border: none;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    font-size: 18px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.setia-modal-close:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: rotate(90deg);
}

.setia-modal-body {
    padding: 25px;
    max-height: 80vh;
    overflow-y: auto;
}

/* بهبود فرم */
.setia-form-group {
    margin-bottom: 25px;
    position: relative;
    opacity: 0;
    animation: fadeIn 0.3s ease forwards;
}

.setia-form-group:nth-child(1) { animation-delay: 0.05s; }
.setia-form-group:nth-child(2) { animation-delay: 0.1s; }
.setia-form-group:nth-child(3) { animation-delay: 0.15s; }
.setia-form-group:nth-child(4) { animation-delay: 0.2s; }
.setia-form-group:nth-child(5) { animation-delay: 0.25s; }
.setia-form-group:nth-child(6) { animation-delay: 0.3s; }
.setia-form-group:nth-child(7) { animation-delay: 0.35s; }
.setia-form-group:nth-child(8) { animation-delay: 0.4s; }
.setia-form-group:nth-child(9) { animation-delay: 0.45s; }
.setia-form-group:nth-child(10) { animation-delay: 0.5s; }

.setia-form-group label {
    display: block;
    margin-bottom: 12px;
    font-weight: 600;
    color: #333;
    position: relative;
    padding-right: 35px;
    line-height: 20px;
}

/* استایل مستقیم برای فیلدهای فرم با آی‌دی */
#title, #topic, #keywords, #category, #tone, #length, #frequency, #status,
input[name="title"], input[name="topic"], input[name="keywords"], select[name="category"], 
select[name="tone"], select[name="length"], select[name="frequency"], select[name="status"] {
    width: 100% !important;
    max-width: 100% !important;
    padding-right: 40px !important;
    box-sizing: border-box !important;
    display: block !important;
}

/* همترازی آیکون‌ها */
.setia-form-group.topic label:before,
.setia-form-group.date label:before,
.setia-form-group.time label:before,
.setia-form-group.category label:before,
.setia-form-group.content label:before,
.setia-form-group.status label:before,
.setia-form-group.description label:before,
.setia-form-group.tone label:before,
.setia-form-group.length label:before {
    position: absolute !important;
    right: 0 !important;
    top: 50% !important;
    transform: translateY(-50%) !important;
    font-family: dashicons !important;
    font-size: 20px !important;
    color: #1967d2 !important;
    line-height: 1 !important;
    width: 20px !important;
    height: 20px !important;
    text-align: center !important;
}

/* آیکون عنوان زمانبندی */
.setia-form-group.topic label:before {
    content: "\f109";
}

/* آیکون تاریخ اجرا */
.setia-form-group.date label:before {
    content: "\f508";
}

/* آیکون زمان اجرا */
.setia-form-group.time label:before {
    content: "\f469";
}

/* آیکون کلمات کلیدی */
.setia-form-group.description label[for="keywords"]:before {
    content: "\f534";
}

/* آیکون دستورالعمل‌های اضافی */
.setia-form-group.description label[for="instructions"]:before {
    content: "\f348";
}

/* آیکون تکرار زمانبندی */
.setia-form-group.description label[for="repeat_status"]:before {
    content: "\f321";
}

/* آیکون لحن محتوا */
.setia-form-group.tone label:before {
    content: "\f482";
}

/* آیکون طول محتوا */
.setia-form-group.length label:before {
    content: "\f478";
}

/* آیکون دسته‌بندی */
.setia-form-group.category label:before {
    content: "\f318";
}

/* آیکون وضعیت انتشار */
.setia-form-group.status label:before {
    content: "\f173";
}

/* چیدمان تک ستونه با عرض کامل */
.setia-form-row {
    display: block !important;
    width: 100% !important;
    margin-bottom: 15px !important;
}

.setia-form-row .setia-form-group {
    width: 100% !important;
    max-width: 100% !important;
    margin-bottom: 15px !important;
}

.setia-form-row-columns {
    background-color: #fbfbff;
    padding: 20px;
    border-radius: 10px;
    border: 1px solid rgba(25, 103, 210, 0.1);
}

/* بهبود فیلدهای ورودی */
.setia-form-group input[type="text"],
.setia-form-group textarea,
.setia-form-group select {
    width: 100% !important;
    max-width: 100% !important;
    padding: 12px 15px;
    border: 1px solid #ddd;
    border-radius: 8px;
    font-size: 14px;
    transition: all 0.3s ease;
    background-color: #fff;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.03);
    display: block !important;
    box-sizing: border-box !important;
    float: none !important;
    margin-left: 0 !important;
    margin-right: 0 !important;
}

.setia-form-group input[type="text"]:focus,
.setia-form-group textarea:focus,
.setia-form-group select:focus {
    border-color: #1967d2;
    box-shadow: 0 0 0 3px rgba(25, 103, 210, 0.15);
    outline: none;
}

.setia-form-group small {
    display: block;
    margin-top: 8px;
    color: #666;
    font-size: 12px;
    line-height: 1.5;
}

/* فوکوس و هاور برای فیلدها */
.setia-form-group.setia-focused label {
    color: #1967d2;
}

.setia-form-group.setia-focused label:before {
    color: #1967d2 !important;
    transform: translateY(-50%) scale(1.1) !important;
}

.setia-form-group:hover label:before {
    color: #4361ee !important;
}

/* استایل عمومی برای چک‌باکس‌ها */
.setia-form-check {
    display: flex;
    align-items: center;
    position: relative;
    padding: 15px 15px 15px 20px;
    margin-bottom: 20px;
    cursor: pointer;
    font-weight: 600;
    color: #333;
    line-height: 22px;
    background-color: #f8f9ff;
    border-radius: 10px;
    border: 1px solid rgba(25, 103, 210, 0.1);
    transition: all 0.3s ease;
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.03);
}

.setia-form-check:hover {
    background-color: #eef2ff;
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.08);
    border-color: rgba(25, 103, 210, 0.2);
}

/* استایل چک‌مارک */
.checkmark {
    position: relative;
    height: 22px;
    width: 22px;
    background-color: #fff;
    border: 2px solid #ddd;
    border-radius: 4px;
    transition: all 0.2s ease;
    margin-left: 15px;
    flex-shrink: 0;
}

.setia-form-check:hover input ~ .checkmark {
    background-color: #f0f3ff;
    border-color: #1976d2;
}

/* استایل چک‌مارک وقتی انتخاب شده */
.setia-form-check input:checked ~ .checkmark {
    background-color: rgba(25, 118, 210, 0.05);
    border-color: #1976d2;
}

.setia-form-check .checkmark:after {
    content: "";
    position: absolute;
    display: none;
    left: 7px;
    top: 3px;
    width: 5px;
    height: 10px;
    border: solid #1976d2;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
}

.setia-form-check input:checked ~ .checkmark:after {
    display: block;
}

/* استایل برای متن چک‌باکس */
.checkbox-text {
    font-size: 14px;
    transition: all 0.3s ease;
}

.setia-form-check input:checked ~ .checkbox-text {
    color: #1976d2;
    font-weight: 700;
}

/* استایل برای چک‌باکس انتخاب شده */
.setia-form-check input:checked ~ .checkbox-text {
    color: #1967d2;
    font-weight: 700;
}

/* کانتینر چک‌باکس‌ها */
.setia-checkbox-container {
    margin-bottom: 20px;
}

/* بهبود تنظیمات تصویر */
#setia-image-options-container {
    background-color: #f8f9ff;
    border-radius: 12px;
    padding: 25px;
    margin: 15px 0 25px;
    border: 1px solid rgba(25, 103, 210, 0.15);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

#setia-image-options-container .setia-form-row {
    margin-bottom: 20px;
    padding-bottom: 20px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

#setia-image-options-container .setia-form-row.no-border {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}

#setia-image-options-container label {
    font-weight: 600;
    display: block;
    margin-bottom: 12px;
}

/* بهبود بخش انتخاب نسبت ابعاد */
.setia-aspect-ratio-options {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
    margin-top: 15px;
    margin-bottom: 10px;
    direction: ltr;
}

.setia-aspect-ratio-option {
    position: relative;
    width: calc(20% - 10px);
    background-color: #fff;
    border: 2px solid #ddd;
    border-radius: 8px;
    transition: all 0.3s ease;
    cursor: pointer;
    overflow: hidden;
    padding: 8px;
    text-align: center;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.setia-aspect-ratio-option:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
    border-color: #1967d2;
}

.setia-aspect-ratio-option.selected {
    border-color: #1967d2;
    background-color: rgba(25, 103, 210, 0.05);
    box-shadow: 0 5px 15px rgba(25, 103, 210, 0.15);
}

.setia-aspect-ratio-option input[type="radio"] {
    position: absolute;
    opacity: 0;
}

.aspect-preview {
    height: 60px;
    background-color: #eef2ff;
    border-radius: 6px;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    border: 1px dashed rgba(25, 103, 210, 0.3);
}

/* نسبت‌های مختلف */
.setia-aspect-ratio-option[data-ratio="1:1"] .aspect-preview {
    width: 100%;
    position: relative;
    padding-bottom: 100%;
    height: 0;
}

.setia-aspect-ratio-option[data-ratio="16:9"] .aspect-preview {
    width: 100%;
    position: relative;
    padding-bottom: 56.25%;
    height: 0;
}

.setia-aspect-ratio-option[data-ratio="9:16"] .aspect-preview {
    width: 100%;
    position: relative;
    padding-bottom: 177.78%;
    height: 0;
}

.setia-aspect-ratio-option[data-ratio="4:3"] .aspect-preview {
    width: 100%;
    position: relative;
    padding-bottom: 75%;
    height: 0;
}

.setia-aspect-ratio-option[data-ratio="3:4"] .aspect-preview {
    width: 100%;
    position: relative;
    padding-bottom: 133.33%;
    height: 0;
}

.aspect-label {
    font-size: 12px;
    font-weight: 600;
    color: #333;
    direction: rtl;
}

.setia-aspect-ratio-option.selected .aspect-label {
    color: #1967d2;
}

/* حالت‌های موبایل */
@media (max-width: 991px) {
    .setia-aspect-ratio-option {
        width: calc(25% - 10px);
    }
}

@media (max-width: 767px) {
    .setia-aspect-ratio-option {
        width: calc(33.33% - 10px);
    }
}

@media (max-width: 480px) {
    .setia-aspect-ratio-option {
        width: calc(50% - 6px);
    }
}

/* بهبود دکمه‌ها */
.setia-form-buttons {
    display: flex;
    justify-content: flex-start;
    gap: 15px;
    margin-top: 30px;
    border-top: 1px solid #eee;
    padding-top: 25px;
}

.setia-button {
    padding: 12px 25px;
    font-size: 14px;
    font-weight: 600;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    border: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.setia-button:before {
    font-family: dashicons;
    margin-left: 8px;
    font-size: 16px;
}

.setia-button-primary {
    background: linear-gradient(135deg, #1967d2 0%, #4361ee 100%);
    color: white;
    box-shadow: 0 4px 10px rgba(25, 103, 210, 0.2);
}

.setia-button-primary:before {
    content: "\f147";
}

.setia-button-primary:hover {
    background: linear-gradient(135deg, #1456b3 0%, #3850d5 100%);
    box-shadow: 0 5px 15px rgba(25, 103, 210, 0.3);
    transform: translateY(-2px);
}

.setia-button-secondary {
    background-color: #f5f5f5;
    color: #333;
    border: 1px solid #ddd;
}

.setia-button-secondary:before {
    content: "\f335";
}

.setia-button-secondary:hover {
    background-color: #e9e9e9;
    transform: translateY(-2px);
}

/* بهبود گزینه‌های تکرار */
.setia-repeat-options {
    margin-top: 10px;
}

.setia-repeat-toggle {
    margin-bottom: 15px;
}

.repeat-option-card {
    background-color: #fff;
    border: 2px solid #ddd;
    border-radius: 8px;
    margin-bottom: 10px;
    transition: all 0.3s ease;
    overflow: hidden;
}

.repeat-option-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    border-color: #1967d2;
}

.repeat-option-card input[type="radio"] {
    position: absolute;
    opacity: 0;
}

.repeat-option-card label {
    display: flex;
    align-items: center;
    padding: 15px;
    cursor: pointer;
    margin: 0;
}

.repeat-option-card input[type="radio"]:checked + label {
    background-color: rgba(25, 103, 210, 0.05);
    border-color: #1967d2;
}

.repeat-icon {
    margin-left: 12px;
    font-size: 20px;
    color: #666;
}

.repeat-option-card input[type="radio"]:checked + label .repeat-icon,
.repeat-option-card input[type="radio"]:checked + label .repeat-text {
    color: #1967d2;
}

.repeat-text {
    font-weight: 600;
    color: #333;
}

/* انیمیشن‌ها */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.2); }
    100% { transform: scale(1); }
}

.pulse-animation {
    animation: pulse 0.5s;
}

.setia-form-animation {
    animation: fadeIn 0.3s ease forwards;
}

/* بهبود زمان */
.setia-time-group {
    display: flex;
    align-items: center;
    gap: 15px;
    background-color: #f0f4ff;
    padding: 18px;
    border-radius: 10px;
    border: 1px solid rgba(25, 103, 210, 0.1);
    margin-bottom: 5px;
    transition: all 0.3s ease;
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.03);
}

.setia-time-group:hover {
    background-color: #e3f2fd;
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.08);
}

.setia-time-group select {
    flex: 1;
    text-align: center;
    font-weight: 600;
    padding: 12px;
    background-color: white;
    border: 1px solid #e0e6f7;
    min-width: 100px;
    border-radius: 8px;
    font-size: 16px;
    color: #1976d2;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    appearance: none;
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="12" height="6" viewBox="0 0 12 6"><path fill="%231976d2" d="M0 0h12L6 6z"/></svg>');
    background-repeat: no-repeat;
    background-position: left 15px center;
    padding-left: 35px;
}

.setia-time-group select:focus {
    border-color: #1976d2;
    box-shadow: 0 0 0 3px rgba(25, 103, 210, 0.15);
    outline: none;
}

.setia-time-separator {
    font-size: 24px;
    font-weight: 700;
    color: #1976d2;
    margin: 0 5px;
    line-height: 1;
}

.setia-time-labels {
    display: flex;
    justify-content: space-between;
    margin-top: 10px;
    padding: 0 15px;
}

.setia-time-label {
    font-size: 12px;
    color: #666;
    text-align: center;
    min-width: 100px;
    flex: 1;
}

/* استایل ثابت برای چک‌باکس‌ها - برای مرورگرهایی که :has را پشتیبانی نمی‌کنند */
.setia-form-check#use_random-parent, 
label[for="use_random"].setia-form-check {
    border-right: 3px solid #9c27b0;
    background-color: #f3e5f5;
}

.setia-form-check#use_random-parent:hover, 
label[for="use_random"].setia-form-check:hover {
    background-color: #e1bee7;
}

.setia-form-check#use_random-parent .checkbox-text, 
label[for="use_random"].setia-form-check .checkbox-text {
    color: #7b1fa2;
}

.setia-form-check#generate_image-parent,
label[for="generate_image"].setia-form-check {
    border-right: 3px solid #1976d2;
    background-color: #e3f2fd;
}

.setia-form-check#generate_image-parent:hover,
label[for="generate_image"].setia-form-check:hover {
    background-color: #bbdefb;
}

.setia-form-check#generate_image-parent .checkbox-text,
label[for="generate_image"].setia-form-check .checkbox-text {
    color: #0d47a1;
}

.setia-form-check#seo-parent,
label[for="seo"].setia-form-check {
    border-right: 3px solid #4caf50;
    background-color: #e8f5e9;
}

.setia-form-check#seo-parent:hover,
label[for="seo"].setia-form-check:hover {
    background-color: #c8e6c9;
}

.setia-form-check#seo-parent .checkbox-text,
label[for="seo"].setia-form-check .checkbox-text {
    color: #2e7d32;
}

/* رنگ زمینه برای چک‌باکس کلی */
.setia-form-check input:checked ~ .checkmark {
    background-color: rgba(25, 118, 210, 0.1);
    border-color: #1976d2;
}

/* استایل خاص برای چک‌باکس تولید تصویر */
.setia-form-check:has(#generate_image),
.setia-form-check#generate_image-parent,
label[for="generate_image"].setia-form-check {
    border-right: 3px solid #1976d2;
    background-color: #e3f2fd;
}

.setia-form-check:has(#generate_image):hover,
.setia-form-check#generate_image-parent:hover,
label[for="generate_image"].setia-form-check:hover {
    background-color: #bbdefb;
}

.setia-form-check:has(#generate_image) .checkbox-text,
.setia-form-check#generate_image-parent .checkbox-text,
label[for="generate_image"].setia-form-check .checkbox-text {
    color: #0d47a1;
}

.setia-form-check:has(#generate_image) input:checked ~ .checkmark,
.setia-form-check#generate_image-parent input:checked ~ .checkmark,
label[for="generate_image"].setia-form-check input:checked ~ .checkmark {
    background-color: rgba(13, 71, 161, 0.1);
    border-color: #0d47a1;
}

.setia-form-check:has(#generate_image) input:checked ~ .checkmark:after,
.setia-form-check#generate_image-parent input:checked ~ .checkmark:after,
label[for="generate_image"].setia-form-check input:checked ~ .checkmark:after {
    border-color: #0d47a1;
}

/* استایل خاص برای چک‌باکس انتخاب تصادفی */
.setia-form-check:has(#use_random),
.setia-form-check#use_random-parent,
label[for="use_random"].setia-form-check {
    border-right: 3px solid #9c27b0;
    background-color: #f3e5f5;
}

.setia-form-check:has(#use_random):hover,
.setia-form-check#use_random-parent:hover,
label[for="use_random"].setia-form-check:hover {
    background-color: #e1bee7;
}

.setia-form-check:has(#use_random) .checkbox-text,
.setia-form-check#use_random-parent .checkbox-text,
label[for="use_random"].setia-form-check .checkbox-text {
    color: #7b1fa2;
}

.setia-form-check:has(#use_random) input:checked ~ .checkmark,
.setia-form-check#use_random-parent input:checked ~ .checkmark,
label[for="use_random"].setia-form-check input:checked ~ .checkmark {
    background-color: rgba(123, 31, 162, 0.1);
    border-color: #7b1fa2;
}

.setia-form-check:has(#use_random) input:checked ~ .checkmark:after,
.setia-form-check#use_random-parent input:checked ~ .checkmark:after,
label[for="use_random"].setia-form-check input:checked ~ .checkmark:after {
    border-color: #7b1fa2;
}

/* استایل خاص برای چک‌باکس سئو */
.setia-form-check:has(#seo),
.setia-form-check#seo-parent,
label[for="seo"].setia-form-check {
    border-right: 3px solid #4caf50;
    background-color: #e8f5e9;
}

.setia-form-check:has(#seo):hover,
.setia-form-check#seo-parent:hover,
label[for="seo"].setia-form-check:hover {
    background-color: #c8e6c9;
}

.setia-form-check:has(#seo) .checkbox-text,
.setia-form-check#seo-parent .checkbox-text,
label[for="seo"].setia-form-check .checkbox-text {
    color: #2e7d32;
}

.setia-form-check:has(#seo) input:checked ~ .checkmark,
.setia-form-check#seo-parent input:checked ~ .checkmark,
label[for="seo"].setia-form-check input:checked ~ .checkmark {
    background-color: rgba(46, 125, 50, 0.1);
    border-color: #2e7d32;
}

.setia-form-check:has(#seo) input:checked ~ .checkmark:after,
.setia-form-check#seo-parent input:checked ~ .checkmark:after,
label[for="seo"].setia-form-check input:checked ~ .checkmark:after {
    border-color: #2e7d32;
}

/* اضافه کردن استایل برای input داخل چک‌باکس‌ها */
.setia-form-check input {
    position: absolute;
    opacity: 0;
    cursor: pointer;
    height: 0;
    width: 0;
}

/* حذف گرید و فلکس برای اطمینان از عرض کامل */
#setia-schedule-form, .setia-form {
    display: block !important;
    width: 100% !important;
}

/* اجبار به تمام عرض بودن با بازنویسی هر گونه استایل دیگر */
.setia-form-group, 
.setia-form-group *, 
#setia-schedule-form,
#setia-schedule-form *,
.setia-form,
.setia-form * {
    box-sizing: border-box !important;
}

.setia-form-row-columns {
    display: block !important;
    width: 100% !important;
    max-width: 100% !important;
}

/* فاصله گذاری آیکون و فیلدها در مودال */
.setia-input-container {
    display: flex !important;
    align-items: center !important;
    gap: 10px !important;
}

.setia-input-icon {
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
    flex-shrink: 0 !important;
} 