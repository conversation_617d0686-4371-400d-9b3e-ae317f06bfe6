/**
 * فونت ایران سنس
 * 
 * @package SETIA
 */

@font-face {
    font-family: 'IRANSans';
    font-style: normal;
    font-weight: normal;
    src: url('../fonts/IRANSansWeb.eot');
    src: url('../fonts/IRANSansWeb.eot?#iefix') format('embedded-opentype'),
         url('../fonts/IRANSansWeb.woff2') format('woff2'),
         url('../fonts/IRANSansWeb.woff') format('woff'),
         url('../fonts/IRANSansWeb.ttf') format('truetype');
}

@font-face {
    font-family: 'IRANSans';
    font-style: normal;
    font-weight: bold;
    src: url('../fonts/IRANSansWeb_Bold.eot');
    src: url('../fonts/IRANSansWeb_Bold.eot?#iefix') format('embedded-opentype'),
         url('../fonts/IRANSansWeb_Bold.woff2') format('woff2'),
         url('../fonts/IRANSansWeb_Bold.woff') format('woff'),
         url('../fonts/IRANSansWeb_Bold.ttf') format('truetype');
}

/* فالبک برای زمانی که فونت ایران سنس در دسترس نباشد */
@font-face {
    font-family: 'IRANSans';
    font-style: normal;
    font-weight: normal;
    src: local('Tahoma');
}

@font-face {
    font-family: 'IRANSans';
    font-style: normal;
    font-weight: bold;
    src: local('Tahoma Bold');
}

@font-face {
    font-family: IRANSans;
    font-style: normal;
    font-weight: 500;
    src: url('../fonts/eot/IRANSansWeb_Medium.eot');
    src: url('../fonts/eot/IRANSansWeb_Medium.eot?#iefix') format('embedded-opentype'),
         url('../fonts/woff2/IRANSansWeb_Medium.woff2') format('woff2'),
         url('../fonts/woff/IRANSansWeb_Medium.woff') format('woff'),
         url('../fonts/ttf/IRANSansWeb_Medium.ttf') format('truetype');
}

@font-face {
    font-family: IRANSans;
    font-style: normal;
    font-weight: 300;
    src: url('../fonts/eot/IRANSansWeb_Light.eot');
    src: url('../fonts/eot/IRANSansWeb_Light.eot?#iefix') format('embedded-opentype'),
         url('../fonts/woff2/IRANSansWeb_Light.woff2') format('woff2'),
         url('../fonts/woff/IRANSansWeb_Light.woff') format('woff'),
         url('../fonts/ttf/IRANSansWeb_Light.ttf') format('truetype');
}

@font-face {
    font-family: IRANSans;
    font-style: normal;
    font-weight: 200;
    src: url('../fonts/eot/IRANSansWeb_UltraLight.eot');
    src: url('../fonts/eot/IRANSansWeb_UltraLight.eot?#iefix') format('embedded-opentype'),
         url('../fonts/woff2/IRANSansWeb_UltraLight.woff2') format('woff2'),
         url('../fonts/woff/IRANSansWeb_UltraLight.woff') format('woff'),
         url('../fonts/ttf/IRANSansWeb_UltraLight.ttf') format('truetype');
}

/* اگر فونت‌های اصلی در دسترس نباشند، از فونت‌های جایگزین استفاده می‌کند */
:root {
    --iran-sans-font: IRANSans, Tahoma, Arial, sans-serif;
}

/* استثنا قرار دادن آیکون‌ها از قانون فراگیر */
*:not(.dashicons):not([class^="dashicons-"]):not(.fa):not([class^="fa-"]):not(.glyphicon):not([class^="glyphicon-"]), 
*:not(.dashicons):not([class^="dashicons-"]):not(.fa):not([class^="fa-"]):not(.glyphicon):not([class^="glyphicon-"])::before, 
*:not(.dashicons):not([class^="dashicons-"]):not(.fa):not([class^="fa-"]):not(.glyphicon):not([class^="glyphicon-"])::after {
   
}

/* اعمال فونت به کلاس‌های عمومی */
body.rtl,
body.rtl .wrap,
body.rtl #wpcontent,
body.rtl #wpbody,
body.rtl #wpbody-content,
body.rtl #adminmenu li:not(.dashicons):not([class*="dashicons-"]), 
body.rtl #adminmenuwrap,
body.rtl .wp-toolbar,
body.rtl #wpadminbar:not(.dashicons):not([class*="dashicons-"]),
body.rtl .wp-admin,
body.rtl .setia-scheduler-wrap,
body.rtl .setia-card,
body.rtl .setia-modal {
    font-family: var(--iran-sans-font) !important;
}

/* تنظیمات RTL برای ادمین و داشبورد */
body.rtl .setia-scheduler-wrap {
    direction: rtl;
    text-align: right;
}

/* اعمال فونت به فرم‌ها و ورودی‌ها */
.setia-scheduler-wrap label,
.setia-scheduler-wrap input,
.setia-scheduler-wrap select,
.setia-scheduler-wrap textarea,
.setia-scheduler-wrap button,
input, 
select, 
textarea,
button,
.wp-core-ui .button,
.wp-core-ui .button-primary,
.wp-core-ui .button-secondary {
    font-family: var(--iran-sans-font) !important;
}

/* برای تقویم شمسی */
.datepicker-container * {
    font-family: var(--iran-sans-font) !important;
}

/* اعمال فونت به تمام عناصر افزونه */
body, 
#wpadminbar:not(.dashicons):not([class*="dashicons-"]),
#adminmenu li:not(.dashicons):not([class*="dashicons-"]),
.wp-core-ui,
.postbox,
.wp-list-table,
.components-button,
.setia-settings *:not(.dashicons):not([class*="dashicons-"]),
.setia-settings-page *:not(.dashicons):not([class*="dashicons-"]), 
.setia-section:not(.dashicons):not([class*="dashicons-"]), 
.setia-settings-container:not(.dashicons):not([class*="dashicons-"]),
.setia-main-container:not(.dashicons):not([class*="dashicons-"]),
#wpbody:not(.dashicons):not([class*="dashicons-"]),
#wpbody-content:not(.dashicons):not([class*="dashicons-"]),
.wrap:not(.dashicons):not([class*="dashicons-"]) {
    font-family: 'IRANSans', Tahoma, sans-serif !important;
}

/* استایل‌های اصلی با فونت ایرانسنس */
.wrap.setia-settings, 
.wrap.setia-settings-page, 
.wrap.setia-main-container,
.setia-section,
.setia-section *:not(.dashicons):not([class*="dashicons-"]),
.setia-actions,
.setia-button,
.setia-settings-container,
.setia-main-container,
.setia-schema-wrap,
.setia-schema-card,
.setia-history-wrap,
.setia-history-card {
    font-family: 'IRANSans', Tahoma, sans-serif !important;
}

/* بازگرداندن فونت آیکون‌ها */
.dashicons,
.dashicons-before:before,
[class^="dashicons-"]:before,
[class*=" dashicons-"]:before {
    font-family: dashicons !important;
    font-style: normal !important;
    font-weight: normal !important;
}

/* تقویت قوانین آیکون‌های داشبورد وردپرس */
#adminmenu .wp-menu-image:before,
#adminmenu .wp-has-current-submenu .wp-submenu .wp-submenu-head,
#adminmenu .wp-menu-arrow,
#adminmenu .wp-has-current-submenu.opensub .wp-submenu .wp-submenu-head,
#collapse-button .collapse-button-icon:before,
.wp-core-ui .button-primary.focus, 
.wp-core-ui .button-primary:focus,
#wpadminbar #wp-admin-bar-menu-toggle .ab-icon:before,
#wpadminbar #wp-admin-bar-wp-logo > .ab-item:before,
#wpadminbar #wp-admin-bar-site-name > .ab-item:before,
#wpadminbar #wp-admin-bar-comments .ab-icon:before,
#wpadminbar #wp-admin-bar-new-content .ab-icon:before,
#wpadminbar #wp-admin-bar-my-account.with-avatar > .ab-empty-item img,
#wpadminbar #wp-admin-bar-my-account.with-avatar > a img,
.notice-dismiss:before,
.notice-dismiss:after,
.wp-list-table .toggle-row:before {
    font-family: dashicons !important;
}

/* اطمینان از نمایش صحیح آیکون‌ها در منوی ادمین */
#adminmenu div.wp-menu-image:before {
    font-family: dashicons !important;
    font-size: 20px !important;
    line-height: 1 !important;
}

/* بهبود صفحه تنظیمات */
.setia-settings-header {
    background: linear-gradient(135deg, #4776E6 0%, #8E54E9 100%) !important;
    box-shadow: 0 8px 20px rgba(142, 84, 233, 0.2) !important;
    padding: 50px 30px !important;
    border-radius: 15px !important;
    margin-bottom: 40px !important;
}

.setia-settings-header h1 {
    font-size: 34px !important;
    margin-bottom: 20px !important;
    font-weight: bold !important;
}

.setia-section {
    border-radius: 15px !important;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08) !important;
    margin-bottom: 30px !important;
    overflow: hidden !important;
    border: none !important;
    transition: all 0.3s ease !important;
}

.setia-section:hover {
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12) !important;
    transform: translateY(-5px) !important;
}

.setia-section h2 {
    padding: 20px 25px !important;
    background: linear-gradient(to right, #f8f9fa, #ffffff) !important;
    border-bottom: 1px solid #e9ecef !important;
    font-size: 20px !important;
    font-weight: bold !important;
    color: #333 !important;
}

.setia-section .form-table th {
    padding: 25px 25px !important;
    font-weight: bold !important;
    color: #333 !important;
}

.setia-section .form-table td {
    padding: 25px 25px !important;
}

.setia-section input[type="text"], 
.setia-section select,
.setia-input,
.setia-select {
    width: 100% !important;
    max-width: 400px !important;
    padding: 12px 15px !important;
    border: 2px solid #e9ecef !important;
    border-radius: 8px !important;
    font-size: 14px !important;
    line-height: 1.8 !important;
    min-height: 45px !important;
    transition: all 0.3s ease !important;
    background-color: #fff !important;
    color: #333 !important;
}

.setia-section input[type="text"]:focus,
.setia-section select:focus,
.setia-input:focus,
.setia-select:focus {
    border-color: #8E54E9 !important;
    box-shadow: 0 0 0 3px rgba(142, 84, 233, 0.2) !important;
    outline: none !important;
}

.setia-section .button-primary, 
.setia-main-submit {
    background: linear-gradient(135deg, #4776E6 0%, #8E54E9 100%) !important;
    border: none !important;
    color: #fff !important;
    padding: 12px 30px !important;
    min-height: 45px !important;
    line-height: 2 !important;
    font-size: 15px !important;
    font-weight: bold !important;
    border-radius: 8px !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
    text-transform: uppercase !important;
    letter-spacing: 0.5px !important;
    box-shadow: 0 4px 10px rgba(142, 84, 233, 0.3) !important;
}

.setia-section .button-primary:hover,
.setia-section .button-primary:focus,
.setia-main-submit:hover,
.setia-main-submit:focus {
    box-shadow: 0 6px 15px rgba(142, 84, 233, 0.4) !important;
    transform: translateY(-2px) !important;
    background: linear-gradient(135deg, #3a68d8 0%, #7941d2 100%) !important;
}

/* مقدار هیجان انگیزتر کردن عناصر دیگر */
.api-test-container .button,
.setia-button {
    background: linear-gradient(135deg, #4776E6 0%, #8E54E9 100%) !important;
    transition: all 0.3s ease !important;
}

.api-test-container .button:hover,
.setia-button:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 6px 15px rgba(142, 84, 233, 0.4) !important;
}

/* بهبود تصاویر پیش‌نمایش */
.image-test-section, .setia-test-result {
    border-radius: 10px !important;
    overflow: hidden !important;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1) !important;
}

#setia-test-image-preview img, 
.setia-test-image {
    border-radius: 8px !important;
    transition: all 0.3s ease !important;
    max-width: 100% !important;
    height: auto !important;
}

#setia-test-image-preview img:hover, 
.setia-test-image:hover {
    transform: scale(1.02) !important;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15) !important;
}

/* بخش عیب‌یابی و ابزارهای کش */
.setia-troubleshooting, .setia-cache-tools {
    margin-top: 30px !important;
    padding: 20px !important;
    background: #f9f9f9 !important;
}

.setia-troubleshooting {
    border-left: 5px solid #4776E6 !important;
    border-radius: 0 10px 10px 0 !important;
}

.setia-cache-tools {
    border-left: 5px solid #8E54E9 !important;
    border-radius: 0 10px 10px 0 !important;
}

.setia-troubleshooting h3, .setia-cache-tools h3 {
    color: #4776E6 !important;
    margin-top: 0 !important;
}

.setia-cache-tools .button {
    margin-top: 10px !important;
}

/* تقویت اعمال فونت ایران‌سنس به افزونه */
.setia-settings-container,
.setia-settings-container *:not(.dashicons):not([class^="dashicons-"]),
.setia-settings-page,
.setia-settings-page *:not(.dashicons):not([class^="dashicons-"]),
.setia-main-container,
.setia-main-container *:not(.dashicons):not([class^="dashicons-"]),
.setia-section,
.setia-section *:not(.dashicons):not([class^="dashicons-"]),
.setia-schema-wrap,
.setia-schema-wrap *:not(.dashicons):not([class^="dashicons-"]),
.setia-history-wrap,
.setia-history-wrap *:not(.dashicons):not([class^="dashicons-"]),
.setia-scheduler-wrap:not(.dashicons):not([class^="dashicons-"]),
.setia-scheduler-wrap *:not(.dashicons):not([class^="dashicons-"]),
.setia-card,
.setia-card *:not(.dashicons):not([class^="dashicons-"]),
.setia-modal,
.setia-modal *:not(.dashicons):not([class^="dashicons-"]),
.setia-button,
.setia-input,
.setia-select,
.setia-textarea,
.setia-form-group,
.setia-form-group *:not(.dashicons):not([class^="dashicons-"]) {
    font-family: var(--iran-sans-font) !important;
}

/* بهبود خوانایی متن فارسی */
body.rtl,
.rtl,
[dir="rtl"],
.setia-settings-container,
.setia-main-container,
.setia-section,
.setia-schema-wrap,
.setia-history-wrap,
.setia-scheduler-wrap {
    font-size: 14px !important;
    line-height: 1.8 !important;
    letter-spacing: 0 !important;
    font-weight: normal !important;
    text-rendering: optimizeLegibility !important;
    -webkit-font-smoothing: antialiased !important;
}

/* بهبود صفحه تنظیمات */
.setia-settings-header {
    background: linear-gradient(135deg, #4776E6 0%, #8E54E9 100%) !important;
    box-shadow: 0 8px 20px rgba(142, 84, 233, 0.2) !important;
    padding: 50px 30px !important;
    border-radius: 15px !important;
    margin-bottom: 40px !important;
}

.setia-settings-header h1 {
    font-size: 34px !important;
    margin-bottom: 20px !important;
    font-weight: bold !important;
}

.setia-section {
    border-radius: 15px !important;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08) !important;
    margin-bottom: 30px !important;
    overflow: hidden !important;
    border: none !important;
    transition: all 0.3s ease !important;
}

.setia-section:hover {
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12) !important;
    transform: translateY(-5px) !important;
}

.setia-section h2 {
    padding: 20px 25px !important;
    background: linear-gradient(to right, #f8f9fa, #ffffff) !important;
    border-bottom: 1px solid #e9ecef !important;
    font-size: 20px !important;
    font-weight: bold !important;
    color: #333 !important;
}

.setia-section .form-table th {
    padding: 25px 25px !important;
    font-weight: bold !important;
    color: #333 !important;
}

.setia-section .form-table td {
    padding: 25px 25px !important;
}

.setia-section input[type="text"], 
.setia-section select,
.setia-input,
.setia-select {
    width: 100% !important;
    max-width: 400px !important;
    padding: 12px 15px !important;
    border: 2px solid #e9ecef !important;
    border-radius: 8px !important;
    font-size: 14px !important;
    line-height: 1.8 !important;
    min-height: 45px !important;
    transition: all 0.3s ease !important;
    background-color: #fff !important;
    color: #333 !important;
}

.setia-section input[type="text"]:focus,
.setia-section select:focus,
.setia-input:focus,
.setia-select:focus {
    border-color: #8E54E9 !important;
    box-shadow: 0 0 0 3px rgba(142, 84, 233, 0.2) !important;
    outline: none !important;
}

.setia-section .button-primary, 
.setia-main-submit {
    background: linear-gradient(135deg, #4776E6 0%, #8E54E9 100%) !important;
    border: none !important;
    color: #fff !important;
    padding: 12px 30px !important;
    min-height: 45px !important;
    line-height: 2 !important;
    font-size: 15px !important;
    font-weight: bold !important;
    border-radius: 8px !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
    text-transform: uppercase !important;
    letter-spacing: 0.5px !important;
    box-shadow: 0 4px 10px rgba(142, 84, 233, 0.3) !important;
}

.setia-section .button-primary:hover,
.setia-section .button-primary:focus,
.setia-main-submit:hover,
.setia-main-submit:focus {
    box-shadow: 0 6px 15px rgba(142, 84, 233, 0.4) !important;
    transform: translateY(-2px) !important;
    background: linear-gradient(135deg, #3a68d8 0%, #7941d2 100%) !important;
}

/* مقدار هیجان انگیزتر کردن عناصر دیگر */
.api-test-container .button,
.setia-button {
    background: linear-gradient(135deg, #4776E6 0%, #8E54E9 100%) !important;
    transition: all 0.3s ease !important;
}

.api-test-container .button:hover,
.setia-button:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 6px 15px rgba(142, 84, 233, 0.4) !important;
}

/* بهبود تصاویر پیش‌نمایش */
.image-test-section, .setia-test-result {
    border-radius: 10px !important;
    overflow: hidden !important;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1) !important;
}

#setia-test-image-preview img, 
.setia-test-image {
    border-radius: 8px !important;
    transition: all 0.3s ease !important;
    max-width: 100% !important;
    height: auto !important;
}

#setia-test-image-preview img:hover, 
.setia-test-image:hover {
    transform: scale(1.02) !important;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15) !important;
}

/* بخش عیب‌یابی و ابزارهای کش */
.setia-troubleshooting, .setia-cache-tools {
    margin-top: 30px !important;
    padding: 20px !important;
    background: #f9f9f9 !important;
}

.setia-troubleshooting {
    border-left: 5px solid #4776E6 !important;
    border-radius: 0 10px 10px 0 !important;
}

.setia-cache-tools {
    border-left: 5px solid #8E54E9 !important;
    border-radius: 0 10px 10px 0 !important;
}

.setia-troubleshooting h3, .setia-cache-tools h3 {
    color: #4776E6 !important;
    margin-top: 0 !important;
}

.setia-cache-tools .button {
    margin-top: 10px !important;
} 