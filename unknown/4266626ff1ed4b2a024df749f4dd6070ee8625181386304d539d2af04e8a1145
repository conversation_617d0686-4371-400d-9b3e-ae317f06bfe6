<?php
/**
 * تست فایل‌های حیاتی افزونه SETIA
 * این فایل برای تشخیص فایل‌های ضروری استفاده می‌شود
 */

// امنیت: جلوگیری از دسترسی مستقیم
if (!defined('ABSPATH')) {
    exit('دسترسی مستقیم مجاز نیست');
}

echo "🔍 تست فایل‌های حیاتی افزونه SETIA\n";
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n\n";

// فایل‌های حیاتی به ترتیب اهمیت
$critical_files = [
    'setia-content-generator.php' => [
        'name' => 'فایل اصلی افزونه',
        'importance' => 5,
        'description' => 'فایل اصلی که شامل Plugin Header و کلاس اصلی است'
    ],
    'ajax-handlers.php' => [
        'name' => 'هندلر AJAX',
        'importance' => 5,
        'description' => 'مدیریت تمام درخواست‌های AJAX'
    ],
    'includes/class-content-generator.php' => [
        'name' => 'کلاس تولید محتوا',
        'importance' => 4,
        'description' => 'منطق اصلی تولید محتوا با هوش مصنوعی'
    ],
    'includes/scheduler.php' => [
        'name' => 'سیستم زمانبندی',
        'importance' => 4,
        'description' => 'مدیریت انتشار خودکار محتوا'
    ],
    'templates/main-page.php' => [
        'name' => 'صفحه اصلی',
        'importance' => 4,
        'description' => 'رابط کاربری اصلی تولید محتوا'
    ],
    'templates/settings-page.php' => [
        'name' => 'صفحه تنظیمات',
        'importance' => 3,
        'description' => 'رابط کاربری تنظیمات'
    ],
    'includes/date-functions.php' => [
        'name' => 'توابع تاریخ',
        'importance' => 3,
        'description' => 'تبدیل تاریخ شمسی و میلادی'
    ],
    'assets/css/admin-settings.css' => [
        'name' => 'استایل‌های مدیریت',
        'importance' => 3,
        'description' => 'استایل‌دهی رابط کاربری'
    ],
    'assets/js/main-page-enhanced.js' => [
        'name' => 'جاوااسکریپت صفحه اصلی',
        'importance' => 3,
        'description' => 'عملکرد تعاملی صفحه اصلی'
    ],
    'assets/js/admin.js' => [
        'name' => 'جاوااسکریپت مدیریت',
        'importance' => 3,
        'description' => 'عملکرد تعاملی پنل مدیریت'
    ]
];

// بررسی وجود فایل‌ها
$missing_critical = [];
$existing_files = [];

foreach ($critical_files as $file => $info) {
    $file_path = __DIR__ . '/' . $file;
    $exists = file_exists($file_path);
    
    $importance_stars = str_repeat('⭐', $info['importance']);
    $status = $exists ? '✅' : '❌';
    
    echo "$status $importance_stars {$info['name']}\n";
    echo "   📁 $file\n";
    echo "   📝 {$info['description']}\n";
    
    if ($exists) {
        $size = filesize($file_path);
        $readable = is_readable($file_path) ? 'قابل خواندن' : 'غیرقابل خواندن';
        echo "   📊 اندازه: " . number_format($size) . " بایت - $readable\n";
        $existing_files[] = $file;
    } else {
        echo "   ⚠️  فایل یافت نشد!\n";
        if ($info['importance'] >= 4) {
            $missing_critical[] = $file;
        }
    }
    
    echo "\n";
}

// نتیجه‌گیری
echo "📋 نتیجه‌گیری:\n";
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n";

$total_files = count($critical_files);
$existing_count = count($existing_files);
$missing_count = $total_files - $existing_count;
$critical_missing = count($missing_critical);

echo "📊 آمار کلی:\n";
echo "   - کل فایل‌های بررسی شده: $total_files\n";
echo "   - فایل‌های موجود: $existing_count\n";
echo "   - فایل‌های مفقود: $missing_count\n";
echo "   - فایل‌های حیاتی مفقود: $critical_missing\n\n";

if ($critical_missing > 0) {
    echo "🚨 هشدار: فایل‌های حیاتی زیر مفقود هستند:\n";
    foreach ($missing_critical as $file) {
        echo "   ❌ $file\n";
    }
    echo "\n💡 بدون این فایل‌ها، افزونه به درستی کار نخواهد کرد!\n\n";
    
    echo "🔧 اقدامات مورد نیاز:\n";
    echo "1. فایل‌های مفقود را از نسخه اصلی کپی کنید\n";
    echo "2. مجوزهای فایل‌ها را بررسی کنید (644 برای فایل‌ها، 755 برای پوشه‌ها)\n";
    echo "3. افزونه را غیرفعال و مجدداً فعال کنید\n";
    echo "4. در صورت ادامه مشکل، افزونه را مجدداً نصب کنید\n\n";
    
} else if ($missing_count > 0) {
    echo "⚠️  توجه: برخی فایل‌های غیرحیاتی مفقود هستند\n";
    echo "افزونه کار می‌کند اما ممکن است برخی ویژگی‌ها محدود باشند\n\n";
    
} else {
    echo "🎉 عالی! تمام فایل‌های حیاتی موجود هستند\n";
    echo "افزونه آماده استفاده است\n\n";
}

// راهنمای اولویت‌بندی
echo "📚 راهنمای اولویت‌بندی فایل‌ها:\n";
echo "⭐⭐⭐⭐⭐ حیاتی - بدون این فایل‌ها افزونه کار نمی‌کند\n";
echo "⭐⭐⭐⭐ مهم - عملکرد اصلی متأثر می‌شود\n";
echo "⭐⭐⭐ ضروری - برخی ویژگی‌ها کار نمی‌کند\n";
echo "⭐⭐ مفید - تجربه کاربری بهتر می‌شود\n";
echo "⭐ اختیاری - ویژگی‌های اضافی\n\n";

// تست عملکرد سریع
if ($critical_missing == 0) {
    echo "🔧 تست عملکرد سریع:\n";
    
    // تست بارگذاری کلاس اصلی
    if (class_exists('SETIA_Content_Generator')) {
        echo "✅ کلاس اصلی افزونه بارگذاری شده\n";
    } else {
        echo "❌ کلاس اصلی افزونه بارگذاری نشده\n";
    }
    
    // تست بارگذاری AJAX handlers
    if (class_exists('SETIA_Ajax_Handlers')) {
        echo "✅ کلاس AJAX handlers بارگذاری شده\n";
    } else {
        echo "❌ کلاس AJAX handlers بارگذاری نشده\n";
    }
    
    // تست وجود اکشن‌های وردپرس
    if (has_action('wp_ajax_setia_generate_content')) {
        echo "✅ اکشن‌های AJAX ثبت شده‌اند\n";
    } else {
        echo "❌ اکشن‌های AJAX ثبت نشده‌اند\n";
    }
}

echo "\n🆘 در صورت مشکل: <EMAIL>\n";
?>
