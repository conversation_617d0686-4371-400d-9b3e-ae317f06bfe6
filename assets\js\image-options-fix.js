/**
 * اسکریپت حل مشکل نمایش تنظیمات تصویر شاخص
 * این فایل به صورت مستقل فقط مشکل نمایش/عدم نمایش تنظیمات تصویر را حل می‌کند
 */
(function() {
    // اجرای سریع و مستقیم کد
    function setImageOptionsVisibility() {
        var checkbox = document.getElementById('generate_image');
        var container = document.getElementById('setia-image-options-container');
        
        if (!checkbox || !container) {
            console.log('خطا: عناصر چک‌باکس یا کانتینر پیدا نشدند');
            return;
        }
        
        // نمایش/عدم نمایش بر اساس وضعیت چک‌باکس
        if (checkbox.checked) {
            container.style.display = 'block';
        } else {
            container.style.display = 'none';
        }
        
        // حذف همه event listenerهای قبلی
        var newCheckbox = checkbox.cloneNode(true);
        checkbox.parentNode.replaceChild(newCheckbox, checkbox);
        checkbox = newCheckbox;
        
        // اضافه کردن event listener جدید
        checkbox.addEventListener('change', function() {
            if (this.checked) {
                container.style.display = 'block';
            } else {
                container.style.display = 'none';
            }
            console.log('وضعیت چک‌باکس تغییر کرد به:', this.checked ? 'فعال' : 'غیرفعال');
        });
    }
    
    // اجرای کد در زمان بارگذاری صفحه
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', function() {
            setImageOptionsVisibility();
            // اجرای مجدد با تاخیر برای اطمینان
            setTimeout(setImageOptionsVisibility, 500);
        });
    } else {
        setImageOptionsVisibility();
        // اجرای مجدد با تاخیر برای اطمینان
        setTimeout(setImageOptionsVisibility, 500);
    }
    
    // اجرای مجدد هنگام باز شدن مودال
    document.addEventListener('click', function(e) {
        if (e.target && (e.target.id === 'setia-new-schedule' || e.target.classList.contains('setia-action-edit'))) {
            setTimeout(setImageOptionsVisibility, 300);
            setTimeout(setImageOptionsVisibility, 1000);
        }
    });
    
    // اطمینان از اجرا با jQuery نیز
    if (typeof jQuery !== 'undefined') {
        jQuery(document).ready(function($) {
            setImageOptionsVisibility();
            $(document).on('click', '#setia-new-schedule, .setia-action-edit', function() {
                setTimeout(setImageOptionsVisibility, 300);
                setTimeout(setImageOptionsVisibility, 1000);
            });
        });
    }
})(); 