/**
 * Enhanced Main Page JavaScript for SETIA Content Generator
 */

jQuery(document).ready(function($) {
    'use strict';

    // علامت‌گذاری که این فایل لود شده
    window.setiaMainPageEnhanced = true;
    
    // Initialize enhanced main page functionality
    initializeMainPage();
    
    function initializeMainPage() {
        // Form validation and enhancement
        initializeFormValidation();
        
        // Toggle functionality
        initializeToggles();
        
        // Tab functionality
        initializeTabs();
        
        // Content generation
        initializeContentGeneration();
        
        // Character counters
        initializeCounters();
        
        // Progress indicator
        initializeProgressIndicator();
        
        // Result actions
        initializeResultActions();
    }
    
    function initializeFormValidation() {
        // Real-time validation for topic
        $('#setia-topic').on('input', function() {
            validateField($(this), {
                required: true,
                minLength: 5,
                maxLength: 100
            });
        });
        
        // Real-time validation for keywords
        $('#setia-keywords').on('input', function() {
            validateField($(this), {
                required: true,
                minLength: 3
            });
            updateKeywordsCounter();
        });
        
        // Form submission validation
        $('#setia-content-form').on('submit', function(e) {
            if (!validateForm()) {
                e.preventDefault();
                showNotification('لطفا خطاهای فرم را برطرف کنید', 'error');
            }
        });
    }
    
    function validateField($field, rules) {
        const value = $field.val().trim();
        const $validation = $field.siblings('.input-validation');
        let isValid = true;
        
        if (rules.required && !value) {
            isValid = false;
        }
        
        if (rules.minLength && value.length < rules.minLength) {
            isValid = false;
        }
        
        if (rules.maxLength && value.length > rules.maxLength) {
            isValid = false;
        }
        
        $validation.removeClass('valid invalid').addClass(isValid ? 'valid' : 'invalid');
        return isValid;
    }
    
    function validateForm() {
        let isValid = true;
        
        // Validate topic
        if (!validateField($('#setia-topic'), { required: true, minLength: 5, maxLength: 100 })) {
            isValid = false;
        }
        
        // Validate keywords
        if (!validateField($('#setia-keywords'), { required: true, minLength: 3 })) {
            isValid = false;
        }
        
        return isValid;
    }
    
    function initializeToggles() {
        // Image generation toggle
        $('#setia-image').on('change', function() {
            const $container = $('#setia-image-options-container');
            if ($(this).is(':checked')) {
                $container.slideDown(300);
            } else {
                $container.slideUp(300);
            }
        });
        
        // Initialize toggle states
        if ($('#setia-image').is(':checked')) {
            $('#setia-image-options-container').show();
        }
    }
    
    function initializeTabs() {
        $('.setia-tab-button').on('click', function() {
            const tabId = $(this).data('tab');
            
            // Update tab buttons
            $('.setia-tab-button').removeClass('active');
            $(this).addClass('active');
            
            // Update tab content
            $('.setia-tab-content').removeClass('active');
            $('#' + tabId + '-tab').addClass('active');
        });
    }
    
    function initializeContentGeneration() {
        $('#setia-content-form').on('submit', function(e) {
            e.preventDefault();
            
            if (!validateForm()) {
                return;
            }
            
            generateContent();
        });
    }
    
    function generateContent() {
        const $button = $('#setia-generate-btn');
        const $buttonText = $button.find('.button-text');
        const $buttonLoader = $button.find('.button-loader');
        
        // Update UI for loading state
        $button.prop('disabled', true).addClass('loading');
        $buttonText.text('در حال تولید...');
        $buttonLoader.show();
        
        // Update progress indicator
        updateProgressStep(2);
        
        // Collect form data
        const formData = {
            action: 'setia_generate_content',
            nonce: setiaParams.nonce,
            topic: $('#setia-topic').val(),
            keywords: $('#setia-keywords').val(),
            tone: $('#setia-tone').val(),
            category: $('#setia-category').val(),
            length: $('#setia-length').val(),
            seo: $('#setia-seo').is(':checked') ? 'yes' : 'no',
            generate_image: $('#setia-image').is(':checked') ? 'yes' : 'no',
            image_style: $('#setia-image-style').val(),
            aspect_ratio: $('#setia-aspect-ratio').val(),
            negative_prompt: $('#setia-negative-prompt').val(),
            image_prompt_details: $('#image_prompt_details').val(),
            instructions: $('#setia-instructions').val()
        };
        
        // Show generation progress
        showGenerationProgress();
        
        // Send AJAX request
        $.ajax({
            url: setiaParams.ajaxUrl,
            type: 'POST',
            data: formData,
            timeout: 120000, // 2 minutes timeout
            success: function(response) {
                handleGenerationSuccess(response);
            },
            error: function(xhr, status, error) {
                handleGenerationError(error);
            },
            complete: function() {
                // Reset button state
                $button.prop('disabled', false).removeClass('loading');
                $buttonText.text('تولید محتوا');
                $buttonLoader.hide();
                
                hideGenerationProgress();
            }
        });
    }
    
    function showGenerationProgress() {
        // Create progress modal or notification
        const progressHtml = `
            <div id="generation-progress" class="setia-generation-progress">
                <div class="progress-content">
                    <div class="progress-icon">
                        <svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M12 2V6M12 18V22M4.93 4.93L7.76 7.76M16.24 16.24L19.07 19.07M2 12H6M18 12H22M4.93 19.07L7.76 16.24M16.24 7.76L19.07 4.93" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    </div>
                    <h3>در حال تولید محتوا...</h3>
                    <p>لطفا صبر کنید، این فرآیند ممکن است تا 2 دقیقه طول بکشد</p>
                    <div class="progress-steps">
                        <div class="progress-step-item active">
                            <span class="step-icon">📝</span>
                            <span class="step-text">تولید متن</span>
                        </div>
                        <div class="progress-step-item" id="seo-step">
                            <span class="step-icon">🔍</span>
                            <span class="step-text">بهینه‌سازی سئو</span>
                        </div>
                        <div class="progress-step-item" id="image-step">
                            <span class="step-icon">🖼️</span>
                            <span class="step-text">تولید تصویر</span>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        $('body').append(progressHtml);
        
        // Simulate progress steps
        setTimeout(() => {
            $('#seo-step').addClass('active');
        }, 10000);
        
        setTimeout(() => {
            $('#image-step').addClass('active');
        }, 20000);
    }
    
    function hideGenerationProgress() {
        $('#generation-progress').fadeOut(300, function() {
            $(this).remove();
        });
    }
    
    function handleGenerationSuccess(response) {
        if (response.success) {
            // Update progress indicator
            updateProgressStep(3);
            
            // Show result container
            $('#setia-result-container').fadeIn(500);
            
            // Populate content
            populateGeneratedContent(response.data);
            
            // Enable result actions
            enableResultActions();
            
            // Scroll to results
            $('html, body').animate({
                scrollTop: $('#setia-result-container').offset().top - 100
            }, 800);
            
            showNotification('محتوا با موفقیت تولید شد!', 'success');
        } else {
            handleGenerationError(response.data.message || 'خطای نامشخص');
        }
    }
    
    function handleGenerationError(error) {
        showNotification('خطا در تولید محتوا: ' + error, 'error');
        updateProgressStep(1); // Reset to first step
    }
    
    function populateGeneratedContent(data) {
        // Populate optimized title
        if (data.optimized_title) {
            $('#setia-optimized-title').text(data.optimized_title);
            $('.setia-optimized-title-container').show();
        }
        
        // Populate content preview
        if (data.content) {
            $('#setia-content-preview').html(data.content);
            updateContentStats(data.content);
        }
        
        // Populate SEO data
        if (data.seo) {
            $('#setia-seo-title').text(data.seo.title || '');
            $('#setia-seo-description').text(data.seo.description || '');
            $('#setia-seo-keywords').text(data.seo.keywords || '');
            $('#setia-seo-meta-length').text((data.seo.description || '').length + ' کاراکتر');
        }
        
        // Populate image
        if (data.image_url) {
            const imageHtml = `<img src="${data.image_url}" alt="تصویر تولید شده" />`;
            $('#setia-image-preview-container').html(imageHtml);
        }

        // فعال کردن دکمه‌ها
        $('#setia-publish-btn, #setia-draft-btn, #setia-regenerate-btn, #setia-copy-btn').prop('disabled', false);

        // ذخیره شناسه محتوا
        if (data.content_id) {
            $('#setia-content-form').data('content-id', data.content_id);
        }
    }
    
    function updateContentStats(content) {
        // Calculate word count
        const wordCount = content.replace(/<[^>]*>/g, '').split(/\s+/).filter(word => word.length > 0).length;
        $('#content-word-count').text(wordCount.toLocaleString('fa-IR'));
        
        // Calculate reading time (assuming 200 words per minute for Persian)
        const readingTime = Math.ceil(wordCount / 200);
        $('#content-read-time').text(readingTime.toLocaleString('fa-IR'));
    }
    
    function initializeCounters() {
        // Keywords counter
        $('#setia-keywords').on('input', updateKeywordsCounter);
        
        // Instructions character counter
        $('#setia-instructions').on('input', function() {
            const count = $(this).val().length;
            $('#instructions-count').text(count.toLocaleString('fa-IR'));
            
            if (count > 500) {
                $(this).addClass('error');
            } else {
                $(this).removeClass('error');
            }
        });
    }
    
    function updateKeywordsCounter() {
        const keywords = $('#setia-keywords').val().split(',').filter(k => k.trim().length > 0);
        $('#keywords-count').text(keywords.length.toLocaleString('fa-IR'));
    }
    
    function initializeProgressIndicator() {
        // Progress indicator is updated by other functions
    }
    
    function updateProgressStep(step) {
        $('.progress-step').removeClass('active');
        $(`.progress-step[data-step="${step}"]`).addClass('active');
    }
    
    function initializeResultActions() {
        // Publish button
        $('#setia-publish-btn').on('click', function() {
            publishContent();
        });
        
        // Draft button
        $('#setia-draft-btn').on('click', function() {
            saveDraft();
        });
        
        // Regenerate button
        $('#setia-regenerate-btn').on('click', function() {
            if (confirm('آیا مطمئن هستید که می‌خواهید محتوا را مجدداً تولید کنید؟')) {
                generateContent();
            }
        });
        
        // Copy button
        $('#setia-copy-btn').on('click', function() {
            copyContent();
        });
        
        // Export button
        $('#setia-export-btn').on('click', function() {
            exportToWord();
        });
    }
    
    function enableResultActions() {
        $('#setia-result-container .setia-button').prop('disabled', false);
    }
    
    function publishContent() {
        // Implementation for publishing content
        showNotification('قابلیت انتشار به زودی اضافه خواهد شد', 'info');
    }
    
    function saveDraft() {
        // Implementation for saving draft
        showNotification('قابلیت ذخیره پیش‌نویس به زودی اضافه خواهد شد', 'info');
    }
    
    function copyContent() {
        const content = $('#setia-content-preview').text();
        navigator.clipboard.writeText(content).then(function() {
            showNotification('محتوا در کلیپ‌بورد کپی شد', 'success');
        }).catch(function() {
            showNotification('خطا در کپی کردن محتوا', 'error');
        });
    }
    
    function exportToWord() {
        // Implementation for Word export
        showNotification('قابلیت خروجی Word به زودی اضافه خواهد شد', 'info');
    }
    
    function showNotification(message, type) {
        const $notification = $(`
            <div class="setia-notification setia-notification-${type}">
                ${message}
            </div>
        `);
        
        $('body').append($notification);
        
        setTimeout(function() {
            $notification.addClass('show');
        }, 100);
        
        setTimeout(function() {
            $notification.removeClass('show');
            setTimeout(function() {
                $notification.remove();
            }, 300);
        }, 3000);
    }

    // مدیریت دکمه انتشار پست
    $('#setia-publish-btn').on('click', function() {
        var contentId = $('#setia-content-form').data('content-id');

        if (!contentId) {
            showNotification('خطا: شناسه محتوا یافت نشد.', 'error');
            return;
        }

        $(this).prop('disabled', true).text('در حال انتشار...');

        $.ajax({
            url: setiaParams.ajaxUrl,
            type: 'POST',
            data: {
                action: 'setia_publish_content',
                nonce: setiaParams.nonce,
                content_id: contentId,
                status: 'publish'
            },
            success: function(response) {
                $('#setia-publish-btn').prop('disabled', false).text('انتشار پست');

                if (response.success) {
                    window.location.href = response.data.edit_url;
                } else {
                    showNotification('خطا در انتشار محتوا: ' + (response.data ? response.data.message : 'خطای نامشخص'), 'error');
                }
            },
            error: function() {
                $('#setia-publish-btn').prop('disabled', false).text('انتشار پست');
                showNotification('خطا در ارتباط با سرور. لطفاً مجدداً تلاش کنید.', 'error');
            }
        });
    });

    // مدیریت دکمه ذخیره پیش‌نویس
    $('#setia-draft-btn').on('click', function() {
        var contentId = $('#setia-content-form').data('content-id');

        if (!contentId) {
            showNotification('خطا: شناسه محتوا یافت نشد.', 'error');
            return;
        }

        $(this).prop('disabled', true).text('در حال ذخیره...');

        $.ajax({
            url: setiaParams.ajaxUrl,
            type: 'POST',
            data: {
                action: 'setia_publish_content',
                nonce: setiaParams.nonce,
                content_id: contentId,
                status: 'draft'
            },
            success: function(response) {
                $('#setia-draft-btn').prop('disabled', false).text('ذخیره پیش‌نویس');

                if (response.success) {
                    window.location.href = response.data.edit_url;
                } else {
                    showNotification('خطا در ذخیره محتوا: ' + (response.data ? response.data.message : 'خطای نامشخص'), 'error');
                }
            },
            error: function() {
                $('#setia-draft-btn').prop('disabled', false).text('ذخیره پیش‌نویس');
                showNotification('خطا در ارتباط با سرور. لطفاً مجدداً تلاش کنید.', 'error');
            }
        });
    });

    // مدیریت دکمه کپی محتوا
    $('#setia-copy-btn').on('click', function() {
        var content = $('#setia-content-preview').html();

        // ایجاد یک المان موقت برای کپی
        var tempElement = $('<div>').html(content).appendTo('body').css('position', 'absolute').css('left', '-9999px');

        // انتخاب متن
        var range = document.createRange();
        range.selectNodeContents(tempElement[0]);
        var selection = window.getSelection();
        selection.removeAllRanges();
        selection.addRange(range);

        // کپی
        try {
            document.execCommand('copy');
            showNotification('محتوا با موفقیت کپی شد.', 'success');
        } catch (err) {
            showNotification('خطا در کپی محتوا: ' + err, 'error');
        }

        // حذف المان موقت
        tempElement.remove();
    });

    // مدیریت دکمه تولید مجدد
    $('#setia-regenerate-btn').on('click', function() {
        if (confirm('آیا از تولید مجدد محتوا اطمینان دارید؟')) {
            $('#setia-content-form').submit();
        }
    });
});
