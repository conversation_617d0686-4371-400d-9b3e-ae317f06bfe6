<?php
/*
 * SETIA Manual Activation Script
 * این فایل برای فعال‌سازی دستی جدول دیتابیس و تنظیمات افزونه SETIA است
 */

// جلوگیری از دسترسی مستقیم
if (!defined('WPINC')) {
    die;
}

/**
 * فعال‌سازی دستی افزونه SETIA
 * این تابع را از طریق کد زیر در functions.php تم خود اجرا کنید
 * <?php do_action('setia_manual_activate'); ?>
 */
function setia_manual_activate() {
    // ایجاد جدول دیتابیس
    global $wpdb;
    $charset_collate = $wpdb->get_charset_collate();
    
    // جدول نگهداری تاریخچه محتوای تولید شده
    $table_name = $wpdb->prefix . 'setia_generated_content';
    
    $sql = "CREATE TABLE $table_name (
        id mediumint(9) NOT NULL AUTO_INCREMENT,
        post_id mediumint(9) DEFAULT NULL,
        topic varchar(255) NOT NULL,
        keywords text NOT NULL,
        tone varchar(50) NOT NULL,
        category varchar(100) NOT NULL,
        length varchar(50) NOT NULL,
        generated_text longtext NOT NULL,
        generated_image_url varchar(255) DEFAULT NULL,
        seo_meta text DEFAULT NULL,
        created_at datetime DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY  (id)
    ) $charset_collate;";
    
    require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
    dbDelta($sql);
    
    // ایجاد پوشه‌های مورد نیاز
    $upload_dir = wp_upload_dir();
    $setia_dir = trailingslashit($upload_dir['basedir']) . 'setia';
    
    if (!file_exists($setia_dir)) {
        wp_mkdir_p($setia_dir);
    }
    
    // ایجاد فایل .htaccess برای امنیت بیشتر
    $htaccess_file = $setia_dir . '/.htaccess';
    if (!file_exists($htaccess_file)) {
        $htaccess_content = "Options -Indexes\n";
        $htaccess_content .= "<Files *.php>\n";
        $htaccess_content .= "Order Allow,Deny\n";
        $htaccess_content .= "Deny from all\n";
        $htaccess_content .= "</Files>\n";
        
        file_put_contents($htaccess_file, $htaccess_content);
    }
    
    // ذخیره تنظیمات پیش‌فرض
    $default_settings = [
        'gemini_api_key' => '',
        'gemma_api_key' => '',
        'imagine_art_api_key' => '',
        'default_tone' => 'عادی',
        'default_length' => 'متوسط',
        'enable_seo' => 'yes',
        'enable_image_generation' => 'yes'
    ];
    
    // ایجاد تنظیمات پیش‌فرض اگر وجود ندارد
    if (!get_option('setia_settings')) {
        update_option('setia_settings', $default_settings);
    }
}

// هوک کردن تابع به یک اکشن که می‌توان از بیرون فراخوانی کرد
add_action('setia_manual_activate', 'setia_manual_activate');

/**
 * راهنمای استفاده:
 * 
 * روش 1: افزودن کد زیر به فایل functions.php تم خود
 * <?php do_action('setia_manual_activate'); ?>
 * 
 * روش 2: اجرای مستقیم با کد زیر در فایل PHP
 * <?php 
 * include_once(ABSPATH . 'wp-admin/includes/plugin.php');
 * if (is_plugin_active('SETIA/setia-content-generator.php')) {
 *    do_action('setia_manual_activate');
 *    echo 'افزونه SETIA با موفقیت فعال شد.';
 * }
 * ?>
 * 
 * روش 3: با نصب افزونه فعال‌ساز دستی SETIA از منوی مدیریت
 */ 