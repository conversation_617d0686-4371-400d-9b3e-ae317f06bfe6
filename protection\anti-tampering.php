<?php
/**
 * SETIA Anti-Tampering System
 * Advanced file integrity and tamper detection
 */

if (!defined('ABSPATH')) {
    exit;
}

class SETIA_Anti_Tampering {
    
    private $protected_files = [];
    private $file_hashes = [];
    private $integrity_option = 'setia_file_integrity';
    private $last_check_option = 'setia_last_integrity_check';
    
    public function __construct() {
        $this->init_protected_files();
        add_action('admin_init', array($this, 'check_file_integrity'));
        add_action('wp_loaded', array($this, 'runtime_integrity_check'));
        
        // Schedule regular integrity checks
        add_action('setia_integrity_check', array($this, 'scheduled_integrity_check'));
        if (!wp_next_scheduled('setia_integrity_check')) {
            wp_schedule_event(time(), 'hourly', 'setia_integrity_check');
        }
    }
    
    /**
     * Initialize protected files list
     */
    private function init_protected_files() {
        $this->protected_files = [
            'setia-content-generator.php',
            'ajax-handlers.php',
            'gemini-ai-plugin.php',
            'includes/class-content-generator.php',
            'includes/scheduler.php',
            'includes/class-loader.php',
            'protection/license-system.php',
            'protection/php-obfuscator.php',
            'protection/js-obfuscator.php',
            'protection/anti-tampering.php'
        ];
    }
    
    /**
     * Generate file integrity hashes
     */
    public function generate_integrity_hashes($plugin_dir) {
        $hashes = array();
        
        foreach ($this->protected_files as $file) {
            $file_path = $plugin_dir . '/' . $file;
            if (file_exists($file_path)) {
                $content = file_get_contents($file_path);
                $hashes[$file] = array(
                    'md5' => md5($content),
                    'sha256' => hash('sha256', $content),
                    'size' => filesize($file_path),
                    'modified' => filemtime($file_path)
                );
            }
        }
        
        update_option($this->integrity_option, $hashes);
        return $hashes;
    }
    
    /**
     * Check file integrity
     */
    public function check_file_integrity() {
        $last_check = get_option($this->last_check_option, 0);
        $check_interval = 3600; // 1 hour
        
        if ((time() - $last_check) < $check_interval) {
            return;
        }
        
        $this->perform_integrity_check();
        update_option($this->last_check_option, time());
    }
    
    /**
     * Perform integrity check
     */
    private function perform_integrity_check() {
        $stored_hashes = get_option($this->integrity_option, array());
        
        if (empty($stored_hashes)) {
            // First time - generate hashes
            $this->generate_integrity_hashes(SETIA_PLUGIN_DIR);
            return;
        }
        
        $plugin_dir = SETIA_PLUGIN_DIR;
        $tampered_files = array();
        
        foreach ($this->protected_files as $file) {
            $file_path = $plugin_dir . '/' . $file;
            
            if (!file_exists($file_path)) {
                $tampered_files[] = array(
                    'file' => $file,
                    'issue' => 'File missing'
                );
                continue;
            }
            
            if (!isset($stored_hashes[$file])) {
                continue;
            }
            
            $content = file_get_contents($file_path);
            $current_md5 = md5($content);
            $current_sha256 = hash('sha256', $content);
            $current_size = filesize($file_path);
            
            $stored = $stored_hashes[$file];
            
            if ($current_md5 !== $stored['md5'] || 
                $current_sha256 !== $stored['sha256'] || 
                $current_size !== $stored['size']) {
                
                $tampered_files[] = array(
                    'file' => $file,
                    'issue' => 'File modified',
                    'expected_md5' => $stored['md5'],
                    'actual_md5' => $current_md5
                );
            }
        }
        
        if (!empty($tampered_files)) {
            $this->handle_tampering_detected($tampered_files);
        }
    }
    
    /**
     * Handle tampering detection
     */
    private function handle_tampering_detected($tampered_files) {
        // Log the incident
        error_log('SETIA SECURITY ALERT: File tampering detected: ' . json_encode($tampered_files));
        
        // Send email notification to admin
        $this->send_tampering_notification($tampered_files);
        
        // Disable plugin functionality
        $this->disable_plugin_functionality();
        
        // Show admin notice
        add_action('admin_notices', function() use ($tampered_files) {
            echo '<div class="notice notice-error">';
            echo '<p><strong>SETIA Security Alert:</strong> File tampering detected. Plugin has been disabled for security.</p>';
            echo '<p>Affected files: ' . implode(', ', array_column($tampered_files, 'file')) . '</p>';
            echo '</div>';
        });
    }
    
    /**
     * Send tampering notification email
     */
    private function send_tampering_notification($tampered_files) {
        $admin_email = get_option('admin_email');
        $site_name = get_bloginfo('name');
        $site_url = get_site_url();
        
        $subject = '[SECURITY ALERT] ' . $site_name . ' - SETIA Plugin Tampering Detected';
        
        $message = "Security Alert: File tampering detected in SETIA Content Generator plugin.\n\n";
        $message .= "Site: " . $site_name . " (" . $site_url . ")\n";
        $message .= "Time: " . date('Y-m-d H:i:s') . "\n\n";
        $message .= "Affected files:\n";
        
        foreach ($tampered_files as $file_info) {
            $message .= "- " . $file_info['file'] . " (" . $file_info['issue'] . ")\n";
        }
        
        $message .= "\nThe plugin has been automatically disabled for security.\n";
        $message .= "Please restore the original files and regenerate integrity hashes.\n";
        
        wp_mail($admin_email, $subject, $message);
    }
    
    /**
     * Disable plugin functionality
     */
    private function disable_plugin_functionality() {
        update_option('setia_security_disabled', true);
        
        // Remove AJAX handlers
        remove_all_actions('wp_ajax_setia_generate_content');
        remove_all_actions('wp_ajax_setia_publish_content');
        remove_all_actions('wp_ajax_setia_test_connection');
    }
    
    /**
     * Check if plugin is security disabled
     */
    public function is_security_disabled() {
        return get_option('setia_security_disabled', false);
    }
    
    /**
     * Runtime integrity check
     */
    public function runtime_integrity_check() {
        if ($this->is_security_disabled()) {
            if (is_admin()) {
                wp_die(
                    '<h1>Security Alert</h1>' .
                    '<p>SETIA Content Generator has been disabled due to detected file tampering.</p>' .
                    '<p>Please contact support to restore functionality.</p>',
                    'Security Alert',
                    array('response' => 403)
                );
            }
            return;
        }
        
        // Quick runtime check of critical files
        $this->quick_integrity_check();
    }
    
    /**
     * Quick integrity check for runtime
     */
    private function quick_integrity_check() {
        $critical_files = [
            'setia-content-generator.php',
            'ajax-handlers.php'
        ];
        
        $stored_hashes = get_option($this->integrity_option, array());
        
        foreach ($critical_files as $file) {
            if (!isset($stored_hashes[$file])) {
                continue;
            }
            
            $file_path = SETIA_PLUGIN_DIR . '/' . $file;
            if (!file_exists($file_path)) {
                $this->disable_plugin_functionality();
                return;
            }
            
            $current_md5 = md5_file($file_path);
            if ($current_md5 !== $stored_hashes[$file]['md5']) {
                $this->disable_plugin_functionality();
                return;
            }
        }
    }
    
    /**
     * Scheduled integrity check
     */
    public function scheduled_integrity_check() {
        $this->perform_integrity_check();
    }
    
    /**
     * Re-enable plugin after verification
     */
    public function re_enable_plugin() {
        delete_option('setia_security_disabled');
        
        // Regenerate integrity hashes
        $this->generate_integrity_hashes(SETIA_PLUGIN_DIR);
        
        return true;
    }
    
    /**
     * Add code signature verification
     */
    public function verify_code_signature($file_path) {
        if (!file_exists($file_path)) {
            return false;
        }
        
        $content = file_get_contents($file_path);
        
        // Look for signature comment
        if (preg_match('/\/\* SETIA_SIGNATURE: ([a-f0-9]+) \*\//', $content, $matches)) {
            $stored_signature = $matches[1];
            
            // Remove signature from content for verification
            $content_without_sig = preg_replace('/\/\* SETIA_SIGNATURE: [a-f0-9]+ \*\//', '', $content);
            
            $calculated_signature = hash('sha256', $content_without_sig . 'SETIA_SECRET_KEY_2024');
            
            return $stored_signature === $calculated_signature;
        }
        
        return false;
    }
    
    /**
     * Add signature to file
     */
    public function add_code_signature($file_path) {
        if (!file_exists($file_path)) {
            return false;
        }
        
        $content = file_get_contents($file_path);
        
        // Remove existing signature if present
        $content = preg_replace('/\/\* SETIA_SIGNATURE: [a-f0-9]+ \*\//', '', $content);
        
        // Calculate signature
        $signature = hash('sha256', $content . 'SETIA_SECRET_KEY_2024');
        
        // Add signature comment
        $signature_comment = "/* SETIA_SIGNATURE: {$signature} */";
        
        // Insert after opening PHP tag
        $content = preg_replace('/(<\?php)/', '$1' . "\n" . $signature_comment, $content, 1);
        
        file_put_contents($file_path, $content);
        
        return true;
    }
    
    /**
     * Get integrity status report
     */
    public function get_integrity_status() {
        $stored_hashes = get_option($this->integrity_option, array());
        $status = array(
            'protected_files' => count($this->protected_files),
            'monitored_files' => count($stored_hashes),
            'last_check' => get_option($this->last_check_option, 0),
            'security_disabled' => $this->is_security_disabled(),
            'files_status' => array()
        );
        
        foreach ($this->protected_files as $file) {
            $file_path = SETIA_PLUGIN_DIR . '/' . $file;
            $status['files_status'][$file] = array(
                'exists' => file_exists($file_path),
                'monitored' => isset($stored_hashes[$file]),
                'size' => file_exists($file_path) ? filesize($file_path) : 0
            );
        }
        
        return $status;
    }
}
