<?php
/**
 * SETIA Protection & Security Page Template
 */

if (!defined('ABSPATH')) {
    exit;
}

// Get protection status
$protection_status = isset($this->protection_manager) ? 
    $this->protection_manager->get_protection_status_display() : 
    array('overall' => array('status' => 'unknown', 'color' => 'gray', 'message' => 'Protection system not loaded'));

$license_status = isset($this->protection_manager) ? 
    $this->protection_manager->license_system->get_license_status_display() : 
    array('status' => 'unknown', 'color' => 'gray', 'message' => 'License system not loaded');
?>

<div class="wrap setia-protection-page">
    <h1>🔒 SETIA Security & Protection</h1>
    
    <!-- Protection Status Overview -->
    <div class="setia-protection-overview">
        <div class="setia-status-cards">
            <!-- Overall Status -->
            <div class="setia-status-card overall-status">
                <div class="status-header">
                    <h3>Overall Security Status</h3>
                    <span class="status-indicator <?php echo $protection_status['overall']['color']; ?>">
                        <?php echo $protection_status['overall']['status']; ?>
                    </span>
                </div>
                <p><?php echo $protection_status['overall']['message']; ?></p>
            </div>
            
            <!-- License Status -->
            <div class="setia-status-card license-status">
                <div class="status-header">
                    <h3>License Status</h3>
                    <span class="status-indicator <?php echo $license_status['color']; ?>">
                        <?php echo $license_status['status']; ?>
                    </span>
                </div>
                <p><?php echo $license_status['message']; ?></p>
                <?php if (isset($license_status['expires'])): ?>
                    <p><small>Expires: <?php echo date('Y-m-d', $license_status['expires']); ?></small></p>
                <?php endif; ?>
            </div>
            
            <!-- File Integrity -->
            <div class="setia-status-card integrity-status">
                <div class="status-header">
                    <h3>File Integrity</h3>
                    <span class="status-indicator <?php echo $protection_status['integrity']['color']; ?>">
                        <?php echo $protection_status['integrity']['status']; ?>
                    </span>
                </div>
                <p><?php echo $protection_status['integrity']['message']; ?></p>
            </div>
        </div>
    </div>
    
    <!-- License Management -->
    <div class="setia-protection-section">
        <h2>License Management</h2>
        <div class="setia-license-form">
            <?php if ($license_status['status'] === 'inactive'): ?>
                <form id="setia-license-activation-form">
                    <table class="form-table">
                        <tr>
                            <th scope="row">License Key</th>
                            <td>
                                <input type="text" id="license_key" name="license_key" class="regular-text" 
                                       placeholder="Enter your license key" required />
                            </td>
                        </tr>
                        <tr>
                            <th scope="row">Email Address</th>
                            <td>
                                <input type="email" id="license_email" name="license_email" class="regular-text" 
                                       placeholder="Enter your email address" required />
                            </td>
                        </tr>
                    </table>
                    
                    <p class="submit">
                        <button type="submit" class="button button-primary">Activate License</button>
                    </p>
                </form>
            <?php else: ?>
                <div class="license-active-info">
                    <p><strong>License is active</strong></p>
                    <button id="deactivate-license" class="button button-secondary">Deactivate License</button>
                </div>
            <?php endif; ?>
        </div>
    </div>
    
    <!-- Protection Tools -->
    <div class="setia-protection-section">
        <h2>Protection Tools</h2>
        <div class="setia-protection-tools">
            <div class="tool-card">
                <h3>Create Protected Distribution</h3>
                <p>Generate a production-ready, protected version of the plugin for distribution.</p>
                <button id="create-protected-dist" class="button button-primary">Create Protected Package</button>
                <div id="protection-progress" style="display: none;">
                    <div class="progress-bar">
                        <div class="progress-fill"></div>
                    </div>
                    <p class="progress-text">Creating protected distribution...</p>
                </div>
            </div>
            
            <div class="tool-card">
                <h3>Verify File Integrity</h3>
                <p>Check all protected files for tampering or unauthorized modifications.</p>
                <button id="verify-integrity" class="button button-secondary">Verify Integrity</button>
            </div>
            
            <div class="tool-card">
                <h3>Regenerate Protection</h3>
                <p>Re-apply all protection measures to the current installation.</p>
                <button id="regenerate-protection" class="button button-secondary">Regenerate Protection</button>
            </div>
        </div>
    </div>
    
    <!-- Security Logs -->
    <div class="setia-protection-section">
        <h2>Security Logs</h2>
        <div class="setia-security-logs">
            <div id="security-log-content">
                <p>Loading security logs...</p>
            </div>
            <button id="refresh-logs" class="button button-secondary">Refresh Logs</button>
        </div>
    </div>
    
    <!-- Advanced Settings -->
    <div class="setia-protection-section">
        <h2>Advanced Security Settings</h2>
        <form id="setia-security-settings-form">
            <table class="form-table">
                <tr>
                    <th scope="row">Auto-Protection</th>
                    <td>
                        <label>
                            <input type="checkbox" name="auto_protection" value="1" checked />
                            Automatically apply protection measures on plugin updates
                        </label>
                    </td>
                </tr>
                <tr>
                    <th scope="row">Integrity Check Frequency</th>
                    <td>
                        <select name="integrity_frequency">
                            <option value="hourly">Every Hour</option>
                            <option value="daily" selected>Daily</option>
                            <option value="weekly">Weekly</option>
                        </select>
                    </td>
                </tr>
                <tr>
                    <th scope="row">Security Notifications</th>
                    <td>
                        <label>
                            <input type="checkbox" name="security_notifications" value="1" checked />
                            Send email notifications for security events
                        </label>
                    </td>
                </tr>
            </table>
            
            <p class="submit">
                <button type="submit" class="button button-primary">Save Security Settings</button>
            </p>
        </form>
    </div>
</div>

<style>
.setia-protection-page {
    max-width: 1200px;
}

.setia-protection-overview {
    margin: 20px 0;
}

.setia-status-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.setia-status-card {
    background: white;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.status-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.status-header h3 {
    margin: 0;
    font-size: 16px;
}

.status-indicator {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: bold;
    text-transform: uppercase;
}

.status-indicator.green {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.status-indicator.red {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.status-indicator.gray {
    background: #e2e3e5;
    color: #383d41;
    border: 1px solid #d6d8db;
}

.setia-protection-section {
    background: white;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 20px;
    margin: 20px 0;
}

.setia-protection-section h2 {
    margin-top: 0;
    border-bottom: 1px solid #eee;
    padding-bottom: 10px;
}

.setia-protection-tools {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.tool-card {
    border: 1px solid #ddd;
    border-radius: 6px;
    padding: 15px;
    background: #f9f9f9;
}

.tool-card h3 {
    margin-top: 0;
    margin-bottom: 10px;
}

.tool-card p {
    margin-bottom: 15px;
    color: #666;
}

.progress-bar {
    width: 100%;
    height: 20px;
    background: #f0f0f0;
    border-radius: 10px;
    overflow: hidden;
    margin: 10px 0;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #667eea, #764ba2);
    width: 0%;
    transition: width 0.3s ease;
    animation: progress-animation 2s infinite;
}

@keyframes progress-animation {
    0% { width: 0%; }
    50% { width: 70%; }
    100% { width: 100%; }
}

.setia-security-logs {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    padding: 15px;
    max-height: 300px;
    overflow-y: auto;
}

#security-log-content {
    font-family: monospace;
    font-size: 12px;
    line-height: 1.4;
}

.license-active-info {
    padding: 15px;
    background: #d4edda;
    border: 1px solid #c3e6cb;
    border-radius: 6px;
    color: #155724;
}
</style>

<script>
jQuery(document).ready(function($) {
    // License activation
    $('#setia-license-activation-form').on('submit', function(e) {
        e.preventDefault();
        
        const $form = $(this);
        const $button = $form.find('button[type="submit"]');
        const originalText = $button.text();
        
        $button.text('Activating...').prop('disabled', true);
        
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'setia_activate_license',
                nonce: '<?php echo wp_create_nonce('setia_license_nonce'); ?>',
                license_key: $('#license_key').val(),
                email: $('#license_email').val()
            },
            success: function(response) {
                if (response.success) {
                    alert('License activated successfully!');
                    location.reload();
                } else {
                    alert('License activation failed: ' + response.data);
                }
            },
            error: function() {
                alert('License activation failed. Please try again.');
            },
            complete: function() {
                $button.text(originalText).prop('disabled', false);
            }
        });
    });
    
    // License deactivation
    $('#deactivate-license').on('click', function() {
        if (!confirm('Are you sure you want to deactivate the license?')) {
            return;
        }
        
        const $button = $(this);
        $button.text('Deactivating...').prop('disabled', true);
        
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'setia_deactivate_license',
                nonce: '<?php echo wp_create_nonce('setia_license_nonce'); ?>'
            },
            success: function(response) {
                if (response.success) {
                    alert('License deactivated successfully!');
                    location.reload();
                } else {
                    alert('License deactivation failed: ' + response.data);
                }
            },
            complete: function() {
                $button.text('Deactivate License').prop('disabled', false);
            }
        });
    });
    
    // Create protected distribution
    $('#create-protected-dist').on('click', function() {
        const $button = $(this);
        const $progress = $('#protection-progress');
        
        $button.prop('disabled', true);
        $progress.show();
        
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'setia_create_protected_distribution',
                nonce: '<?php echo wp_create_nonce('setia_deployment_nonce'); ?>'
            },
            success: function(response) {
                if (response.success) {
                    alert('Protected distribution created successfully!');
                } else {
                    alert('Failed to create protected distribution: ' + response.data);
                }
            },
            error: function() {
                alert('Failed to create protected distribution. Please try again.');
            },
            complete: function() {
                $button.prop('disabled', false);
                $progress.hide();
            }
        });
    });
});
</script>
