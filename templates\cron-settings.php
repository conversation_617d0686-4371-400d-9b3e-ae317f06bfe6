<?php
// امنیت: جلوگیری از دسترسی مستقیم
if (!defined('ABSPATH')) {
    exit;
}

// ذخیره تنظیمات
if (isset($_POST['setia_save_cron_settings'])) {
    if (wp_verify_nonce($_POST['setia_cron_nonce'], 'setia_cron_settings')) {
        // ذخیره تنظیمات
        $security_key = sanitize_text_field($_POST['setia_cron_security_key']);
        update_option('setia_cron_security_key', $security_key);
        
        // پیام موفقیت
        echo '<div class="notice notice-success is-dismissible"><p>تنظیمات با موفقیت ذخیره شد.</p></div>';
    }
}

// دریافت تنظیمات فعلی
$security_key = get_option('setia_cron_security_key', '');
if (empty($security_key)) {
    // ایجاد کلید امنیتی تصادفی
    $security_key = wp_generate_password(32, false);
    update_option('setia_cron_security_key', $security_key);
}

// آدرس فایل کرون تریگر
$cron_url = plugins_url('cron-trigger.php', dirname(__FILE__));
$cron_url_with_key = add_query_arg('key', $security_key, $cron_url);

// بررسی وضعیت DISABLE_WP_CRON
$wp_config_path = ABSPATH . 'wp-config.php';
$wp_cron_disabled = false;

if (file_exists($wp_config_path)) {
    $wp_config_content = file_get_contents($wp_config_path);
    if (strpos($wp_config_content, "define('DISABLE_WP_CRON', true)") !== false || strpos($wp_config_content, "define('DISABLE_WP_CRON', TRUE)") !== false) {
        $wp_cron_disabled = true;
    }
}
?>

<div class="wrap">
    <h1>تنظیمات کرون سرور واقعی</h1>
    
    <div class="setia-notice setia-info-notice">
        <p>
            <strong>توضیحات:</strong> برای اجرای خودکار زمانبندی‌ها بدون نیاز به بازدید کاربران، باید کرون سرور واقعی را تنظیم کنید.
            این روش بسیار مطمئن‌تر از کرون وردپرس است و زمانبندی‌ها را حتی بدون بازدید کاربران اجرا می‌کند.
        </p>
    </div>
    
    <?php if (!$wp_cron_disabled): ?>
    <div class="setia-notice setia-warning-notice">
        <p>
            <strong>هشدار:</strong> کرون وردپرس هنوز فعال است. برای استفاده از کرون سرور واقعی، باید کرون وردپرس را غیرفعال کنید.
            برای این کار، خط زیر را به فایل wp-config.php اضافه کنید:
            <br>
            <code>define('DISABLE_WP_CRON', true);</code>
        </p>
    </div>
    <?php else: ?>
    <div class="setia-notice setia-success-notice">
        <p>
            <strong>تبریک:</strong> کرون وردپرس با موفقیت غیرفعال شده است.
        </p>
    </div>
    <?php endif; ?>
    
    <form method="post" action="">
        <?php wp_nonce_field('setia_cron_settings', 'setia_cron_nonce'); ?>
        
        <table class="form-table">
            <tr>
                <th scope="row">کلید امنیتی</th>
                <td>
                    <input type="text" name="setia_cron_security_key" value="<?php echo esc_attr($security_key); ?>" class="regular-text" />
                    <p class="description">این کلید برای امنیت بیشتر استفاده می‌شود. می‌توانید آن را تغییر دهید یا همین کلید تصادفی را استفاده کنید.</p>
                </td>
            </tr>
        </table>
        
        <p class="submit">
            <input type="submit" name="setia_save_cron_settings" class="button button-primary" value="ذخیره تنظیمات" />
        </p>
    </form>
    
    <h2>راهنمای تنظیم کرون سرور</h2>
    
    <div class="setia-card">
        <h3>گام 1: غیرفعال کردن کرون وردپرس</h3>
        <p>خط زیر را به فایل wp-config.php اضافه کنید:</p>
        <pre><code>define('DISABLE_WP_CRON', true);</code></pre>
    </div>
    
    <div class="setia-card">
        <h3>گام 2: تنظیم کرون سرور لینوکس (Crontab)</h3>
        <p>در سرور لینوکس، دستور زیر را اجرا کنید تا کرون تب را ویرایش کنید:</p>
        <pre><code>crontab -e</code></pre>
        <p>سپس خط زیر را اضافه کنید (برای اجرای هر 5 دقیقه):</p>
        <pre><code>*/5 * * * * wget -q -O /dev/null "<?php echo esc_url($cron_url_with_key); ?>" > /dev/null 2>&1</code></pre>
        <p>یا با استفاده از curl:</p>
        <pre><code>*/5 * * * * curl -s "<?php echo esc_url($cron_url_with_key); ?>" > /dev/null 2>&1</code></pre>
    </div>
    
    <div class="setia-card">
        <h3>گام 3: تنظیم کرون در سی‌پنل (cPanel)</h3>
        <p>1. وارد سی‌پنل شوید</p>
        <p>2. به بخش "Cron Jobs" بروید</p>
        <p>3. در بخش "Add New Cron Job":</p>
        <ul>
            <li>Common Settings: Every 5 minutes را انتخاب کنید</li>
            <li>Command: دستور زیر را وارد کنید</li>
        </ul>
        <pre><code>wget -q -O /dev/null "<?php echo esc_url($cron_url_with_key); ?>" > /dev/null 2>&1</code></pre>
        <p>یا:</p>
        <pre><code>curl -s "<?php echo esc_url($cron_url_with_key); ?>" > /dev/null 2>&1</code></pre>
        <p>4. روی "Add New Cron Job" کلیک کنید</p>
    </div>
    
    <div class="setia-card">
        <h3>تست کرون</h3>
        <p>برای تست کرون، روی لینک زیر کلیک کنید:</p>
        <p><a href="<?php echo esc_url($cron_url_with_key); ?>" target="_blank" class="button">اجرای دستی کرون</a></p>
        <p>اگر پیام "SETIA CRON: کرون با موفقیت اجرا شد" را مشاهده کردید، تنظیمات درست است.</p>
    </div>
</div>

<style>
.setia-notice {
    background-color: #fff;
    border-left: 4px solid #ccc;
    box-shadow: 0 1px 1px rgba(0,0,0,.04);
    margin: 0 0 20px;
    padding: 10px 12px;
}

.setia-info-notice {
    border-left-color: #2271b1;
}

.setia-warning-notice {
    border-left-color: #dba617;
}

.setia-success-notice {
    border-left-color: #46b450;
}

.setia-card {
    background-color: #fff;
    border: 1px solid #ccd0d4;
    box-shadow: 0 1px 1px rgba(0,0,0,.04);
    margin: 0 0 20px;
    padding: 10px 20px;
}

.setia-card h3 {
    margin-top: 0;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
}

pre {
    background-color: #f6f7f7;
    padding: 10px;
    overflow: auto;
    border-radius: 3px;
}

code {
    background-color: #f6f7f7;
    padding: 2px 5px;
    border-radius: 3px;
}
</style> 