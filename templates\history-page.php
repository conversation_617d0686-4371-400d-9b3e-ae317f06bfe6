<?php
/**
 * صفحه تاریخچه تولید محتوا
 * SETIA Content Generator Plugin
 */

// جلوگیری از دسترسی مستقیم
if (!defined('ABSPATH')) {
    exit;
}
?>

<div class="wrap setia-history-wrap">
    <div class="setia-header">
        <h1 class="setia-title">
            <span class="setia-icon">📊</span>
            تاریخچه تولید محتوا
        </h1>
        <p class="setia-subtitle">مشاهده و مدیریت تاریخچه کامل محتوای تولید شده با هوش مصنوعی</p>
    </div>

    <div class="setia-main-container">
        <!-- فیلترها و جستجو -->
        <div class="setia-card">
            <div class="setia-card-header">
                <h3>فیلتر و جستجو</h3>
                <button id="export-history" class="setia-button setia-button-outline">
                    <span class="dashicons dashicons-download"></span>
                    خروجی Excel
                </button>
            </div>
            
            <div class="setia-card-body">
                <div class="setia-filters-grid">
                    <div class="setia-form-group">
                        <label for="date-from">از تاریخ:</label>
                        <input type="date" id="date-from" class="setia-input">
                    </div>
                    
                    <div class="setia-form-group">
                        <label for="date-to">تا تاریخ:</label>
                        <input type="date" id="date-to" class="setia-input">
                    </div>
                    
                    <div class="setia-form-group">
                        <label for="content-type-filter">نوع محتوا:</label>
                        <select id="content-type-filter" class="setia-select">
                            <option value="all">همه انواع</option>
                            <option value="article">مقاله</option>
                            <option value="product">محصول</option>
                            <option value="page">صفحه</option>
                        </select>
                    </div>
                    
                    <div class="setia-form-group">
                        <label for="status-filter">وضعیت:</label>
                        <select id="status-filter" class="setia-select">
                            <option value="all">همه وضعیت‌ها</option>
                            <option value="published">منتشر شده</option>
                            <option value="draft">پیش‌نویس</option>
                            <option value="failed">ناموفق</option>
                        </select>
                    </div>
                    
                    <div class="setia-form-group">
                        <label for="search-keyword">جستجوی کلیدواژه:</label>
                        <input type="text" id="search-keyword" class="setia-input" placeholder="عنوان، کلیدواژه یا محتوا...">
                    </div>
                    
                    <div class="setia-form-group">
                        <label>&nbsp;</label>
                        <button id="apply-filters" class="setia-button setia-button-primary">
                            <span class="dashicons dashicons-search"></span>
                            اعمال فیلتر
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- آمار کلی -->
        <div class="setia-stats-grid">
            <div class="setia-stat-card">
                <div class="setia-stat-icon">📝</div>
                <div class="setia-stat-content">
                    <h4>کل محتوای تولید شده</h4>
                    <span class="setia-stat-number">127</span>
                    <span class="setia-stat-change">+12 این ماه</span>
                </div>
            </div>
            
            <div class="setia-stat-card">
                <div class="setia-stat-icon">✅</div>
                <div class="setia-stat-content">
                    <h4>محتوای منتشر شده</h4>
                    <span class="setia-stat-number">98</span>
                    <span class="setia-stat-change">77% نرخ انتشار</span>
                </div>
            </div>
            
            <div class="setia-stat-card">
                <div class="setia-stat-icon">🛒</div>
                <div class="setia-stat-content">
                    <h4>محصولات WooCommerce</h4>
                    <span class="setia-stat-number">45</span>
                    <span class="setia-stat-change">+8 این هفته</span>
                </div>
            </div>
            
            <div class="setia-stat-card">
                <div class="setia-stat-icon">⏱️</div>
                <div class="setia-stat-content">
                    <h4>میانگین زمان تولید</h4>
                    <span class="setia-stat-number">2.3 دقیقه</span>
                    <span class="setia-stat-change">بهبود 15%</span>
                </div>
            </div>
        </div>

        <!-- جدول تاریخچه -->
        <div class="setia-card">
            <div class="setia-card-header">
                <h3>لیست محتوای تولید شده</h3>
                <div class="setia-card-actions">
                    <select id="items-per-page" class="setia-select">
                        <option value="10">10 مورد در صفحه</option>
                        <option value="25" selected>25 مورد در صفحه</option>
                        <option value="50">50 مورد در صفحه</option>
                        <option value="100">100 مورد در صفحه</option>
                    </select>
                </div>
            </div>
            
            <div class="setia-table-container">
                <table class="setia-history-table">
                    <thead>
                        <tr>
                            <th>عنوان</th>
                            <th>نوع</th>
                            <th>وضعیت</th>
                            <th>تاریخ تولید</th>
                            <th>کلیدواژه اصلی</th>
                            <th>تعداد کلمات</th>
                            <th>عملیات</th>
                        </tr>
                    </thead>
                    <tbody id="history-table-body">
                        <tr>
                            <td>
                                <strong>راهنمای خرید گوشی هوشمند 2024</strong>
                                <small>ID: 1234</small>
                            </td>
                            <td><span class="setia-type-badge article">مقاله</span></td>
                            <td><span class="setia-status-badge published">منتشر شده</span></td>
                            <td>1403/04/12 - 14:30</td>
                            <td>گوشی هوشمند</td>
                            <td>1,250</td>
                            <td>
                                <div class="setia-action-buttons">
                                    <button class="setia-button setia-button-small setia-button-outline" onclick="viewContent(1234)">مشاهده</button>
                                    <button class="setia-button setia-button-small setia-button-secondary" onclick="editContent(1234)">ویرایش</button>
                                    <button class="setia-button setia-button-small setia-button-danger" onclick="deleteContent(1234)">حذف</button>
                                </div>
                            </td>
                        </tr>
                        
                        <tr>
                            <td>
                                <strong>کیف چرمی مردانه کلاسیک</strong>
                                <small>ID: 1233</small>
                            </td>
                            <td><span class="setia-type-badge product">محصول</span></td>
                            <td><span class="setia-status-badge published">منتشر شده</span></td>
                            <td>1403/04/12 - 13:15</td>
                            <td>کیف چرمی</td>
                            <td>850</td>
                            <td>
                                <div class="setia-action-buttons">
                                    <button class="setia-button setia-button-small setia-button-outline" onclick="viewContent(1233)">مشاهده</button>
                                    <button class="setia-button setia-button-small setia-button-secondary" onclick="editContent(1233)">ویرایش</button>
                                    <button class="setia-button setia-button-small setia-button-danger" onclick="deleteContent(1233)">حذف</button>
                                </div>
                            </td>
                        </tr>
                        
                        <tr>
                            <td>
                                <strong>آموزش نصب ورد پرس</strong>
                                <small>ID: 1232</small>
                            </td>
                            <td><span class="setia-type-badge article">مقاله</span></td>
                            <td><span class="setia-status-badge draft">پیش‌نویس</span></td>
                            <td>1403/04/12 - 12:00</td>
                            <td>ورد پرس</td>
                            <td>2,100</td>
                            <td>
                                <div class="setia-action-buttons">
                                    <button class="setia-button setia-button-small setia-button-outline" onclick="viewContent(1232)">مشاهده</button>
                                    <button class="setia-button setia-button-small setia-button-secondary" onclick="editContent(1232)">ویرایش</button>
                                    <button class="setia-button setia-button-small setia-button-primary" onclick="publishContent(1232)">انتشار</button>
                                </div>
                            </td>
                        </tr>
                        
                        <tr>
                            <td>
                                <strong>بررسی لپ‌تاپ گیمینگ</strong>
                                <small>ID: 1231</small>
                            </td>
                            <td><span class="setia-type-badge article">مقاله</span></td>
                            <td><span class="setia-status-badge failed">ناموفق</span></td>
                            <td>1403/04/12 - 11:30</td>
                            <td>لپ‌تاپ گیمینگ</td>
                            <td>0</td>
                            <td>
                                <div class="setia-action-buttons">
                                    <button class="setia-button setia-button-small setia-button-outline" onclick="viewError(1231)">مشاهده خطا</button>
                                    <button class="setia-button setia-button-small setia-button-primary" onclick="retryGeneration(1231)">تولید مجدد</button>
                                    <button class="setia-button setia-button-small setia-button-danger" onclick="deleteContent(1231)">حذف</button>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            
            <!-- صفحه‌بندی -->
            <div class="setia-pagination">
                <div class="setia-pagination-info">
                    نمایش 1 تا 25 از 127 مورد
                </div>
                <div class="setia-pagination-controls">
                    <button class="setia-button setia-button-outline" disabled>قبلی</button>
                    <span class="setia-page-numbers">
                        <button class="setia-page-btn active">1</button>
                        <button class="setia-page-btn">2</button>
                        <button class="setia-page-btn">3</button>
                        <span>...</span>
                        <button class="setia-page-btn">6</button>
                    </span>
                    <button class="setia-button setia-button-outline">بعدی</button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- مودال مشاهده محتوا -->
<div id="content-modal" class="setia-modal" style="display: none;">
    <div class="setia-modal-content">
        <div class="setia-modal-header">
            <h3 id="modal-title">مشاهده محتوا</h3>
            <span class="setia-modal-close">&times;</span>
        </div>
        <div class="setia-modal-body" id="modal-body">
            <!-- محتوا در اینجا بارگذاری می‌شود -->
        </div>
    </div>
</div>

<style>
.setia-history-wrap {
    direction: rtl;
    font-family: 'IRANSans', Tahoma, sans-serif;
}

.setia-filters-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    align-items: end;
}

.setia-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin: 20px 0;
}

.setia-stat-card {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.setia-stat-icon {
    font-size: 2.5em;
    opacity: 0.9;
}

.setia-stat-content h4 {
    margin: 0 0 5px 0;
    font-size: 14px;
    opacity: 0.9;
}

.setia-stat-number {
    display: block;
    font-size: 28px;
    font-weight: bold;
    margin-bottom: 5px;
}

.setia-stat-change {
    font-size: 12px;
    opacity: 0.8;
}

.setia-table-container {
    overflow-x: auto;
    margin: 20px 0;
}

.setia-history-table {
    width: 100%;
    border-collapse: collapse;
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.setia-history-table th {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 15px 10px;
    text-align: right;
    font-weight: 600;
    font-size: 14px;
}

.setia-history-table td {
    padding: 15px 10px;
    border-bottom: 1px solid #e1e8ed;
    vertical-align: middle;
}

.setia-history-table tr:hover {
    background: #f8f9fa;
}

.setia-type-badge, .setia-status-badge {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    text-align: center;
    display: inline-block;
    min-width: 70px;
}

.setia-type-badge.article {
    background: #e3f2fd;
    color: #1976d2;
}

.setia-type-badge.product {
    background: #f3e5f5;
    color: #7b1fa2;
}

.setia-status-badge.published {
    background: #d4edda;
    color: #155724;
}

.setia-status-badge.draft {
    background: #fff3cd;
    color: #856404;
}

.setia-status-badge.failed {
    background: #f8d7da;
    color: #721c24;
}

.setia-action-buttons {
    display: flex;
    gap: 5px;
    flex-wrap: wrap;
}

.setia-pagination {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 20px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
}

.setia-pagination-info {
    color: #666;
    font-size: 14px;
}

.setia-pagination-controls {
    display: flex;
    align-items: center;
    gap: 10px;
}

.setia-page-numbers {
    display: flex;
    gap: 5px;
    align-items: center;
}

.setia-page-btn {
    padding: 8px 12px;
    border: 1px solid #ddd;
    background: white;
    color: #333;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.setia-page-btn:hover {
    background: #667eea;
    color: white;
    border-color: #667eea;
}

.setia-page-btn.active {
    background: #667eea;
    color: white;
    border-color: #667eea;
}

.setia-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    z-index: 10000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.setia-modal-content {
    background: white;
    border-radius: 12px;
    max-width: 800px;
    width: 90%;
    max-height: 80%;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
}

.setia-modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.setia-modal-header h3 {
    margin: 0;
}

.setia-modal-close {
    font-size: 24px;
    cursor: pointer;
    opacity: 0.8;
    transition: opacity 0.3s;
}

.setia-modal-close:hover {
    opacity: 1;
}

.setia-modal-body {
    padding: 20px;
    max-height: 400px;
    overflow-y: auto;
}

@media (max-width: 768px) {
    .setia-filters-grid {
        grid-template-columns: 1fr;
    }

    .setia-stats-grid {
        grid-template-columns: 1fr;
    }

    .setia-action-buttons {
        flex-direction: column;
    }

    .setia-pagination {
        flex-direction: column;
        gap: 15px;
    }
}
</style>

<script>
jQuery(document).ready(function($) {
    // اعمال فیلترها
    $('#apply-filters').on('click', function() {
        var dateFrom = $('#date-from').val();
        var dateTo = $('#date-to').val();
        var contentType = $('#content-type-filter').val();
        var status = $('#status-filter').val();
        var keyword = $('#search-keyword').val();

        // اینجا باید AJAX call برای فیلتر کردن داده‌ها باشد
        alert('فیلترها اعمال شد. (در نسخه نهایی با AJAX پیاده‌سازی می‌شود)');
    });

    // خروجی Excel
    $('#export-history').on('click', function() {
        alert('خروجی Excel در حال آماده‌سازی است...');
    });

    // بستن مودال
    $('.setia-modal-close').on('click', function() {
        $('.setia-modal').hide();
    });

    // بستن مودال با کلیک روی پس‌زمینه
    $('.setia-modal').on('click', function(e) {
        if (e.target === this) {
            $(this).hide();
        }
    });

    // تغییر تعداد آیتم‌ها در صفحه
    $('#items-per-page').on('change', function() {
        var itemsPerPage = $(this).val();
        alert('تعداد آیتم‌ها به ' + itemsPerPage + ' تغییر یافت.');
    });
});

// توابع عملیات
function viewContent(id) {
    $('#modal-title').text('مشاهده محتوا - ID: ' + id);
    $('#modal-body').html('<p>در حال بارگذاری محتوا...</p>');
    $('#content-modal').show();

    // شبیه‌سازی بارگذاری محتوا
    setTimeout(function() {
        $('#modal-body').html('<h4>عنوان: راهنمای خرید گوشی هوشمند 2024</h4><p>این یک نمونه محتوای تولید شده است...</p>');
    }, 1000);
}

function editContent(id) {
    if (confirm('آیا می‌خواهید این محتوا را ویرایش کنید؟')) {
        window.open('/wp-admin/post.php?post=' + id + '&action=edit', '_blank');
    }
}

function deleteContent(id) {
    if (confirm('آیا مطمئن هستید که می‌خواهید این محتوا را حذف کنید؟')) {
        alert('محتوا حذف شد. (در نسخه نهایی با AJAX پیاده‌سازی می‌شود)');
    }
}

function publishContent(id) {
    if (confirm('آیا می‌خواهید این محتوا را منتشر کنید؟')) {
        alert('محتوا منتشر شد.');
    }
}

function viewError(id) {
    $('#modal-title').text('جزئیات خطا - ID: ' + id);
    $('#modal-body').html('<div style="color: #dc3545;"><h4>خطای رخ داده:</h4><p>API Key نامعتبر است. لطفاً کلید API خود را بررسی کنید.</p></div>');
    $('#content-modal').show();
}

function retryGeneration(id) {
    if (confirm('آیا می‌خواهید تولید محتوا را مجدداً تلاش کنید؟')) {
        alert('تولید مجدد محتوا شروع شد...');
    }
}
</script>
