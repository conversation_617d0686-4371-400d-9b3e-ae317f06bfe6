<?php
// امنیت: جلوگیری از دسترسی مستقیم
if (!defined('ABSPATH')) {
    exit;
}

// گرفتن تاریخچه محتوای تولید شده
global $wpdb;
$table_name = $wpdb->prefix . 'setia_generated_content';
$items_per_page = 10;
$current_page = isset($_GET['paged']) ? max(1, intval($_GET['paged'])) : 1;
$offset = ($current_page - 1) * $items_per_page;

// تعداد کل رکوردها
$total_items = $wpdb->get_var("SELECT COUNT(*) FROM $table_name");
$total_pages = ceil($total_items / $items_per_page);

// گرفتن محتوای تولید شده با پیجینیشن
$history_items = $wpdb->get_results(
    $wpdb->prepare(
        "SELECT * FROM $table_name ORDER BY created_at DESC LIMIT %d OFFSET %d",
        $items_per_page,
        $offset
    )
);

// اضافه کردن متادستور برای جلوگیری از کش شدن
echo '<meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate" />';
echo '<meta http-equiv="Pragma" content="no-cache" />';
echo '<meta http-equiv="Expires" content="0" />';

// حذف لینک‌های فونت آیکون که با فونت ایران‌سنس تداخل دارند
// echo '<link rel="stylesheet" href="' . includes_url('css/dashicons.min.css') . '" type="text/css" media="all" />';
// echo '<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" />';
?>

<style>
:root {
    --setia-primary: #4a6bef;
    --setia-primary-dark: #3854c8;
    --setia-secondary: #6c49b8;
    --setia-accent: #ff7043;
    --setia-success: #43a047;
    --setia-warning: #ff9800;
    --setia-error: #f44336;
    --setia-gray-100: #f5f5f5;
    --setia-gray-200: #eeeeee;
    --setia-gray-300: #e0e0e0;
    --setia-gray-400: #bdbdbd;
    --setia-text-dark: #202124;
    --setia-text-medium: #5f6368;
    --setia-text-light: #80868b;
    --setia-gradient-primary: linear-gradient(135deg, var(--setia-primary) 0%, var(--setia-secondary) 100%);
    --setia-box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    --setia-border-radius: 8px;
}

/* اطمینان از نمایش صحیح آیکون‌ها */
.dashicons, .dashicons-before:before {
    font-family: dashicons !important;
    display: inline-block;
    line-height: 1;
    font-weight: 400;
    font-style: normal;
    text-decoration: inherit;
    text-transform: none;
    text-rendering: auto;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    width: 20px;
    height: 20px;
    font-size: 20px;
    vertical-align: middle;
    text-align: center;
}

.wrap.setia-history-page {
    max-width: 1300px;
    margin: 20px auto;
    background-color: white;
    padding: 25px;
    border-radius: var(--setia-border-radius);
    box-shadow: var(--setia-box-shadow);
    position: relative;
    overflow: hidden;
}

.wrap.setia-history-page::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 5px;
    background: var(--setia-gradient-primary);
}

.setia-history-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
    padding-bottom: 15px;
    border-bottom: 1px solid var(--setia-gray-300);
}

.setia-history-header h1 {
    color: var(--setia-primary);
    display: flex;
    align-items: center;
    gap: 10px;
    margin: 0;
    font-size: 24px;
}

.setia-history-header h1 .dashicons {
    font-size: 28px;
    width: 28px;
    height: 28px;
}

.setia-card {
    background-color: white;
    border-radius: var(--setia-border-radius);
    box-shadow: var(--setia-box-shadow);
    margin-bottom: 20px;
    overflow: hidden;
}

.setia-card-header {
    padding: 15px 20px;
    background: var(--setia-gray-100);
    border-bottom: 1px solid var(--setia-gray-300);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.setia-card-title {
    margin: 0;
    color: var(--setia-text-dark);
    font-size: 16px;
    font-weight: 600;
}

.setia-bulk-actions {
    display: flex;
    align-items: center;
    gap: 10px;
}

.setia-history-table {
    width: 100%;
    border-collapse: collapse;
}

.setia-history-table th {
    background-color: var(--setia-gray-100);
    color: var(--setia-text-dark);
    text-align: right;
    padding: 12px 15px;
    font-weight: 600;
    border-bottom: 2px solid var(--setia-gray-300);
}

.setia-history-table td {
    padding: 12px 15px;
    border-bottom: 1px solid var(--setia-gray-200);
    vertical-align: middle;
}

.setia-history-table tr:hover {
    background-color: rgba(74, 107, 239, 0.05);
}

.setia-history-table tr:last-child td {
    border-bottom: none;
}

.setia-checkbox {
    position: relative;
    width: 18px;
    height: 18px;
    cursor: pointer;
    margin: 0 auto;
    display: block;
}

.setia-checkbox:checked {
    accent-color: var(--setia-primary);
}

.setia-history-row-id {
    text-align: center;
    font-weight: bold;
    color: var(--setia-primary);
}

.setia-tag {
    display: inline-block;
    padding: 3px 8px;
    border-radius: 15px;
    font-size: 12px;
    background-color: var(--setia-gray-200);
    color: var(--setia-text-medium);
    margin-right: 5px;
    margin-bottom: 5px;
}

.setia-tag-tone {
    background-color: rgba(74, 107, 239, 0.1);
    color: var(--setia-primary);
}

.setia-tag-length {
    background-color: rgba(108, 73, 184, 0.1);
    color: var(--setia-secondary);
}

.setia-tag-category {
    background-color: rgba(255, 112, 67, 0.1);
    color: var(--setia-accent);
}

.setia-status {
    display: inline-flex;
    align-items: center;
    padding: 3px 8px;
    border-radius: 15px;
    font-size: 12px;
    font-weight: 600;
    gap: 5px;
}

.setia-status-published {
    background-color: rgba(67, 160, 71, 0.1);
    color: var(--setia-success);
}

.setia-status-draft {
    background-color: rgba(255, 152, 0, 0.1);
    color: var(--setia-warning);
}

.setia-status-unpublished {
    background-color: rgba(189, 189, 189, 0.3);
    color: var(--setia-text-medium);
}

.setia-actions {
    display: flex;
    gap: 8px;
    flex-wrap: nowrap;
    justify-content: flex-end;
    align-items: center;
}

.setia-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 5px;
    padding: 8px;
    border-radius: var(--setia-border-radius);
    border: none;
    font-size: 13px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
    text-decoration: none;
    min-width: 36px;
    height: 36px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    box-sizing: border-box;
    line-height: 1;
}

/* سبک‌های مخصوص لینک‌ها */
a.setia-btn {
    display: inline-flex;
    line-height: normal;
    box-sizing: border-box;
    text-decoration: none;
    align-items: center;
    justify-content: center;
}

.setia-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.setia-btn:active {
    transform: translateY(0);
}

.setia-btn .setia-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 16px;
    height: 16px;
}

.setia-btn .setia-icon svg {
    width: 16px;
    height: 16px;
    fill: currentColor;
}

.setia-btn-text {
    display: none; /* Hide the text */
}

.setia-btn-tooltip {
    position: relative;
}

.setia-btn-tooltip:hover::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: -30px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 12px;
    white-space: nowrap;
    z-index: 100;
}

.setia-btn-view {
    background-color: var(--setia-gray-100);
    color: var(--setia-text-dark);
}

.setia-btn-view:hover {
    background-color: var(--setia-gray-200);
}

.setia-btn-publish {
    background-color: var(--setia-primary);
    color: white;
}

.setia-btn-publish:hover {
    background-color: var(--setia-primary-dark);
}

.setia-btn-draft {
    background-color: var(--setia-gray-300);
    color: var(--setia-text-dark);
}

.setia-btn-edit {
    background-color: var(--setia-secondary);
    color: white;
}

.setia-btn-delete {
    background-color: rgba(244, 67, 54, 0.1);
    color: var(--setia-error);
}

.setia-btn-delete:hover {
    background-color: var(--setia-error);
    color: white;
}

.setia-bulk-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 8px 15px;
    border-radius: var(--setia-border-radius);
    border: 1px solid var(--setia-gray-300);
    background-color: white;
    color: var(--setia-text-dark);
    cursor: pointer;
    transition: all 0.2s;
    font-size: 13px;
    font-weight: 500;
}

.setia-bulk-btn:hover {
    background-color: var(--setia-gray-100);
    transform: translateY(-2px);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.setia-bulk-btn:active {
    transform: translateY(0);
}

.setia-bulk-btn .dashicons {
    font-size: 16px;
    width: 16px;
    height: 16px;
}

.setia-bulk-delete {
    background-color: rgba(244, 67, 54, 0.1);
    color: var(--setia-error);
    border-color: var(--setia-error);
}

.setia-bulk-delete:hover {
    background-color: var(--setia-error);
    color: white;
}

.setia-select-all {
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 13px;
    color: var(--setia-text-medium);
    cursor: pointer;
}

.setia-select-all:hover {
    color: var(--setia-primary);
}

.setia-pagination {
    margin-top: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    background-color: var(--setia-gray-100);
    border-radius: var(--setia-border-radius);
}

.setia-pagination-info {
    color: var(--setia-text-medium);
    font-size: 14px;
}

.setia-pagination-links {
    display: flex;
    gap: 5px;
}

.setia-pagination-links a,
.setia-pagination-links span {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border-radius: var(--setia-border-radius);
    color: var(--setia-text-medium);
    background-color: white;
    border: 1px solid var(--setia-gray-300);
    text-decoration: none;
    transition: all 0.2s;
}

.setia-pagination-links a:hover {
    background-color: var(--setia-gray-200);
}

.setia-pagination-links .current {
    background-color: var(--setia-primary);
    color: white;
    border-color: var(--setia-primary);
}

.setia-no-history {
    padding: 40px;
    text-align: center;
    background-color: var(--setia-gray-100);
    border-radius: var(--setia-border-radius);
    color: var(--setia-text-medium);
}

.setia-no-history p {
    margin: 0;
    font-size: 16px;
}

.setia-no-history .dashicons {
    font-size: 48px;
    width: 48px;
    height: 48px;
    margin-bottom: 10px;
    color: var(--setia-gray-400);
}

/* Modal styles */
.setia-modal {
    position: fixed;
    z-index: 99999; /* افزایش z-index برای اطمینان از نمایش روی همه المان‌ها */
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
}

.setia-modal-content {
    background-color: white;
    margin: 5% auto;
    padding: 25px;
    border-radius: var(--setia-border-radius);
    box-shadow: var(--setia-box-shadow);
    width: 80%;
    max-width: 1000px;
    max-height: 80vh;
    overflow-y: auto;
    position: relative;
    animation: fadeIn 0.3s ease-in-out;
}

.setia-modal-close {
    position: absolute;
    top: 15px;
    right: 20px;
    color: var(--setia-text-light);
    font-size: 24px;
    cursor: pointer;
    transition: all 0.2s;
}

.setia-modal-close:hover {
    color: var(--setia-error);
}

#setia-modal-title {
    color: var(--setia-primary);
    font-size: 24px;
    margin-top: 0;
    margin-bottom: 20px;
    border-bottom: 1px solid var(--setia-gray-300);
    padding-bottom: 15px;
}

#setia-modal-content {
    background-color: var(--setia-gray-100);
    padding: 20px;
    border-radius: var(--setia-border-radius);
    margin-bottom: 20px;
    max-height: 300px;
    overflow-y: auto;
    line-height: 1.6;
}

.setia-modal-section {
    margin-top: 25px;
    background-color: white;
    border: 1px solid var(--setia-gray-300);
    border-radius: var(--setia-border-radius);
    overflow: hidden;
}

.setia-modal-section-header {
    background-color: var(--setia-gray-100);
    padding: 10px 15px;
    border-bottom: 1px solid var(--setia-gray-300);
    display: flex;
    align-items: center;
    gap: 10px;
}

.setia-modal-section-header h3 {
    margin: 0;
    font-size: 16px;
    color: var(--setia-text-dark);
}

.setia-modal-section-content {
    padding: 15px;
}

.setia-modal-image-content img {
    max-width: 100%;
    max-height: 300px;
    display: block;
    margin: 0 auto;
    border-radius: var(--setia-border-radius);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.setia-fade-in {
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes slideInFromTop {
    from { transform: translateY(-30px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

.setia-slide-in {
    animation: slideInFromTop 0.4s ease-out;
}

@keyframes rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.rotating {
    animation: rotate 1.5s linear infinite;
}

.disabled {
    opacity: 0.7;
    pointer-events: none;
}

/* استایل‌های مربوط به آیکون‌های SVG */
.setia-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 20px;
    height: 20px;
}

.setia-icon svg {
    width: 100%;
    height: 100%;
    fill: currentColor;
}

.setia-status .setia-icon {
    margin-left: 4px;
}

/* انیمیشن آیکون برای حالت بارگذاری */
.setia-icon-spin svg {
    animation: rotate 1s linear infinite;
}

@keyframes rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}
</style>

<div class="wrap setia-history-page setia-fade-in">
    <div class="setia-history-header">
        <h1>
            <span class="setia-icon">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><path d="M75 75L41 41C25.9 25.9 0 36.6 0 57.9V168c0 13.3 10.7 24 24 24H134.1c21.4 0 32.1-25.9 17-41l-30.8-30.8C155 85.5 203 64 256 64c106 0 192 86 192 192s-86 192-192 192c-40.8 0-78.6-12.7-109.7-34.4c-14.5-10.1-34.4-6.6-44.6 7.9s-6.6 34.4 7.9 44.6C151.2 495 201.7 512 256 512c141.4 0 256-114.6 256-256S397.4 0 256 0C185.3 0 121.3 28.7 75 75zm181 53c-13.3 0-24 10.7-24 24V256c0 6.4 2.5 12.5 7 17l72 72c9.4 9.4 24.6 9.4 33.9 0s9.4-24.6 0-33.9l-65-65V152c0-13.3-10.7-24-24-24z"/></svg>
            </span> 
            تاریخچه محتوای تولید شده
        </h1>
    </div>
    
    <?php if (empty($history_items)): ?>
        <div class="setia-no-history">
            <span class="setia-icon" style="width: 48px; height: 48px;">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 384 512"><path d="M0 64C0 28.7 28.7 0 64 0H224V128c0 17.7 14.3 32 32 32H384V448c0 35.3-28.7 64-64 64H64c-35.3 0-64-28.7-64-64V64zm384 64H256V0L384 128z"/></svg>
            </span>
            <p>هنوز هیچ محتوایی تولید نشده است.</p>
        </div>
    <?php else: ?>
        <div class="setia-card setia-slide-in">
            <div class="setia-card-header">
                <h3 class="setia-card-title">لیست محتواهای تولید شده</h3>
                <div class="setia-bulk-actions">
                    <label class="setia-select-all">
                        <input type="checkbox" id="select-all-contents">
                        <span>انتخاب همه</span>
                    </label>
                    <button type="button" id="bulk-delete-btn" class="setia-bulk-btn setia-bulk-delete" disabled>
                        <span class="setia-icon">
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512"><path d="M135.2 17.7L128 32H32C14.3 32 0 46.3 0 64S14.3 96 32 96H416c17.7 0 32-14.3 32-32s-14.3-32-32-32H320l-7.2-14.3C307.4 6.8 296.3 0 284.2 0H163.8c-12.1 0-23.2 6.8-28.6 17.7zM416 128H32L53.2 467c1.6 25.3 22.6 45 47.9 45H346.9c25.3 0 46.3-19.7 47.9-45L416 128z"/></svg>
                        </span>
                        <span>حذف انتخاب شده‌ها</span>
                    </button>
                </div>
            </div>
            
            <table class="setia-history-table">
            <thead>
                <tr>
                        <th width="3%"></th>
                    <th width="5%">شناسه</th>
                        <th width="17%">موضوع</th>
                        <th width="17%">کلمات کلیدی</th>
                        <th width="7%">لحن</th>
                        <th width="7%">طول</th>
                        <th width="13%">وضعیت</th>
                        <th width="18%">تاریخ</th>
                        <th width="13%">عملیات</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($history_items as $item): ?>
                        <tr data-id="<?php echo esc_attr($item->id); ?>">
                            <td>
                                <input type="checkbox" class="setia-checkbox content-checkbox" data-id="<?php echo esc_attr($item->id); ?>">
                            </td>
                            <td class="setia-history-row-id"><?php echo esc_html($item->id); ?></td>
                        <td><?php echo esc_html($item->topic); ?></td>
                            <td>
                                <?php 
                                $keywords = explode(',', $item->keywords);
                                foreach (array_slice($keywords, 0, 2) as $keyword) {
                                    echo '<span class="setia-tag">' . esc_html(trim($keyword)) . '</span>';
                                }
                                if (count($keywords) > 2) {
                                    echo '<span class="setia-tag">+' . (count($keywords) - 2) . '</span>';
                                }
                                ?>
                            </td>
                            <td><span class="setia-tag setia-tag-tone"><?php echo esc_html($item->tone); ?></span></td>
                            <td><span class="setia-tag setia-tag-length"><?php echo esc_html($item->length); ?></span></td>
                        <td>
                            <?php 
                            if (!empty($item->post_id)) {
                                $post_status = get_post_status($item->post_id);
                                $status_label = '';
                                    $status_class = '';
                                    $status_svg = '';
                                
                                switch ($post_status) {
                                    case 'publish':
                                        $status_label = 'منتشر شده';
                                            $status_class = 'setia-status-published';
                                            $status_svg = '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><path d="M256 512A256 256 0 1 0 256 0a256 256 0 1 0 0 512zM369 209L241 337c-9.4 9.4-24.6 9.4-33.9 0l-64-64c-9.4-9.4-9.4-24.6 0-33.9s24.6-9.4 33.9 0l47 47L335 175c9.4-9.4 24.6-9.4 33.9 0s9.4 24.6 0 33.9z"/></svg>';
                                        break;
                                    case 'draft':
                                        $status_label = 'پیش‌نویس';
                                            $status_class = 'setia-status-draft';
                                            $status_svg = '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 576 512"><path d="M0 64C0 28.7 28.7 0 64 0H224V128c0 17.7 14.3 32 32 32H384V448c0 35.3-28.7 64-64 64H64c-35.3 0-64-28.7-64-64V64zm384 64H256V0L384 128z"/></svg>';
                                        break;
                                    default:
                                        $status_label = $post_status;
                                            $status_class = '';
                                            $status_svg = '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><path d="M256 512A256 256 0 1 0 256 0a256 256 0 1 0 0 512zM216 336h24V272H216c-13.3 0-24-10.7-24-24s10.7-24 24-24h48c13.3 0 24 10.7 24 24v88h8c13.3 0 24 10.7 24 24s-10.7 24-24 24H216c-13.3 0-24-10.7-24-24s10.7-24 24-24zm40-208a32 32 0 1 1 0 64 32 32 0 1 1 0-64z"/></svg>';
                                }
                                
                                    echo '<span class="setia-status ' . esc_attr($status_class) . '"><span class="setia-icon">' . $status_svg . '</span>' . esc_html($status_label) . '</span>';
                            } else {
                                    echo '<span class="setia-status setia-status-unpublished"><span class="setia-icon"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 384 512"><path d="M0 48V487.7C0 501.1 10.9 512 24.3 512c5 0 9.9-1.5 14-4.4L192 400 345.7 507.6c4.1 2.9 9 4.4 14 4.4c13.4 0 24.3-10.9 24.3-24.3V48c0-26.5-21.5-48-48-48H48C21.5 0 0 21.5 0 48z"/></svg></span>منتشر نشده</span>';
                            }
                            ?>
                        </td>
                        <td><?php echo date_i18n(get_option('date_format') . ' ' . get_option('time_format'), strtotime($item->created_at)); ?></td>
                        <td>
                                <div class="setia-actions">
                                    <button type="button" class="setia-btn setia-btn-view setia-view-content setia-btn-tooltip" data-tooltip="مشاهده" data-id="<?php echo esc_attr($item->id); ?>">
                                        <span class="setia-icon">
                                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 576 512"><path d="M288 32c-80.8 0-145.5 36.8-192.6 80.6C48.6 156 17.3 208 2.5 243.7c-3.3 7.9-3.3 16.7 0 24.6C17.3 304 48.6 356 95.4 399.4C142.5 443.2 207.2 480 288 480s145.5-36.8 192.6-80.6c46.8-43.5 78.1-95.4 93-131.1c3.3-7.9 3.3-16.7 0-24.6c-14.9-35.7-46.2-87.7-93-131.1C433.5 68.8 368.8 32 288 32zM144 256a144 144 0 1 1 288 0 144 144 0 1 1 -288 0zm144-64c0 35.3-28.7 64-64 64c-7.1 0-13.9-1.2-20.3-3.3c-5.5-1.8-11.9 1.6-11.7 7.4c.3 6.9 1.3 13.8 3.2 20.7c13.7 51.2 66.4 81.6 117.6 67.9s81.6-66.4 67.9-117.6c-11.1-41.5-47.8-69.4-88.6-71.1c-5.8-.2-9.2 6.1-7.4 11.7c2.1 6.4 3.3 13.2 3.3 20.3z"/></svg>
                                        </span>
                                        <span class="setia-btn-text">مشاهده</span>
                                    </button>
                            
                            <?php if (!empty($item->post_id)): ?>
                                        <a href="<?php echo get_edit_post_link($item->post_id); ?>" class="setia-btn setia-btn-edit setia-btn-tooltip" data-tooltip="ویرایش">
                                            <span class="setia-icon">
                                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><path d="M471.6 21.7c-21.9-21.9-57.3-21.9-79.2 0L362.3 51.7l97.9 97.9 30.1-30.1c21.9-21.9 21.9-57.3 0-79.2L471.6 21.7zm-299.2 220c-6.1 6.1-10.8 13.6-13.5 21.9l-29.6 88.8c-2.9 8.6-.6 18.1 5.8 24.6s15.9 8.7 24.6 5.8l88.8-29.6c8.2-2.7 15.7-7.4 21.9-13.5L437.7 172.3 339.7 74.3 172.4 241.7zM96 192a48 48 0 1 0 0-96 48 48 0 1 0 0 96z"/></svg>
                                            </span>
                                            <span class="setia-btn-text">ویرایش</span>
                                        </a>
                            <?php else: ?>
                                        <button type="button" class="setia-btn setia-btn-draft setia-publish-content setia-btn-tooltip" data-tooltip="پیش‌نویس" data-id="<?php echo esc_attr($item->id); ?>" data-publish-type="draft">
                                            <span class="setia-icon">
                                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 384 512"><path d="M0 64C0 28.7 28.7 0 64 0H224V128c0 17.7 14.3 32 32 32H384V448c0 35.3-28.7 64-64 64H64c-35.3 0-64-28.7-64-64V64zm384 64H256V0L384 128z"/></svg>
                                            </span>
                                            <span class="setia-btn-text">پیش‌نویس</span>
                                        </button>
                                        <button type="button" class="setia-btn setia-btn-publish setia-publish-content setia-btn-tooltip" data-tooltip="انتشار" data-id="<?php echo esc_attr($item->id); ?>" data-publish-type="publish">
                                            <span class="setia-icon">
                                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><path d="M288 109.3V352c0 17.7-14.3 32-32 32s-32-14.3-32-32V109.3l-73.4 73.4c-12.5 12.5-32.8 12.5-45.3 0s-12.5-32.8 0-45.3l128-128c12.5-12.5 32.8-12.5 45.3 0l128 128c12.5 12.5 12.5 32.8 0 45.3s-32.8 12.5-45.3 0L288 109.3zM64 352H192c0 35.3 28.7 64 64 64s64-28.7 64-64H448c35.3 0 64 28.7 64 64v32c0 35.3-28.7 64-64 64H64c-35.3 0-64-28.7-64-64V416c0-35.3 28.7-64 64-64z"/></svg>
                                            </span>
                                            <span class="setia-btn-text">انتشار</span>
                                        </button>
                            <?php endif; ?>
                            
                                    <button type="button" class="setia-btn setia-btn-delete setia-delete-content setia-btn-tooltip" data-tooltip="حذف" data-id="<?php echo esc_attr($item->id); ?>">
                                        <span class="setia-icon">
                                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512"><path d="M135.2 17.7L128 32H32C14.3 32 0 46.3 0 64S14.3 96 32 96H416c17.7 0 32-14.3 32-32s-14.3-32-32-32H320l-7.2-14.3C307.4 6.8 296.3 0 284.2 0H163.8c-12.1 0-23.2 6.8-28.6 17.7zM416 128H32L53.2 467c1.6 25.3 22.6 45 47.9 45H346.9c25.3 0 46.3-19.7 47.9-45L416 128z"/></svg>
                                        </span>
                                        <span class="setia-btn-text">حذف</span>
                                    </button>
                                </div>
                        </td>
                    </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
        </div>
        
        <?php if ($total_pages > 1): ?>
            <div class="setia-pagination">
                <div class="setia-pagination-info">
                    نمایش <?php echo min($items_per_page, $total_items); ?> مورد از <?php echo $total_items; ?> مورد
                </div>
                <div class="setia-pagination-links">
                        <?php
                        echo paginate_links(array(
                            'base' => add_query_arg('paged', '%#%'),
                            'format' => '',
                        'prev_text' => '<span class="setia-icon"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 320 512"><path d="M310.6 233.4c12.5 12.5 12.5 32.8 0 45.3l-192 192c-12.5 12.5-32.8 12.5-45.3 0s-12.5-32.8 0-45.3L242.7 256 73.4 86.6c-12.5-12.5-12.5-32.8 0-45.3s32.8-12.5 45.3 0l192 192z"/></svg></span>',
                        'next_text' => '<span class="setia-icon"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 320 512"><path d="M9.4 233.4c-12.5 12.5-12.5 32.8 0 45.3l192 192c12.5 12.5 32.8 12.5 45.3 0s12.5-32.8 0-45.3L77.3 256 246.6 86.6c12.5-12.5 12.5-32.8 0-45.3s-32.8-12.5-45.3 0l-192 192z"/></svg></span>',
                            'total' => $total_pages,
                            'current' => $current_page
                        ));
                        ?>
                </div>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</div>

<!-- مدال جدید ساده و مستقل -->
<div id="setia-custom-modal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-color: rgba(0,0,0,0.7); z-index: 99999; font-family: Tahoma, Arial, sans-serif; direction: rtl; overflow-y: auto; backdrop-filter: blur(5px);">
    <div style="position: relative; width: 80%; max-width: 900px; margin: 50px auto; background-color: white; border-radius: 10px; box-shadow: 0 5px 20px rgba(0,0,0,0.3); padding: 20px; max-height: 80vh; overflow-y: auto;">
        <div style="position: sticky; top: 0; background: white; padding: 10px 0; border-bottom: 1px solid #eee; margin-bottom: 15px; display: flex; justify-content: space-between; align-items: center;">
            <h2 id="setia-custom-modal-title" style="margin: 0; color: #4a6bef; font-size: 22px;"></h2>
            <button id="setia-custom-modal-close" style="background: none; border: none; font-size: 24px; line-height: 24px; cursor: pointer; color: #666;">&times;</button>
        </div>
        
        <div id="setia-modal-content" style="background-color: #f8f8f8; padding: 15px; border-radius: 8px; margin-bottom: 20px; line-height: 1.8;"></div>
        
        <div style="margin-top: 20px; background-color: white; border: 1px solid #eee; border-radius: 8px; overflow: hidden;">
            <div style="background-color: #f5f5f5; padding: 10px 15px; border-bottom: 1px solid #eee; display: flex; align-items: center; gap: 10px;">
                <span class="setia-icon">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" width="20" height="20"><path d="M352 256c0 22.2-1.2 43.6-3.3 64H163.3c-2.2-20.4-3.3-41.8-3.3-64s1.2-43.6 3.3-64H348.7c2.2 20.4 3.3 41.8 3.3 64zm28.8-64H503.9c5.3 20.5 8.1 41.9 8.1 64s-2.8 43.5-8.1 64H380.8c2.1-20.6 3.2-42 3.2-64s-1.1-43.4-3.2-64zm112.6-32H376.7c-10-63.9-29.8-117.4-55.3-151.6c78.3 20.7 142 77.5 171.9 151.6zm-149.1 0H167.7c6.1-36.4 15.5-68.6 27-94.7c10.5-23.6 22.2-40.7 33.5-51.5C239.4 3.2 248.7 0 256 0s16.6 3.2 27.8 13.8c11.3 10.8 23 27.9 33.5 51.5c11.6 26 20.9 58.2 27 94.7zm-209 0H18.6C48.6 85.9 112.2 29.1 190.6 8.4C165.1 42.6 145.3 96.1 135.3 160zM8.1 192C2.8 212.5 0 233.9 0 256s2.8 43.5 8.1 64H131.2c-2.1-20.6-3.2-42-3.2-64s1.1-43.4 3.2-64zM194.7 446.6c-11.6-26-20.9-58.2-27-94.6H344.3c-6.1 36.4-15.5 68.6-27 94.6c-10.5 23.6-22.2 40.7-33.5 51.5C272.6 508.8 263.3 512 256 512s-16.6-3.2-27.8-13.8c-11.3-10.8-23-27.9-33.5-51.5zM135.3 352c10 63.9 29.8 117.4 55.3 151.6C112.2 482.9 48.6 426.1 18.6 352H135.3zm358.1 0c-30 74.1-93.6 130.9-171.9 151.6c25.5-34.2 45.2-87.7 55.3-151.6H493.4z"/></svg>
                </span>
                <h3 style="margin: 0; font-size: 16px; color: #333;">اطلاعات سئو</h3>
            </div>
            <div style="padding: 15px;" id="setia-modal-seo-content"></div>
        </div>
        
        <div id="setia-modal-image" style="display: none; margin-top: 20px; background-color: white; border: 1px solid #eee; border-radius: 8px; overflow: hidden;">
            <div style="background-color: #f5f5f5; padding: 10px 15px; border-bottom: 1px solid #eee; display: flex; align-items: center; gap: 10px;">
                <span class="setia-icon">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" width="20" height="20"><path d="M0 96C0 60.7 28.7 32 64 32H448c35.3 0 64 28.7 64 64V416c0 35.3-28.7 64-64 64H64c-35.3 0-64-28.7-64-64V96zM323.8 202.5c-4.5-6.6-11.9-10.5-19.8-10.5s-15.4 3.9-19.8 10.5l-87 127.6L170.7 297c-4.6-5.7-11.5-9-18.7-9s-14.2 3.3-18.7 9l-64 80c-5.8 7.2-6.9 17.1-2.9 25.4s12.4 13.6 21.6 13.6h336c8.9 0 17.1-4.9 21.5-12.8s3.6-17.4-1.6-24.7l-120-176zM112 192a48 48 0 1 0 0-96 48 48 0 1 0 0 96z"/></svg>
                </span>
                <h3 style="margin: 0; font-size: 16px; color: #333;">تصویر شاخص</h3>
            </div>
            <div style="padding: 15px; text-align: center;" id="setia-modal-image-content"></div>
        </div>
    </div>
</div>

<script>
jQuery(document).ready(function($) {
    // مدیریت چک‌باکس‌ها
    $('#select-all-contents').on('change', function() {
        var isChecked = $(this).prop('checked');
        $('.content-checkbox').prop('checked', isChecked);
        updateBulkDeleteButton();
    });
    
    // Individual checkbox change
    $('.content-checkbox').on('change', function() {
        updateBulkDeleteButton();
        // If not all checkboxes are checked, uncheck the "select all" checkbox
        if (!$(this).prop('checked')) {
            $('#select-all-contents').prop('checked', false);
        }
        
        // If all checkboxes are checked, check the "select all" checkbox
        if ($('.content-checkbox:checked').length === $('.content-checkbox').length) {
            $('#select-all-contents').prop('checked', true);
        }
    });
    
    // Helper function to update bulk delete button state
    function updateBulkDeleteButton() {
        var hasCheckedItems = $('.content-checkbox:checked').length > 0;
        $('#bulk-delete-btn').prop('disabled', !hasCheckedItems);
    }
    
    // حذف گروهی
    $('#bulk-delete-btn').on('click', function(e) {
        e.preventDefault();
        
        var checkedIds = [];
        $('.content-checkbox:checked').each(function() {
            checkedIds.push($(this).data('id'));
        });
        
        if (checkedIds.length === 0) {
            return;
        }
        
        // استفاده از SweetAlert به جای confirm استاندارد
        Swal.fire({
            title: 'حذف گروهی',
            text: 'آیا از حذف ' + checkedIds.length + ' مورد انتخاب شده اطمینان دارید؟',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#3085d6',
            confirmButtonText: 'بله، حذف شود',
            cancelButtonText: 'انصراف'
        }).then((result) => {
            if (result.isConfirmed) {
                var button = $('#bulk-delete-btn');
                var originalHtml = button.html();
                button.html('<span class="setia-icon setia-icon-spin"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><path d="M304 48a48 48 0 1 0 -96 0 48 48 0 1 0 96 0zm0 416a48 48 0 1 0 -96 0 48 48 0 1 0 96 0zM48 304a48 48 0 1 0 0-96 48 48 0 1 0 0 96zm464-48a48 48 0 1 0 -96 0 48 48 0 1 0 96 0zM142.9 437A48 48 0 1 0 75 369.1 48 48 0 1 0 142.9 437zm0-294.2A48 48 0 1 0 75 75a48 48 0 1 0 67.9 67.9zM369.1 437A48 48 0 1 0 437 369.1 48 48 0 1 0 369.1 437z"/></svg></span> در حال حذف...').addClass('disabled').attr('disabled', true);
                
                $.ajax({
                    url: ajaxurl,
                    type: 'POST',
                    data: {
                        action: 'setia_bulk_delete_content',
                        nonce: '<?php echo wp_create_nonce('setia_content_nonce'); ?>',
                        content_ids: checkedIds
                    },
                    success: function(response) {
                        if (response.success) {
                            Swal.fire({
                                title: 'موفقیت‌آمیز',
                                text: 'محتواهای انتخاب شده با موفقیت حذف شدند',
                                icon: 'success',
                                confirmButtonText: 'متوجه شدم'
                            }).then(() => {
                                location.reload();
                            });
                        } else {
                            button.html(originalHtml).removeClass('disabled').attr('disabled', false);
                            Swal.fire({
                                title: 'خطا',
                                text: 'خطا در حذف محتواها: ' + (response.data ? response.data.message : 'خطای نامشخص'),
                                icon: 'error',
                                confirmButtonText: 'متوجه شدم'
                            });
                        }
                    },
                    error: function() {
                        button.html(originalHtml).removeClass('disabled').attr('disabled', false);
                        Swal.fire({
                            title: 'خطا',
                            text: 'خطا در ارتباط با سرور',
                            icon: 'error',
                            confirmButtonText: 'متوجه شدم'
                        });
                    }
                });
            }
        });
    });

    // مدیریت مدال سفارشی
    function showCustomModal() {
        $('#setia-custom-modal').fadeIn(300);
        $('body').css('overflow', 'hidden'); // جلوگیری از اسکرول صفحه پشت مدال
    }
    
    function hideCustomModal() {
        $('#setia-custom-modal').fadeOut(300);
        $('body').css('overflow', 'auto'); // بازگرداندن اسکرول صفحه
    }
    
    // نصب رویدادهای مدال
    $('#setia-custom-modal-close').on('click', function() {
        hideCustomModal();
    });
    
    // بستن مدال با کلیک خارج از محتوا
    $('#setia-custom-modal').on('click', function(e) {
        if (e.target === this) {
            hideCustomModal();
        }
    });
    
    // بستن مدال با کلید Escape
    $(document).on('keydown', function(e) {
        if (e.key === 'Escape' && $('#setia-custom-modal').is(':visible')) {
            hideCustomModal();
        }
    });

    // مشاهده محتوا
    $('.setia-view-content').on('click', function(e) {
        e.preventDefault();
        
        console.log('View content button clicked');
        
        var contentId = $(this).data('id');
        console.log('Content ID:', contentId);
        
        // نمایش وضعیت بارگذاری
        var button = $(this);
        var originalIconHtml = button.find('.setia-icon').html();
        button.find('.setia-icon').html('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><path d="M304 48a48 48 0 1 0 -96 0 48 48 0 1 0 96 0zm0 416a48 48 0 1 0 -96 0 48 48 0 1 0 96 0zM48 304a48 48 0 1 0 0-96 48 48 0 1 0 0 96zm464-48a48 48 0 1 0 -96 0 48 48 0 1 0 96 0zM142.9 437A48 48 0 1 0 75 369.1 48 48 0 1 0 142.9 437zm0-294.2A48 48 0 1 0 75 75a48 48 0 1 0 67.9 67.9zM369.1 437A48 48 0 1 0 437 369.1 48 48 0 1 0 369.1 437z"/></svg>').addClass('setia-icon-spin');
        button.addClass('disabled').attr('disabled', true);
        
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'setia_get_content_details',
                nonce: '<?php echo wp_create_nonce('setia_content_nonce'); ?>',
                content_id: contentId
            },
            success: function(response) {
                // بازگرداندن دکمه به حالت اولیه
                button.find('.setia-icon').html(originalIconHtml).removeClass('setia-icon-spin');
                button.removeClass('disabled').attr('disabled', false);
                
                console.log('Ajax response:', response);
                
                if (response.success) {
                    var data = response.data;
                    
                    // پر کردن محتوای مدال سفارشی
                    $('#setia-custom-modal-title').text(data.topic);
                    $('#setia-modal-content').html(data.content);
                    
                    // نمایش اطلاعات سئو
                    var seoHtml = '';
                    if (data.seo) {
                        seoHtml += '<p style="margin:8px 0;"><strong>عنوان:</strong> ' + data.seo.title + '</p>';
                        seoHtml += '<p style="margin:8px 0;"><strong>توضیحات:</strong> ' + data.seo.description + '</p>';
                        seoHtml += '<p style="margin:8px 0;"><strong>کلمات کلیدی:</strong> ' + data.seo.keywords + '</p>';
                    }
                    $('#setia-modal-seo-content').html(seoHtml);
                    
                    // نمایش تصویر
                    if (data.image_url) {
                        $('#setia-modal-image-content').html('<img src="' + data.image_url + '" alt="تصویر شاخص" style="max-width:100%; border-radius:8px; box-shadow:0 2px 10px rgba(0,0,0,0.1);">');
                        $('#setia-modal-image').show();
                    } else {
                        $('#setia-modal-image').hide();
                    }
                    
                    // نمایش مدال
                    showCustomModal();
                    console.log('Custom modal should be visible now');
                } else {
                    Swal.fire({
                        title: 'خطا',
                        text: 'خطا در بارگذاری اطلاعات: ' + (response.data ? response.data.message : 'خطای نامشخص'),
                        icon: 'error',
                        confirmButtonText: 'متوجه شدم'
                    });
                }
            },
            error: function(xhr, status, error) {
                // بازگرداندن دکمه به حالت اولیه
                button.find('.setia-icon').html(originalIconHtml).removeClass('setia-icon-spin');
                button.removeClass('disabled').attr('disabled', false);
                console.log('Ajax error:', xhr, status, error);
                Swal.fire({
                    title: 'خطا',
                    text: 'خطا در ارتباط با سرور: ' + error,
                    icon: 'error',
                    confirmButtonText: 'متوجه شدم'
                });
            }
        });
    });
    
    // انتشار محتوا
    $('.setia-publish-content').on('click', function(e) {
        e.preventDefault();
        var contentId = $(this).data('id');
        var publishType = $(this).data('publish-type') || 'draft';
        
        var confirmTitle = publishType === 'publish' ? 'انتشار مستقیم محتوا' : 'انتشار به صورت پیش‌نویس';
        var confirmMessage = publishType === 'publish' 
            ? 'آیا از انتشار مستقیم این محتوا اطمینان دارید؟ این محتوا بلافاصله در سایت منتشر خواهد شد.' 
            : 'آیا از انتشار این محتوا به صورت پیش‌نویس اطمینان دارید؟';
            
        Swal.fire({
            title: confirmTitle,
            text: confirmMessage,
            icon: 'question',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: 'بله، ادامه بده',
            cancelButtonText: 'انصراف'
        }).then((result) => {
            if (result.isConfirmed) {
                var button = $(this);
                var originalIconHtml = button.find('.setia-icon').html();
                button.find('.setia-icon').html('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><path d="M304 48a48 48 0 1 0 -96 0 48 48 0 1 0 96 0zm0 416a48 48 0 1 0 -96 0 48 48 0 1 0 96 0zM48 304a48 48 0 1 0 0-96 48 48 0 1 0 0 96zm464-48a48 48 0 1 0 -96 0 48 48 0 1 0 96 0zM142.9 437A48 48 0 1 0 75 369.1 48 48 0 1 0 142.9 437zm0-294.2A48 48 0 1 0 75 75a48 48 0 1 0 67.9 67.9zM369.1 437A48 48 0 1 0 437 369.1 48 48 0 1 0 369.1 437z"/></svg>').addClass('setia-icon-spin');
                button.addClass('disabled').attr('disabled', true);
                
                $.ajax({
                    url: ajaxurl,
                    type: 'POST',
                    data: {
                        action: 'setia_publish_from_history',
                        nonce: '<?php echo wp_create_nonce('setia_content_nonce'); ?>',
                        content_id: contentId,
                        status: publishType
                    },
                    success: function(response) {
                        // بازگرداندن دکمه به حالت اولیه در صورت خطا
                        if (!response.success) {
                            button.find('.setia-icon').html(originalIconHtml).removeClass('setia-icon-spin');
                            button.removeClass('disabled').attr('disabled', false);
                            Swal.fire({
                                title: 'خطا',
                                text: 'خطا در انتشار محتوا: ' + (response.data ? response.data.message : 'خطای نامشخص'),
                                icon: 'error',
                                confirmButtonText: 'متوجه شدم'
                            });
                            return;
                        }
                        
                        // اگر به صفحه ویرایش پست منتقل می‌شویم
                        if (response.data && response.data.edit_url) {
                            Swal.fire({
                                title: 'موفقیت‌آمیز',
                                text: 'محتوا با موفقیت منتشر شد. در حال انتقال به صفحه ویرایش...',
                                icon: 'success',
                                confirmButtonText: 'متوجه شدم'
                            }).then(() => {
                                window.location.href = response.data.edit_url;
                            });
                        } else {
                            Swal.fire({
                                title: 'موفقیت‌آمیز',
                                text: 'محتوا با موفقیت منتشر شد.',
                                icon: 'success',
                                confirmButtonText: 'متوجه شدم'
                            }).then(() => {
                                location.reload();
                            });
                        }
                    },
                    error: function(xhr, status, error) {
                        button.find('.setia-icon').html(originalIconHtml).removeClass('setia-icon-spin');
                        button.removeClass('disabled').attr('disabled', false);
                        console.log('Ajax error:', xhr, status, error);
                        Swal.fire({
                            title: 'خطا',
                            text: 'خطا در ارتباط با سرور: ' + error,
                            icon: 'error',
                            confirmButtonText: 'متوجه شدم'
                        });
                    }
                });
            }
        });
    });
    
    // حذف محتوا
    $('.setia-delete-content').on('click', function(e) {
        e.preventDefault();
        var contentId = $(this).data('id');
        
        Swal.fire({
            title: 'حذف محتوا',
            text: 'آیا از حذف این محتوا اطمینان دارید؟',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#3085d6',
            confirmButtonText: 'بله، حذف شود',
            cancelButtonText: 'انصراف'
        }).then((result) => {
            if (result.isConfirmed) {
                var button = $(this);
                var originalIconHtml = button.find('.setia-icon').html();
                button.find('.setia-icon').html('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><path d="M304 48a48 48 0 1 0 -96 0 48 48 0 1 0 96 0zm0 416a48 48 0 1 0 -96 0 48 48 0 1 0 96 0zM48 304a48 48 0 1 0 0-96 48 48 0 1 0 0 96zm464-48a48 48 0 1 0 -96 0 48 48 0 1 0 96 0zM142.9 437A48 48 0 1 0 75 369.1 48 48 0 1 0 142.9 437zm0-294.2A48 48 0 1 0 75 75a48 48 0 1 0 67.9 67.9zM369.1 437A48 48 0 1 0 437 369.1 48 48 0 1 0 369.1 437z"/></svg>').addClass('setia-icon-spin');
                button.addClass('disabled').attr('disabled', true);
                
                $.ajax({
                    url: ajaxurl,
                    type: 'POST',
                    data: {
                        action: 'setia_delete_content',
                        nonce: '<?php echo wp_create_nonce('setia_content_nonce'); ?>',
                        content_id: contentId
                    },
                    success: function(response) {
                        if (response.success) {
                            Swal.fire({
                                title: 'موفقیت‌آمیز',
                                text: 'محتوا با موفقیت حذف شد',
                                icon: 'success',
                                confirmButtonText: 'متوجه شدم'
                            }).then(() => {
                                location.reload();
                            });
                        } else {
                            button.find('.setia-icon').html(originalIconHtml).removeClass('setia-icon-spin');
                            button.removeClass('disabled').attr('disabled', false);
                            Swal.fire({
                                title: 'خطا',
                                text: 'خطا در حذف محتوا: ' + (response.data ? response.data.message : 'خطای نامشخص'),
                                icon: 'error',
                                confirmButtonText: 'متوجه شدم'
                            });
                        }
                    },
                    error: function(xhr, status, error) {
                        button.find('.setia-icon').html(originalIconHtml).removeClass('setia-icon-spin');
                        button.removeClass('disabled').attr('disabled', false);
                        console.log('Ajax error:', xhr, status, error);
                        Swal.fire({
                            title: 'خطا',
                            text: 'خطا در ارتباط با سرور: ' + error,
                            icon: 'error',
                            confirmButtonText: 'متوجه شدم'
                        });
                    }
                });
            }
        });
    });
});
</script> 