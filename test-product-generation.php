<?php
/**
 * تست سیستم تولید محصول WooCommerce
 */

// بارگذاری WordPress
require_once('wp-config.php');
require_once('wp-load.php');

// بررسی وجود WooCommerce
if (!class_exists('WooCommerce')) {
    echo '<h2>❌ WooCommerce نصب نیست</h2>';
    echo '<p>برای تست سیستم تولید محصول، ابتدا WooCommerce را نصب کنید.</p>';
    echo '<a href="' . admin_url('plugin-install.php?s=woocommerce&tab=search&type=term') . '">نصب WooCommerce</a>';
    exit;
}

echo '<h1>🧪 تست سیستم تولید محصول SETIA</h1>';

// بررسی وجود کلاس‌های مورد نیاز
if (!class_exists('SETIA_Content_Generator')) {
    echo '<h2>❌ کلاس SETIA_Content_Generator یافت نشد</h2>';
    exit;
}

// بررسی وجود فایل ajax-handlers
if (!file_exists('ajax-handlers.php')) {
    echo '<h2>❌ فایل ajax-handlers.php یافت نشد</h2>';
    exit;
}

echo '<h2>✅ بررسی‌های اولیه موفق</h2>';

// تست ایجاد محصول ساده
echo '<h2>🔧 تست ایجاد محصول ساده</h2>';

$product_data = array(
    'post_title' => 'محصول تست SETIA - ' . date('Y-m-d H:i:s'),
    'post_content' => 'این یک محصول تست است که توسط سیستم SETIA ایجاد شده است.',
    'post_status' => 'draft',
    'post_type' => 'product'
);

$product_id = wp_insert_post($product_data);

if (is_wp_error($product_id)) {
    echo '<p>❌ خطا در ایجاد محصول: ' . $product_id->get_error_message() . '</p>';
} else {
    echo '<p>✅ محصول با موفقیت ایجاد شد. ID: ' . $product_id . '</p>';
    
    // تنظیم نوع محصول
    wp_set_object_terms($product_id, 'simple', 'product_type');
    
    // تنظیم قیمت
    update_post_meta($product_id, '_regular_price', '100000');
    update_post_meta($product_id, '_price', '100000');
    
    // تنظیم SKU
    update_post_meta($product_id, '_sku', 'SETIA-TEST-' . $product_id);
    
    // تنظیم وضعیت موجودی
    update_post_meta($product_id, '_manage_stock', 'yes');
    update_post_meta($product_id, '_stock_quantity', 10);
    update_post_meta($product_id, '_stock_status', 'instock');
    
    echo '<p>✅ متادیتای محصول تنظیم شد</p>';
    
    // نمایش لینک محصول
    echo '<p><a href="' . admin_url('post.php?post=' . $product_id . '&action=edit') . '" target="_blank">مشاهده محصول در پنل مدیریت</a></p>';
    
    // حذف محصول تست
    wp_delete_post($product_id, true);
    echo '<p>🗑️ محصول تست حذف شد</p>';
}

echo '<h2>🎯 تست تابع‌های کمکی</h2>';

// تست تولید SKU
function test_generate_sku($product_name, $product_id) {
    $sku_base = '';
    $words = explode(' ', $product_name);
    foreach ($words as $word) {
        if (strlen($word) > 2) {
            $sku_base .= strtoupper(substr($word, 0, 2));
        }
    }
    $sku = $sku_base . '-' . $product_id;
    return $sku;
}

$test_sku = test_generate_sku('لپ تاپ گیمینگ', 123);
echo '<p>✅ تست تولید SKU: ' . $test_sku . '</p>';

// تست تولید قیمت
function test_generate_price($product_name) {
    $base_price = 100000;
    $product_lower = strtolower($product_name);
    
    if (strpos($product_lower, 'لپ تاپ') !== false) {
        $base_price = rand(15000000, 50000000);
    } elseif (strpos($product_lower, 'موبایل') !== false) {
        $base_price = rand(5000000, 30000000);
    }
    
    return $base_price;
}

$test_price = test_generate_price('لپ تاپ گیمینگ');
echo '<p>✅ تست تولید قیمت: ' . number_format($test_price) . ' تومان</p>';

// تست تولید برچسب‌ها
function test_generate_tags($product_name) {
    $tags = array('محصول جدید', 'کیفیت بالا');
    $product_lower = strtolower($product_name);
    
    if (strpos($product_lower, 'لپ تاپ') !== false) {
        $tags = array_merge($tags, array('لپ تاپ', 'کامپیوتر', 'تکنولوژی'));
    }
    
    return $tags;
}

$test_tags = test_generate_tags('لپ تاپ گیمینگ');
echo '<p>✅ تست تولید برچسب‌ها: ' . implode(', ', $test_tags) . '</p>';

echo '<h2>🎉 تست‌ها تکمیل شد</h2>';
echo '<p><a href="' . admin_url('admin.php?page=setia-content-generator') . '">بازگشت به صفحه SETIA</a></p>';
?>
