<?php
// امنیت: جلوگیری از دسترسی مستقیم
if (!defined('ABSPATH')) {
    exit;
}

// ذخیره تنظیمات
if (isset($_POST['setia_save_internal_cron_settings'])) {
    if (wp_verify_nonce($_POST['setia_internal_cron_nonce'], 'setia_internal_cron_settings')) {
        // ذخیره تنظیمات
        $security_key = sanitize_text_field($_POST['setia_internal_cron_key']);
        update_option('setia_internal_cron_key', $security_key);
        
        $interval = absint($_POST['setia_internal_cron_interval']);
        if ($interval < 5) {
            $interval = 5; // حداقل 5 دقیقه
        }
        update_option('setia_internal_cron_interval', $interval);
        
        // پیام موفقیت
        echo '<div class="notice notice-success is-dismissible"><p>تنظیمات با موفقیت ذخیره شد.</p></div>';
    }
}

// دریافت تنظیمات فعلی
$security_key = get_option('setia_internal_cron_key', '');
if (empty($security_key)) {
    // ایجاد کلید امنیتی تصادفی
    $security_key = wp_generate_password(32, false);
    update_option('setia_internal_cron_key', $security_key);
}

$interval = get_option('setia_internal_cron_interval', 15); // پیش‌فرض: 15 دقیقه

// آدرس فایل کرون داخلی
$cron_url = plugins_url('internal-cron.php', dirname(__FILE__));
$cron_url_with_key = add_query_arg('key', $security_key, $cron_url);

// زمان آخرین بررسی
$last_check = get_option('setia_internal_cron_last_check', 0);
$last_check_time = $last_check ? date_i18n('Y-m-d H:i:s', $last_check) : 'هیچ وقت';
?>

<div class="wrap">
    <h1>تنظیمات کرون داخلی</h1>
    
    <div class="setia-notice setia-info-notice">
        <p>
            <strong>توضیحات:</strong> کرون داخلی یک راه حل جایگزین برای سایت‌هایی است که امکان تنظیم کرون سرور واقعی را ندارند.
            این سیستم با استفاده از بازدیدهای کاربران و یک اسکریپت جاوااسکریپت، زمانبندی‌ها را اجرا می‌کند.
        </p>
    </div>
    
    <form method="post" action="">
        <?php wp_nonce_field('setia_internal_cron_settings', 'setia_internal_cron_nonce'); ?>
        
        <table class="form-table">
            <tr>
                <th scope="row">کلید امنیتی</th>
                <td>
                    <input type="text" name="setia_internal_cron_key" value="<?php echo esc_attr($security_key); ?>" class="regular-text" />
                    <p class="description">این کلید برای امنیت بیشتر استفاده می‌شود. می‌توانید آن را تغییر دهید یا همین کلید تصادفی را استفاده کنید.</p>
                </td>
            </tr>
            <tr>
                <th scope="row">فاصله بررسی (دقیقه)</th>
                <td>
                    <input type="number" name="setia_internal_cron_interval" value="<?php echo esc_attr($interval); ?>" min="5" step="1" class="small-text" />
                    <p class="description">فاصله زمانی بررسی زمانبندی‌ها (به دقیقه). حداقل 5 دقیقه.</p>
                </td>
            </tr>
            <tr>
                <th scope="row">آخرین بررسی</th>
                <td>
                    <p><?php echo esc_html($last_check_time); ?></p>
                </td>
            </tr>
        </table>
        
        <p class="submit">
            <input type="submit" name="setia_save_internal_cron_settings" class="button button-primary" value="ذخیره تنظیمات" />
        </p>
    </form>
    
    <h2>راهنمای استفاده از کرون داخلی</h2>
    
    <div class="setia-card">
        <h3>گام 1: فعال‌سازی کرون داخلی</h3>
        <p>کد زیر را به فایل header.php قالب خود اضافه کنید (قبل از تگ &lt;/head&gt;):</p>
        <pre><code>&lt;script&gt;
// کرون داخلی SETIA
(function() {
    var lastRun = localStorage.getItem('setia_internal_cron_last_run') || 0;
    var now = Math.floor(Date.now() / 1000);
    var interval = <?php echo esc_js($interval * 60); ?>; // تبدیل دقیقه به ثانیه
    
    if (now - lastRun > interval) {
        var img = new Image();
        img.src = '<?php echo esc_js($cron_url_with_key); ?>&t=' + now;
        localStorage.setItem('setia_internal_cron_last_run', now);
        console.log('SETIA: کرون داخلی اجرا شد');
    }
})();
&lt;/script&gt;</code></pre>
        <p>این کد با هر بازدید کاربر، بررسی می‌کند که آیا زمان اجرای کرون رسیده است یا خیر. اگر زمان اجرا رسیده باشد، فایل کرون داخلی را فراخوانی می‌کند.</p>
    </div>
    
    <div class="setia-card">
        <h3>گام 2: تست کرون داخلی</h3>
        <p>برای تست کرون داخلی، روی لینک زیر کلیک کنید:</p>
        <p><a href="<?php echo esc_url($cron_url_with_key); ?>" target="_blank" class="button">اجرای دستی کرون داخلی</a></p>
        <p>اگر پیام موفقیت را مشاهده کردید، تنظیمات درست است.</p>
    </div>
    
    <div class="setia-card">
        <h3>راه حل جایگزین: استفاده از سرویس‌های پینگ خارجی</h3>
        <p>می‌توانید از سرویس‌های پینگ خارجی مانند UptimeRobot یا Pingdom برای فراخوانی منظم آدرس زیر استفاده کنید:</p>
        <pre><code><?php echo esc_url($cron_url_with_key); ?></code></pre>
        <p>این سرویس‌ها می‌توانند در فواصل زمانی مشخص (مثلاً هر 5 دقیقه) این آدرس را فراخوانی کنند و باعث اجرای زمانبندی‌ها شوند.</p>
    </div>
</div>

<style>
.setia-notice {
    background-color: #fff;
    border-left: 4px solid #ccc;
    box-shadow: 0 1px 1px rgba(0,0,0,.04);
    margin: 0 0 20px;
    padding: 10px 12px;
}

.setia-info-notice {
    border-left-color: #2271b1;
}

.setia-card {
    background-color: #fff;
    border: 1px solid #ccd0d4;
    box-shadow: 0 1px 1px rgba(0,0,0,.04);
    margin: 0 0 20px;
    padding: 10px 20px;
}

.setia-card h3 {
    margin-top: 0;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
}

pre {
    background-color: #f6f7f7;
    padding: 10px;
    overflow: auto;
    border-radius: 3px;
}

code {
    background-color: #f6f7f7;
    padding: 2px 5px;
    border-radius: 3px;
}
</style> 