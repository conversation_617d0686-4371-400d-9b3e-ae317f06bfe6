<?php
// امنیت: جلوگیری از دسترسی مستقیم
if (!defined('ABSPATH')) {
    exit;
}

// دریافت تنظیمات ذخیره شده
$settings = get_option('setia_settings', array());
$gemini_api_key = isset($settings['gemini_api_key']) ? $settings['gemini_api_key'] : '';
$gemma_api_key = isset($settings['gemma_api_key']) ? $settings['gemma_api_key'] : '';
$imagine_art_api_key = isset($settings['imagine_art_api_key']) ? $settings['imagine_art_api_key'] : '';
$default_tone = isset($settings['default_tone']) ? $settings['default_tone'] : 'عادی';
$default_length = isset($settings['default_length']) ? $settings['default_length'] : 'متوسط';
$enable_seo = isset($settings['enable_seo']) ? $settings['enable_seo'] : 'yes';
$enable_image_generation = isset($settings['enable_image_generation']) ? $settings['enable_image_generation'] : 'yes';
$default_image_style = isset($settings['default_image_style']) ? $settings['default_image_style'] : 'realistic';
$default_aspect_ratio = isset($settings['default_aspect_ratio']) ? $settings['default_aspect_ratio'] : '16:9';
$internal_cron_interval = isset($settings['internal_cron_interval']) ? $settings['internal_cron_interval'] : 15;

// تنظیم ذخیره تنظیمات
if (isset($_POST['submit_settings']) && check_admin_referer('setia_settings')) {
    // دریافت و ذخیره داده‌ها
    $settings['gemini_api_key'] = sanitize_text_field($_POST['gemini_api_key']);
    $settings['gemma_api_key'] = sanitize_text_field($_POST['gemma_api_key']);
    $settings['imagine_art_api_key'] = sanitize_text_field($_POST['imagine_art_api_key']);
    $settings['default_tone'] = sanitize_text_field($_POST['default_tone']);
    $settings['default_length'] = sanitize_text_field($_POST['default_length']);
    $settings['enable_seo'] = sanitize_text_field($_POST['enable_seo']);
    $settings['enable_image_generation'] = sanitize_text_field($_POST['enable_image_generation']);
    $settings['default_image_style'] = sanitize_text_field($_POST['default_image_style']);
    $settings['default_aspect_ratio'] = sanitize_text_field($_POST['default_aspect_ratio']);
    $settings['internal_cron_interval'] = absint($_POST['internal_cron_interval']);
    
    // بروزرسانی تنظیمات
    update_option('setia_settings', $settings);
    
    // بروزرسانی گزینه کرون داخلی جداگانه برای سازگاری با سایر بخش‌ها
    update_option('setia_admin_cron_interval', $settings['internal_cron_interval']);
    update_option('setia_internal_cron_interval', $settings['internal_cron_interval']);
    
    // نمایش پیام موفقیت
    echo '<div class="notice notice-success"><p>تنظیمات با موفقیت ذخیره شدند</p></div>';
    
    // بروزرسانی متغیرها
    $gemini_api_key = $settings['gemini_api_key'];
    $gemma_api_key = $settings['gemma_api_key'];
    $imagine_art_api_key = $settings['imagine_art_api_key'];
    $default_tone = $settings['default_tone'];
    $default_length = $settings['default_length'];
    $enable_seo = $settings['enable_seo'];
    $enable_image_generation = $settings['enable_image_generation'];
    $default_image_style = $settings['default_image_style'];
    $default_aspect_ratio = $settings['default_aspect_ratio'];
    $internal_cron_interval = $settings['internal_cron_interval'];
}

// پاک‌سازی کش افزونه
if (isset($_POST['clear_setia_cache']) && check_admin_referer('setia_clear_cache')) {
    // اجرای عملیات پاک‌سازی کش
    if (function_exists('setia_clear_cache')) {
        setia_clear_cache();
    } else {
        // روش جایگزین: به‌روزرسانی گزینه برای اجبار به پاکسازی کش
        update_option('setia_css_version', time());
    }
    
    // نمایش پیام موفقیت
    echo '<div class="notice notice-success"><p>کش افزونه با موفقیت پاک‌سازی شد</p></div>';
}

// نمایش یک پیام اطلاع‌رسانی در مورد رفع خطای صفحه
echo '<div class="notice notice-info"><p><strong>توجه:</strong> خطاهای ارتباطی با اسکریپت‌ها برطرف شده است. در صورت مشاهده هرگونه خطای دیگر لطفاً کش مرورگر را پاک کرده (Ctrl+F5) و صفحه را مجدداً بارگذاری کنید.</p></div>';

// اضافه کردن استایل درون خطی برای اطمینان از اعمال استایل‌ها
?>
<style type="text/css">
/* بارگذاری پویا فونت ایران‌سنس با مسیر مطلق */
@font-face {
    font-family: 'IRANSans';
    font-style: normal;
    font-weight: 400;
    /* مسیر مطلق بر اساس آدرس افزونه */
    src: url('<?php echo plugin_dir_url(__FILE__); ?>../assets/fonts/IRANSansWeb.woff2') format('woff2'),
         url('<?php echo plugin_dir_url(__FILE__); ?>../assets/fonts/IRANSansWeb.woff') format('woff');
    font-display: swap;
}

@font-face {
    font-family: 'IRANSans';
    font-style: normal;
    font-weight: 700;
    src: url('<?php echo plugin_dir_url(__FILE__); ?>../assets/fonts/IRANSansWeb_Bold.woff2') format('woff2'),
         url('<?php echo plugin_dir_url(__FILE__); ?>../assets/fonts/IRANSansWeb_Bold.woff') format('woff');
    font-display: swap;
}

/* استایل درون خطی اضطراری - فقط در صورت عدم بارگذاری فایل CSS استفاده می‌شود */
.wrap.setia-settings {
    max-width: 100% !important;
    margin: 20px auto !important;
    font-family: "IRANSans", "Tahoma", sans-serif;
    color: #333 !important;
}

.setia-settings-container {
    max-width: 1200px !important;
    margin: 0 auto !important;
}

.setia-settings-header {
    text-align: center !important;
    margin-bottom: 30px !important;
    padding: 30px !important;
    /* بهبود رابط گرافیکی: گرادیانت رنگ جدید و گوشه‌های گرد بیشتر */
    background: linear-gradient(135deg, #6244EC 0%, #428df5 100%) !important;
    border-radius: 12px !important;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1) !important;
    color: #fff !important;
}

.setia-settings-header h1 {
    font-size: 28px !important;
    font-weight: 700 !important;
    margin: 0 0 15px 0 !important;
    padding: 0 !important;
    line-height: 1.3 !important;
    color: #fff !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2) !important;
}

.setia-section {
    background: #fff !important;
    border-radius: 12px !important; /* بهبود ظاهری کارت‌ها */
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.04) !important;
    margin-bottom: 25px !important;
    border: 1px solid #e2e4e7 !important;
    overflow: hidden !important;
}

.setia-section h2 {
    margin: 0 !important;
    padding: 16px 20px !important;
    background: #f9f9f9 !important;
    border-bottom: 1px solid #e2e4e7 !important;
    font-size: 18px !important; /* بهبود خوانایی */
    font-weight: 600 !important;
    color: #1d2327 !important;
}

.setia-section .form-table {
    margin: 0 !important;
    border-collapse: collapse !important;
    width: 100% !important;
}

.setia-submit-button {
    background: #fff !important;
    border: 1px solid #e2e4e7 !important;
    box-shadow: 0 2px 4px rgba(0,0,0,.04) !important;
    padding: 20px !important;
    border-radius: 6px !important;
    text-align: left !important;
    margin-top: 30px !important;
}

.rtl .setia-submit-button {
    text-align: right !important;
}

body, body.wp-admin, #wpcontent, #wpbody, #wpbody-content, .wrap, .setia-section, input, select, textarea, button, .button {
    font-family: 'IRANSans', Tahoma, sans-serif !important;
}
</style>

<div class="wrap setia-settings">
    <div class="setia-settings-container">
        <div class="setia-settings-header">
            <div class="setia-header-content">
                <div class="setia-header-icon">
                    <svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M12 2L2 7L12 12L22 7L12 2Z" stroke="currentColor" stroke-width="2" stroke-linejoin="round"/>
                        <path d="M2 17L12 22L22 17" stroke="currentColor" stroke-width="2" stroke-linejoin="round"/>
                        <path d="M2 12L12 17L22 12" stroke="currentColor" stroke-width="2" stroke-linejoin="round"/>
                    </svg>
                </div>
                <div class="setia-header-text">
                    <h1>تنظیمات افزونه SETIA Content Generator</h1>
                    <p>افزونه SETIA از هوش مصنوعی برای تولید محتوای خودکار و بهینه‌سازی وب‌سایت شما استفاده می‌کند</p>
                </div>
            </div>
            <div class="setia-status-indicators">
                <div class="setia-status-item" id="gemini-status">
                    <span class="status-dot"></span>
                    <span class="status-text">Gemini API</span>
                </div>
                <div class="setia-status-item" id="imagine-status">
                    <span class="status-dot"></span>
                    <span class="status-text">Imagine Art API</span>
                </div>
            </div>
        </div>

        <div class="setia-settings-form">
            <form method="post" action="">
                <?php wp_nonce_field('setia_settings'); ?>

                <div class="setia-section setia-card">
                    <div class="setia-section-header">
                        <div class="setia-section-icon">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M12 15L8 11H16L12 15Z" fill="currentColor"/>
                                <path d="M2 12C2 6.48 6.48 2 12 2S22 6.48 22 12 17.52 22 12 22 2 17.52 2 12Z" stroke="currentColor" stroke-width="2"/>
                            </svg>
                        </div>
                        <div class="setia-section-title">
                            <h2>تنظیمات API</h2>
                            <p class="section-description">کلیدهای دسترسی سرویس‌های هوش مصنوعی را وارد کنید</p>
                        </div>
                    </div>
                        <div class="setia-form-group">
                            <label for="gemini_api_key" class="setia-label">
                                <span class="label-icon">🤖</span>
                                کلید API Google AI (Gemini)
                                <span class="required-indicator">*</span>
                            </label>
                            <div class="setia-input-wrapper">
                                <input type="text" id="gemini_api_key" name="gemini_api_key" placeholder="AIzaSy..." value="<?php echo esc_attr($gemini_api_key); ?>" class="setia-input">
                                <div class="input-status" id="gemini-input-status"></div>
                            </div>
                            <div class="setia-help-content">
                                <button type="button" class="setia-help-toggle">راهنمای دریافت کلید</button>
                                <div class="setia-help-steps">
                                    <ol>
                                        <li>به <a href="https://makersuite.google.com/app/apikey" target="_blank">Google AI Studio</a> وارد شوید</li>
                                        <li>روی «Create API key» کلیک کرده و پروژه را انتخاب یا ایجاد کنید</li>
                                        <li>کلید تولیدشده را کپی کرده و در این فیلد جای‌گذاری کنید</li>
                                    </ol>
                                    <p class="help-note">این کلید برای تمام درخواست‌های متنی و تصویری Gemini استفاده می‌شود</p>
                                </div>
                            </div>
                        </div>

                        <div class="setia-form-group">
                            <label for="imagine_art_api_key" class="setia-label">
                                <span class="label-icon">🎨</span>
                                کلید API Imagine Art (Vyro)
                            </label>
                            <div class="setia-input-wrapper">
                                <input type="text" id="imagine_art_api_key" name="imagine_art_api_key" placeholder="sk-live-..." value="<?php echo esc_attr($imagine_art_api_key); ?>" class="setia-input">
                                <div class="input-status" id="imagine-input-status"></div>
                            </div>
                            <div class="setia-help-content">
                                <button type="button" class="setia-help-toggle">راهنمای دریافت کلید</button>
                                <div class="setia-help-steps">
                                    <ol>
                                        <li>در <a href="https://imagine.art/dashboard/api" target="_blank">Imagine Art Dashboard</a> لاگین کنید</li>
                                        <li>دکمه «Generate new key» را انتخاب کنید</li>
                                        <li>کلید صادرشده را کپی کرده و اینجا وارد کنید</li>
                                    </ol>
                                    <p class="help-note">در صورت عدم وارد کردن این کلید، از منبع جایگزین استفاده خواهد شد</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="setia-section setia-card">
                    <div class="setia-section-header">
                        <div class="setia-section-icon">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M12 2L3.09 8.26L12 14L20.91 8.26L12 2Z" stroke="currentColor" stroke-width="2" stroke-linejoin="round"/>
                                <path d="M3.09 15.74L12 22L20.91 15.74" stroke="currentColor" stroke-width="2" stroke-linejoin="round"/>
                            </svg>
                        </div>
                        <div class="setia-section-title">
                            <h2>تنظیمات پیش‌فرض</h2>
                            <p class="section-description">تنظیمات پیش‌فرض برای تولید محتوا</p>
                        </div>
                    </div>
                    <div class="setia-section-content">
                        <div class="setia-form-row">
                            <div class="setia-form-group">
                                <label for="default_tone" class="setia-label">
                                    <span class="label-icon">🎭</span>
                                    لحن پیش‌فرض
                                </label>
                                <select id="default_tone" name="default_tone" class="setia-select">
                                    <option value="عادی" <?php selected($default_tone, 'عادی'); ?>>عادی</option>
                                    <option value="رسمی" <?php selected($default_tone, 'رسمی'); ?>>رسمی</option>
                                    <option value="دوستانه" <?php selected($default_tone, 'دوستانه'); ?>>دوستانه</option>
                                    <option value="علمی" <?php selected($default_tone, 'علمی'); ?>>علمی</option>
                                    <option value="آموزشی" <?php selected($default_tone, 'آموزشی'); ?>>آموزشی</option>
                                </select>
                            </div>

                            <div class="setia-form-group">
                                <label for="default_length" class="setia-label">
                                    <span class="label-icon">📏</span>
                                    طول پیش‌فرض محتوا
                                </label>
                                <select id="default_length" name="default_length" class="setia-select">
                                    <option value="کوتاه" <?php selected($default_length, 'کوتاه'); ?>>کوتاه</option>
                                    <option value="متوسط" <?php selected($default_length, 'متوسط'); ?>>متوسط</option>
                                    <option value="بلند" <?php selected($default_length, 'بلند'); ?>>بلند</option>
                                    <option value="خیلی بلند" <?php selected($default_length, 'خیلی بلند'); ?>>خیلی بلند</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="setia-section setia-card">
                    <div class="setia-section-header">
                        <div class="setia-section-icon">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z" stroke="currentColor" stroke-width="2"/>
                                <path d="M8 12L11 15L16 9" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                        </div>
                        <div class="setia-section-title">
                            <h2>تنظیمات عملکرد</h2>
                            <p class="section-description">فعال‌سازی یا غیرفعال‌سازی قابلیت‌های افزونه</p>
                        </div>
                    </div>
                    <div class="setia-section-content">
                        <div class="setia-form-row">
                            <div class="setia-form-group">
                                <label for="enable_seo" class="setia-label">
                                    <span class="label-icon">🔍</span>
                                    تولید متاتگ‌های SEO
                                </label>
                                <div class="setia-toggle-wrapper">
                                    <select id="enable_seo" name="enable_seo" class="setia-select">
                                        <option value="yes" <?php selected($enable_seo, 'yes'); ?>>فعال</option>
                                        <option value="no" <?php selected($enable_seo, 'no'); ?>>غیرفعال</option>
                                    </select>
                                </div>
                            </div>

                            <div class="setia-form-group">
                                <label for="enable_image_generation" class="setia-label">
                                    <span class="label-icon">🖼️</span>
                                    تولید تصویر شاخص
                                </label>
                                <div class="setia-toggle-wrapper">
                                    <select id="enable_image_generation" name="enable_image_generation" class="setia-select">
                                        <option value="yes" <?php selected($enable_image_generation, 'yes'); ?>>فعال</option>
                                        <option value="no" <?php selected($enable_image_generation, 'no'); ?>>غیرفعال</option>
                                    </select>
                                </div>
                                <p class="setia-field-description">
                                    برای تولید تصویر شاخص از هوش مصنوعی استفاده می‌شود. در صورت عدم دسترسی، از سرویس جایگزین استفاده خواهد شد.
                                </p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="setia-section setia-card">
                    <div class="setia-section-header">
                        <div class="setia-section-icon">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M14.828 14.828L21 21M16.5 10.5C16.5 13.8137 13.8137 16.5 10.5 16.5C7.18629 16.5 4.5 13.8137 4.5 10.5C4.5 7.18629 7.18629 4.5 10.5 4.5C13.8137 4.5 16.5 7.18629 16.5 10.5Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                        </div>
                        <div class="setia-section-title">
                            <h2>تست تولید تصویر</h2>
                            <p class="section-description">آزمایش عملکرد سیستم تولید تصویر</p>
                        </div>
                    </div>
                    <div class="setia-section-content">
                        <div class="setia-test-container">
                            <div class="setia-form-group">
                                <label for="test_prompt" class="setia-label">
                                    <span class="label-icon">💭</span>
                                    موضوع تصویر تست
                                </label>
                                <input type="text" id="test_prompt" placeholder="مثال: منظره طبیعی زیبا در فصل پاییز" class="setia-input">
                                <p class="setia-field-description">موضوعی برای تولید تصویر تست وارد کنید</p>
                            </div>

                            <div class="setia-form-row">
                                <div class="setia-form-group">
                                    <label for="test_image_style" class="setia-label">استایل تست</label>
                                    <select id="test_image_style" class="setia-select">
                                        <option value="realistic">واقع‌گرایانه</option>
                                        <option value="anime">انیمه</option>
                                        <option value="flux-schnell">Flux Schnell</option>
                                        <option value="imagine-turbo">Imagine Turbo</option>
                                    </select>
                                </div>

                                <div class="setia-form-group">
                                    <label for="test_aspect_ratio" class="setia-label">ابعاد تست</label>
                                    <select id="test_aspect_ratio" class="setia-select">
                                        <option value="1:1">مربع (1:1)</option>
                                        <option value="16:9">عریض (16:9)</option>
                                        <option value="9:16">عمودی (9:16)</option>
                                    </select>
                                </div>
                            </div>

                            <div class="setia-test-actions">
                                <button type="button" id="generate_test_image" class="setia-button setia-button-primary">
                                    <span class="button-icon">🎨</span>
                                    تولید تصویر تست
                                </button>
                            </div>

                            <div id="test_image_result" class="setia-test-result" style="display: none;">
                                <div id="test_image_loading" class="setia-loading" style="display: none;">
                                    <div class="loading-spinner"></div>
                                    <span>در حال تولید تصویر...</span>
                                </div>
                                <div id="test_image_preview" class="setia-image-preview"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="setia-section setia-card">
                    <div class="setia-section-header">
                        <div class="setia-section-icon">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M4 16L4 18C4 19.1046 4.89543 20 6 20L18 20C19.1046 20 20 19.1046 20 18L20 16M16 12L12 16M12 16L8 12M12 16L12 4" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                        </div>
                        <div class="setia-section-title">
                            <p class="section-description">تنظیمات پیش‌فرض برای تولید تصویر و زمانبندی</p>
                        </div>
                    </div>
                    <div class="setia-section-content">
                        <div class="setia-form-row">
                            <div class="setia-form-group">
                                <label for="default_image_style" class="setia-label">
                                    <span class="label-icon">🎨</span>
                                    استایل پیش‌فرض تصویر
                                </label>
                                <select id="default_image_style" name="default_image_style" class="setia-select">
                                    <option value="realistic" <?php selected($default_image_style, 'realistic'); ?>>واقع‌گرایانه (Realistic)</option>
                                    <option value="anime" <?php selected($default_image_style, 'anime'); ?>>انیمه (Anime)</option>
                                    <option value="flux-schnell" <?php selected($default_image_style, 'flux-schnell'); ?>>Flux Schnell</option>
                                    <option value="flux-dev-fast" <?php selected($default_image_style, 'flux-dev-fast'); ?>>Flux Dev Fast</option>
                                    <option value="flux-dev" <?php selected($default_image_style, 'flux-dev'); ?>>Flux Dev</option>
                                    <option value="imagine-turbo" <?php selected($default_image_style, 'imagine-turbo'); ?>>Imagine Turbo</option>
                                </select>
                                <p class="setia-field-description">استایلی که به‌صورت پیش‌فرض برای تولید تصویر انتخاب می‌شود</p>
                            </div>

                            <div class="setia-form-group">
                                <label for="default_aspect_ratio" class="setia-label">
                                    <span class="label-icon">📐</span>
                                    ابعاد پیش‌فرض تصویر
                                </label>
                                <select id="default_aspect_ratio" name="default_aspect_ratio" class="setia-select">
                                    <option value="1:1" <?php selected($default_aspect_ratio, '1:1'); ?>>مربع (1:1)</option>
                                    <option value="16:9" <?php selected($default_aspect_ratio, '16:9'); ?>>عریض (16:9)</option>
                                    <option value="9:16" <?php selected($default_aspect_ratio, '9:16'); ?>>عمودی (9:16)</option>
                                    <option value="4:3" <?php selected($default_aspect_ratio, '4:3'); ?>>تلویزیونی (4:3)</option>
                                    <option value="3:4" <?php selected($default_aspect_ratio, '3:4'); ?>>پرتره (3:4)</option>
                                </select>
                            </div>
                        </div>

                        <div class="setia-form-group">
                            <label for="internal_cron_interval" class="setia-label">
                                <span class="label-icon">⏰</span>
                                فاصله کرون داخلی (دقیقه)
                            </label>
                            <div class="setia-input-wrapper">
                                <input type="number" id="internal_cron_interval" name="internal_cron_interval" value="<?php echo esc_attr($internal_cron_interval); ?>" min="5" step="1" class="setia-input setia-input-small">
                                <span class="input-unit">دقیقه</span>
                            </div>
                            <p class="setia-field-description">تعیین می‌کند هر چند دقیقه یک‌بار کرون داخلی وردپرس وظایف افزونه را اجرا کند</p>
                        </div>
                    </div>
                </div>

                <div class="setia-submit-section">
                    <div class="setia-submit-wrapper">
                        <button type="submit" name="submit_settings" class="setia-button setia-button-primary setia-button-large">
                            <span class="button-icon">💾</span>
                            ذخیره تنظیمات
                        </button>
                        <p class="submit-description">تغییرات شما پس از ذخیره اعمال خواهد شد</p>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Enhanced JavaScript for improved settings page -->
<script type="text/javascript">
jQuery(document).ready(function($) {
    // Initialize enhanced settings page functionality
    initializeSettingsPage();

    function initializeSettingsPage() {
        // Help toggle functionality
        $('.setia-help-toggle').on('click', function() {
            var $steps = $(this).siblings('.setia-help-steps');
            $steps.slideToggle(300);
            $(this).toggleClass('active');
        });

        // API key validation
        $('#gemini_api_key, #imagine_art_api_key').on('input', function() {
            validateApiKey($(this));
        });

        // Test image generation
        $('#generate_test_image').on('click', function() {
            generateTestImage();
        });

        // Status indicators update
        updateStatusIndicators();

        // Form validation
        $('form').on('submit', function(e) {
            if (!validateForm()) {
                e.preventDefault();
            }
        });

        // Auto-save draft functionality
        var autoSaveTimer;
        $('input, select').on('change', function() {
            clearTimeout(autoSaveTimer);
            autoSaveTimer = setTimeout(function() {
                showNotification('تغییرات شما ذخیره شد', 'success');
            }, 1000);
        });
    }

    function validateApiKey($input) {
        var value = $input.val();
        var $status = $input.siblings('.input-status');

        if (value.length === 0) {
            $status.removeClass('valid invalid').addClass('empty');
            return;
        }

        var isValid = false;
        if ($input.attr('id') === 'gemini_api_key') {
            isValid = value.startsWith('AIza') && value.length > 20;
        } else if ($input.attr('id') === 'imagine_art_api_key') {
            isValid = value.startsWith('sk-') && value.length > 20;
        }

        $status.removeClass('valid invalid empty').addClass(isValid ? 'valid' : 'invalid');
    }

    function generateTestImage() {
        var prompt = $('#test_prompt').val();
        var style = $('#test_image_style').val();
        var aspectRatio = $('#test_aspect_ratio').val();

        if (!prompt.trim()) {
            showNotification('لطفا موضوعی برای تولید تصویر وارد کنید', 'error');
            return;
        }

        $('#test_image_result').show();
        $('#test_image_loading').show();
        $('#test_image_preview').empty();
        $('#generate_test_image').prop('disabled', true).text('در حال تولید...');

        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'setia_generate_test_image',
                prompt: prompt,
                image_style: style,
                aspect_ratio: aspectRatio,
                nonce: '<?php echo wp_create_nonce('setia_test_connection'); ?>'
            },
            success: function(response) {
                $('#test_image_loading').hide();
                $('#generate_test_image').prop('disabled', false).html('<span class="button-icon">🎨</span>تولید تصویر تست');

                if (response.success) {
                    $('#test_image_preview').html(
                        '<div class="test-image-success">' +
                        '<h4>تصویر با موفقیت تولید شد:</h4>' +
                        '<img src="' + response.data.image_url + '" alt="تصویر تولید شده" class="generated-image">' +
                        '</div>'
                    );
                    showNotification('تصویر با موفقیت تولید شد', 'success');
                } else {
                    $('#test_image_preview').html(
                        '<div class="test-image-error">' +
                        '<p>خطا در تولید تصویر: ' + response.data.message + '</p>' +
                        '</div>'
                    );
                    showNotification('خطا در تولید تصویر', 'error');
                }
            },
            error: function() {
                $('#test_image_loading').hide();
                $('#generate_test_image').prop('disabled', false).html('<span class="button-icon">🎨</span>تولید تصویر تست');
                showNotification('خطا در ارتباط با سرور', 'error');
            }
        });
    }

    function updateStatusIndicators() {
        var geminiKey = $('#gemini_api_key').val();
        var imagineKey = $('#imagine_art_api_key').val();

        $('#gemini-status .status-dot').removeClass('active inactive').addClass(geminiKey ? 'active' : 'inactive');
        $('#imagine-status .status-dot').removeClass('active inactive').addClass(imagineKey ? 'active' : 'inactive');
    }

    function validateForm() {
        var isValid = true;
        var geminiKey = $('#gemini_api_key').val();

        if (!geminiKey) {
            showNotification('کلید API Gemini الزامی است', 'error');
            isValid = false;
        }

        return isValid;
    }

    function showNotification(message, type) {
        var $notification = $('<div class="setia-notification setia-notification-' + type + '">' + message + '</div>');
        $('body').append($notification);

        setTimeout(function() {
            $notification.addClass('show');
        }, 100);

        setTimeout(function() {
            $notification.removeClass('show');
            setTimeout(function() {
                $notification.remove();
            }, 300);
        }, 3000);
    }

    // Initialize on page load
    updateStatusIndicators();

    // Update status indicators when API keys change
    $('#gemini_api_key, #imagine_art_api_key').on('input', function() {
        setTimeout(updateStatusIndicators, 100);
    });
});
</script>