<?php
/**
 * فایل دیباگ برای بررسی مشکل زمانبندی
 */

// بارگذاری وردپرس
require_once('../../../wp-load.php');

// بررسی دسترسی ادمین
if (!current_user_can('manage_options')) {
    die('دسترسی غیرمجاز');
}

echo '<h1>دیباگ زمانبندی SETIA</h1>';

// بررسی وجود توکن
echo '<h2>بررسی توکن امنیتی</h2>';
$nonce = wp_create_nonce('setia-nonce');
echo "توکن فعلی: {$nonce}<br>";

// شبیه‌سازی ارسال فرم
echo '<h2>شبیه‌سازی ارسال فرم</h2>';

$test_data = [
    'action' => 'setia_save_schedule',
    'nonce' => $nonce,
    'title' => 'عنوان تست',
    'topic' => 'موضوع تست',
    'keywords' => 'کلمه کلیدی تست',
    'category' => 1,
    'tone' => 'عادی',
    'length' => 'متوسط',
    'frequency' => 'daily',
    'status' => 'active',
    'generate_image' => 'yes',
    'schedule_id' => 0
];

echo '<pre>';
echo "داده‌های تست: \n";
print_r($test_data);
echo '</pre>';

// بررسی تنظیمات فعلی
echo '<h2>تنظیمات فعلی زمانبندی</h2>';
$schedules = get_option('setia_content_schedules', array());

echo '<pre>';
print_r($schedules);
echo '</pre>';

// بررسی کرون‌های فعال
echo '<h2>کرون‌های فعال</h2>';
$crons = _get_cron_array();
$setia_crons = [];

foreach ($crons as $timestamp => $hooks) {
    foreach ($hooks as $hook => $events) {
        if (strpos($hook, 'setia_') !== false) {
            foreach ($events as $key => $event) {
                $setia_crons[] = [
                    'hook' => $hook,
                    'timestamp' => $timestamp,
                    'time' => date('Y-m-d H:i:s', $timestamp),
                    'args' => $event['args']
                ];
            }
        }
    }
}

echo '<pre>';
print_r($setia_crons);
echo '</pre>';

// بررسی مسیرهای فایل
echo '<h2>مسیرهای فایل</h2>';
echo 'ABSPATH: ' . ABSPATH . '<br>';
echo 'Plugin Path: ' . plugin_dir_path(__FILE__) . '<br>';
echo 'Includes Path: ' . plugin_dir_path(__FILE__) . 'includes/<br>';
echo 'Templates Path: ' . plugin_dir_path(__FILE__) . 'templates/<br>';

// بررسی فایل‌های کلیدی
echo '<h2>بررسی فایل‌های کلیدی</h2>';
$files_to_check = [
    'includes/scheduler.php',
    'templates/scheduler-page.php',
    'ajax-handlers.php'
];

foreach ($files_to_check as $file) {
    $full_path = plugin_dir_path(__FILE__) . $file;
    echo "{$file}: " . (file_exists($full_path) ? 'موجود' : 'عدم وجود') . ' - ' . $full_path . '<br>';
}

// بررسی اکشن‌های اجکس
echo '<h2>اکشن‌های اجکس</h2>';
global $wp_filter;
$ajax_actions = [];

if (isset($wp_filter['wp_ajax_setia_save_schedule'])) {
    $ajax_actions['wp_ajax_setia_save_schedule'] = 'ثبت شده';
} else {
    $ajax_actions['wp_ajax_setia_save_schedule'] = 'ثبت نشده';
}

if (isset($wp_filter['wp_ajax_setia_delete_schedule'])) {
    $ajax_actions['wp_ajax_setia_delete_schedule'] = 'ثبت شده';
} else {
    $ajax_actions['wp_ajax_setia_delete_schedule'] = 'ثبت نشده';
}

if (isset($wp_filter['wp_ajax_setia_get_schedules'])) {
    $ajax_actions['wp_ajax_setia_get_schedules'] = 'ثبت شده';
} else {
    $ajax_actions['wp_ajax_setia_get_schedules'] = 'ثبت نشده';
}

echo '<pre>';
print_r($ajax_actions);
echo '</pre>';

echo '<h2>اطلاعات PHP</h2>';
echo 'PHP Version: ' . phpversion() . '<br>';
echo 'WordPress Version: ' . get_bloginfo('version') . '<br>';
echo 'Memory Limit: ' . ini_get('memory_limit') . '<br>';
echo 'Post Max Size: ' . ini_get('post_max_size') . '<br>';
echo 'Max Execution Time: ' . ini_get('max_execution_time') . '<br>'; 