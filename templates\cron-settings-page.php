<?php
/**
 * صفحه تنظیمات Cron Jobs
 * SETIA Content Generator Plugin
 */

// جلوگیری از دسترسی مستقیم
if (!defined('ABSPATH')) {
    exit;
}
?>

<div class="wrap setia-cron-wrap">
    <div class="setia-header">
        <h1 class="setia-title">
            <span class="setia-icon">⚙️</span>
            تنظیمات Cron Jobs
        </h1>
        <p class="setia-subtitle">پیکربندی و مدیریت وظایف زمانبندی شده سیستم</p>
    </div>

    <div class="setia-main-container">
        <!-- وضعیت سیستم -->
        <div class="setia-card">
            <div class="setia-card-header">
                <h3>وضعیت سیستم Cron</h3>
                <div class="setia-status-indicator">
                    <span class="setia-status-dot active"></span>
                    <span class="setia-status-text">فعال</span>
                </div>
            </div>
            
            <div class="setia-card-body">
                <div class="setia-system-info">
                    <div class="setia-info-item">
                        <strong>آخرین اجرا:</strong>
                        <span>1403/04/12 - 14:30:25</span>
                    </div>
                    <div class="setia-info-item">
                        <strong>اجرای بعدی:</strong>
                        <span>1403/04/12 - 15:00:00</span>
                    </div>
                    <div class="setia-info-item">
                        <strong>تعداد وظایف فعال:</strong>
                        <span>5 وظیفه</span>
                    </div>
                    <div class="setia-info-item">
                        <strong>نوع Cron:</strong>
                        <span>WordPress Cron</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- تنظیمات کلی -->
        <div class="setia-card">
            <div class="setia-card-header">
                <h3>تنظیمات کلی Cron</h3>
            </div>
            
            <div class="setia-card-body">
                <div class="setia-form-grid">
                    <div class="setia-form-group">
                        <label for="cron-enabled">فعال‌سازی Cron:</label>
                        <div class="setia-toggle-switch">
                            <input type="checkbox" id="cron-enabled" class="setia-toggle-input" checked>
                            <label for="cron-enabled" class="setia-toggle-label">
                                <span class="setia-toggle-slider"></span>
                            </label>
                        </div>
                    </div>
                    
                    <div class="setia-form-group">
                        <label for="cron-interval">فاصله زمانی اجرا (دقیقه):</label>
                        <input type="number" id="cron-interval" class="setia-input" value="30" min="1" max="1440">
                    </div>
                    
                    <div class="setia-form-group">
                        <label for="max-execution-time">حداکثر زمان اجرا (ثانیه):</label>
                        <input type="number" id="max-execution-time" class="setia-input" value="300" min="30" max="3600">
                    </div>
                    
                    <div class="setia-form-group">
                        <label for="memory-limit">حد مجاز حافظه (MB):</label>
                        <input type="number" id="memory-limit" class="setia-input" value="256" min="128" max="1024">
                    </div>
                    
                    <div class="setia-form-group">
                        <label for="error-reporting">گزارش خطاها:</label>
                        <select id="error-reporting" class="setia-select">
                            <option value="all">همه خطاها</option>
                            <option value="critical" selected>فقط خطاهای مهم</option>
                            <option value="none">بدون گزارش</option>
                        </select>
                    </div>
                    
                    <div class="setia-form-group">
                        <label for="log-retention">نگهداری لاگ‌ها (روز):</label>
                        <input type="number" id="log-retention" class="setia-input" value="30" min="1" max="365">
                    </div>
                </div>
            </div>
        </div>

        <!-- مدیریت وظایف -->
        <div class="setia-card">
            <div class="setia-card-header">
                <h3>وظایف Cron فعال</h3>
                <button id="add-cron-job" class="setia-button setia-button-primary">
                    <span class="dashicons dashicons-plus-alt"></span>
                    افزودن وظیفه جدید
                </button>
            </div>
            
            <div class="setia-cron-jobs-list">
                <div class="setia-cron-job-item">
                    <div class="setia-job-info">
                        <h4>تولید محتوای خودکار</h4>
                        <p>Hook: setia_auto_content_generation</p>
                        <span class="setia-job-schedule">هر 30 دقیقه</span>
                    </div>
                    <div class="setia-job-status">
                        <span class="setia-status-badge active">فعال</span>
                    </div>
                    <div class="setia-job-actions">
                        <button class="setia-button setia-button-small setia-button-outline">ویرایش</button>
                        <button class="setia-button setia-button-small setia-button-secondary">اجرای فوری</button>
                        <button class="setia-button setia-button-small setia-button-danger">حذف</button>
                    </div>
                </div>
                
                <div class="setia-cron-job-item">
                    <div class="setia-job-info">
                        <h4>پاکسازی فایل‌های موقت</h4>
                        <p>Hook: setia_cleanup_temp_files</p>
                        <span class="setia-job-schedule">روزانه ساعت 02:00</span>
                    </div>
                    <div class="setia-job-status">
                        <span class="setia-status-badge active">فعال</span>
                    </div>
                    <div class="setia-job-actions">
                        <button class="setia-button setia-button-small setia-button-outline">ویرایش</button>
                        <button class="setia-button setia-button-small setia-button-secondary">اجرای فوری</button>
                        <button class="setia-button setia-button-small setia-button-danger">حذف</button>
                    </div>
                </div>
                
                <div class="setia-cron-job-item">
                    <div class="setia-job-info">
                        <h4>بروزرسانی آمار</h4>
                        <p>Hook: setia_update_statistics</p>
                        <span class="setia-job-schedule">هر ساعت</span>
                    </div>
                    <div class="setia-job-status">
                        <span class="setia-status-badge inactive">غیرفعال</span>
                    </div>
                    <div class="setia-job-actions">
                        <button class="setia-button setia-button-small setia-button-outline">ویرایش</button>
                        <button class="setia-button setia-button-small setia-button-primary">فعال‌سازی</button>
                        <button class="setia-button setia-button-small setia-button-danger">حذف</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- لاگ Cron -->
        <div class="setia-card">
            <div class="setia-card-header">
                <h3>لاگ اجرای Cron Jobs</h3>
                <div class="setia-card-actions">
                    <button id="refresh-logs" class="setia-button setia-button-outline">
                        <span class="dashicons dashicons-update"></span>
                        بروزرسانی
                    </button>
                    <button id="clear-cron-logs" class="setia-button setia-button-outline">پاک کردن لاگ‌ها</button>
                </div>
            </div>
            
            <div class="setia-cron-logs">
                <div class="setia-log-item success">
                    <div class="setia-log-time">1403/04/12 - 14:30</div>
                    <div class="setia-log-hook">setia_auto_content_generation</div>
                    <div class="setia-log-message">تولید محتوا با موفقیت انجام شد</div>
                    <div class="setia-log-duration">2.3 ثانیه</div>
                    <div class="setia-log-status">موفق</div>
                </div>
                
                <div class="setia-log-item error">
                    <div class="setia-log-time">1403/04/12 - 14:00</div>
                    <div class="setia-log-hook">setia_auto_content_generation</div>
                    <div class="setia-log-message">خطا: کلید API نامعتبر</div>
                    <div class="setia-log-duration">0.1 ثانیه</div>
                    <div class="setia-log-status">خطا</div>
                </div>
                
                <div class="setia-log-item success">
                    <div class="setia-log-time">1403/04/12 - 13:30</div>
                    <div class="setia-log-hook">setia_cleanup_temp_files</div>
                    <div class="setia-log-message">پاکسازی فایل‌های موقت انجام شد</div>
                    <div class="setia-log-duration">1.8 ثانیه</div>
                    <div class="setia-log-status">موفق</div>
                </div>
            </div>
        </div>

        <!-- دکمه‌های عملیات -->
        <div class="setia-actions-bar">
            <button id="save-cron-settings" class="setia-button setia-button-primary setia-button-large">
                <span class="dashicons dashicons-yes"></span>
                ذخیره تنظیمات
            </button>
            
            <button id="test-cron" class="setia-button setia-button-secondary setia-button-large">
                <span class="dashicons dashicons-admin-tools"></span>
                تست Cron
            </button>
            
            <button id="restart-cron" class="setia-button setia-button-outline setia-button-large">
                <span class="dashicons dashicons-update"></span>
                راه‌اندازی مجدد
            </button>
        </div>
    </div>
</div>

<!-- مودال افزودن وظیفه جدید -->
<div id="add-cron-modal" class="setia-modal" style="display: none;">
    <div class="setia-modal-content">
        <div class="setia-modal-header">
            <h3>افزودن وظیفه Cron جدید</h3>
            <span class="setia-modal-close">&times;</span>
        </div>
        <div class="setia-modal-body">
            <form id="cron-job-form">
                <div class="setia-form-group">
                    <label for="job-name">نام وظیفه:</label>
                    <input type="text" id="job-name" class="setia-input" placeholder="مثال: تولید محتوای خودکار">
                </div>
                
                <div class="setia-form-group">
                    <label for="job-hook">Hook Name:</label>
                    <input type="text" id="job-hook" class="setia-input" placeholder="مثال: setia_custom_task">
                </div>
                
                <div class="setia-form-group">
                    <label for="job-schedule">زمانبندی:</label>
                    <select id="job-schedule" class="setia-select">
                        <option value="hourly">هر ساعت</option>
                        <option value="twicedaily">دو بار در روز</option>
                        <option value="daily">روزانه</option>
                        <option value="weekly">هفتگی</option>
                    </select>
                </div>
                
                <div class="setia-form-group">
                    <label for="job-args">آرگومان‌ها (JSON):</label>
                    <textarea id="job-args" class="setia-textarea" placeholder='{"param1": "value1"}'></textarea>
                </div>
                
                <div class="setia-modal-actions">
                    <button type="submit" class="setia-button setia-button-primary">ایجاد وظیفه</button>
                    <button type="button" class="setia-button setia-button-secondary setia-modal-close">انصراف</button>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
.setia-cron-wrap {
    direction: rtl;
    font-family: 'IRANSans', Tahoma, sans-serif;
}

.setia-status-indicator {
    display: flex;
    align-items: center;
    gap: 8px;
}

.setia-status-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #dc3545;
}

.setia-status-dot.active {
    background: #28a745;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

.setia-system-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.setia-info-item {
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    border-right: 4px solid #667eea;
}

.setia-info-item strong {
    display: block;
    color: #2c3e50;
    margin-bottom: 5px;
}

.setia-form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.setia-toggle-switch {
    display: flex;
    align-items: center;
    gap: 10px;
}

.setia-toggle-input {
    display: none;
}

.setia-toggle-label {
    position: relative;
    width: 50px;
    height: 24px;
    background: #ccc;
    border-radius: 12px;
    cursor: pointer;
    transition: background 0.3s;
}

.setia-toggle-slider {
    position: absolute;
    top: 2px;
    right: 2px;
    width: 20px;
    height: 20px;
    background: white;
    border-radius: 50%;
    transition: transform 0.3s;
}

.setia-toggle-input:checked + .setia-toggle-label {
    background: #667eea;
}

.setia-toggle-input:checked + .setia-toggle-label .setia-toggle-slider {
    transform: translateX(-26px);
}

.setia-cron-jobs-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.setia-cron-job-item {
    display: grid;
    grid-template-columns: 1fr auto auto;
    gap: 20px;
    align-items: center;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
    border-right: 4px solid #667eea;
}

.setia-job-info h4 {
    margin: 0 0 5px 0;
    color: #2c3e50;
}

.setia-job-info p {
    margin: 0 0 5px 0;
    color: #666;
    font-size: 12px;
}

.setia-job-schedule {
    background: #e3f2fd;
    color: #1976d2;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 600;
}

.setia-status-badge {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    text-align: center;
}

.setia-status-badge.active {
    background: #d4edda;
    color: #155724;
}

.setia-status-badge.inactive {
    background: #f8d7da;
    color: #721c24;
}

.setia-job-actions {
    display: flex;
    gap: 8px;
}

.setia-cron-logs {
    max-height: 400px;
    overflow-y: auto;
}

.setia-log-item {
    display: grid;
    grid-template-columns: 120px 200px 1fr 80px 80px;
    gap: 15px;
    padding: 15px;
    border-bottom: 1px solid #e1e8ed;
    align-items: center;
    font-size: 14px;
}

.setia-log-item.success {
    border-right: 4px solid #28a745;
}

.setia-log-item.error {
    border-right: 4px solid #dc3545;
}

.setia-log-time {
    font-size: 12px;
    color: #666;
    font-weight: 600;
}

.setia-log-hook {
    font-family: monospace;
    background: #f1f3f4;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 11px;
}

.setia-log-message {
    color: #2c3e50;
}

.setia-log-duration {
    text-align: center;
    color: #666;
    font-size: 12px;
}

.setia-log-status {
    text-align: center;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 600;
}

.setia-log-item.success .setia-log-status {
    background: #d4edda;
    color: #155724;
}

.setia-log-item.error .setia-log-status {
    background: #f8d7da;
    color: #721c24;
}

.setia-actions-bar {
    display: flex;
    gap: 15px;
    justify-content: center;
    margin-top: 30px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
}

.setia-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    z-index: 10000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.setia-modal-content {
    background: white;
    border-radius: 12px;
    max-width: 600px;
    width: 90%;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
}

.setia-modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 12px 12px 0 0;
}

.setia-modal-header h3 {
    margin: 0;
}

.setia-modal-close {
    font-size: 24px;
    cursor: pointer;
    opacity: 0.8;
    transition: opacity 0.3s;
}

.setia-modal-close:hover {
    opacity: 1;
}

.setia-modal-body {
    padding: 20px;
}

.setia-modal-actions {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
    margin-top: 20px;
}

.setia-textarea {
    width: 100%;
    min-height: 80px;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-family: monospace;
    resize: vertical;
}
</style>

<script>
jQuery(document).ready(function($) {
    // رویدادها
    $('#add-cron-job').on('click', function() {
        $('#add-cron-modal').show();
    });

    $('.setia-modal-close').on('click', function() {
        $('.setia-modal').hide();
    });

    $('#save-cron-settings').on('click', function() {
        alert('تنظیمات Cron ذخیره شد.');
    });

    $('#test-cron').on('click', function() {
        alert('تست Cron با موفقیت انجام شد.');
    });

    $('#restart-cron').on('click', function() {
        if (confirm('آیا مطمئن هستید که می‌خواهید Cron را راه‌اندازی مجدد کنید؟')) {
            alert('Cron در حال راه‌اندازی مجدد است...');
        }
    });

    $('#refresh-logs').on('click', function() {
        alert('لاگ‌ها بروزرسانی شدند.');
    });

    $('#clear-cron-logs').on('click', function() {
        if (confirm('آیا مطمئن هستید که می‌خواهید لاگ‌ها را پاک کنید؟')) {
            $('.setia-cron-logs').html('<p style="text-align: center; padding: 20px; color: #666;">لاگ‌ها پاک شدند.</p>');
        }
    });

    $('#cron-job-form').on('submit', function(e) {
        e.preventDefault();
        alert('وظیفه Cron جدید ایجاد شد.');
        $('#add-cron-modal').hide();
    });
});
</script>
