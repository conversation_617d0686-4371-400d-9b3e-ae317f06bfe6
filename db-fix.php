<?php
/**
 * اسکریپت بررسی و اصلاح ساختار دیتابیس برای زمانبندی SETIA
 * این فایل را فقط یکبار اجرا کنید
 */

// بررسی اگر این فایل به صورت مستقیم فراخوانی شده است
if (!isset($_GET['fix_key']) || $_GET['fix_key'] !== 'setia_db_fix_key') {
    header('HTTP/1.0 403 Forbidden');
    die('دسترسی مستقیم به این فایل مجاز نیست. کلید امنیتی را وارد کنید.');
}

// مسیر اصلی وردپرس
$wp_load_path = __DIR__ . '/wp-load.php';

// بررسی وجود فایل wp-load.php
if (!file_exists($wp_load_path)) {
    // تلاش برای یافتن wp-load.php در مسیرهای مختلف
    $possible_paths = array(
        dirname(__DIR__) . '/wp-load.php',
        dirname(dirname(__DIR__)) . '/wp-load.php',
        dirname(dirname(dirname(__DIR__))) . '/wp-load.php',
        dirname(dirname(dirname(dirname(__DIR__)))) . '/wp-load.php',
    );
    
    $found = false;
    foreach ($possible_paths as $path) {
        if (file_exists($path)) {
            $wp_load_path = $path;
            $found = true;
            break;
        }
    }
    
    if (!$found) {
        die('خطا: فایل wp-load.php پیدا نشد. لطفاً این اسکریپت را در مسیر اصلی وردپرس قرار دهید.');
    }
}

// بارگذاری هسته وردپرس
require_once $wp_load_path;

// بررسی دسترسی مدیر
if (!current_user_can('manage_options')) {
    die('شما دسترسی لازم برای انجام این عملیات را ندارید.');
}

// نمایش سربرگ
echo '<!DOCTYPE html>
<html dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تعمیر دیتابیس SETIA</title>
    <style>
        body { font-family: Tahoma, Arial; padding: 20px; background: #f5f5f5; }
        h1, h2, h3 { color: #333; }
        .container { max-width: 1000px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 5px; overflow: auto; }
    </style>
</head>
<body>
    <div class="container">
        <h1>تعمیر دیتابیس SETIA</h1>';

// بخش زمانبندی حذف شده است

// بخش بررسی ساختار جدول زمانبندی حذف شده است
        
// بخش مدیریت جدول زمانبندی حذف شده است

// بخش بررسی کرون زمانبندی حذف شده است

// بررسی تنظیم DISABLE_WP_CRON
echo '<h2>بررسی تنظیم DISABLE_WP_CRON</h2>';

if (defined('DISABLE_WP_CRON') && DISABLE_WP_CRON) {
    echo '<p class="success">✅ تنظیم DISABLE_WP_CRON فعال است.</p>';
    echo '<p>لطفاً اطمینان حاصل کنید که کرون سرور برای اجرای فایل cron-trigger.php تنظیم شده است.</p>';
} else {
    echo '<p class="warning">⚠️ تنظیم DISABLE_WP_CRON فعال نیست.</p>';
    echo '<p>برای عملکرد بهتر، توصیه می‌شود این تنظیم را در فایل wp-config.php فعال کنید:</p>';
    echo '<pre>define(\'DISABLE_WP_CRON\', true);</pre>';
}

// پایان
echo '<h2>عملیات تعمیر به پایان رسید</h2>';
echo '<p>بخش زمانبندی از افزونه حذف شده است.</p>';

echo '</div></body></html>'; 