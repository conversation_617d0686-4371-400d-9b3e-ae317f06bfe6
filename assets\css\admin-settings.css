/* Modern SETIA Settings Page - Enhanced Design - LOADED SUCCESSFULLY */

/* CSS Test Marker - If you see this comment in browser dev tools, CSS is loading */
body.wp-admin .wrap.setia-settings::before {
    content: "SETIA CSS LOADED ✓";
    position: fixed;
    top: 32px;
    right: 20px;
    background: #00a32a;
    color: white;
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 12px;
    z-index: 9999;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
}

/* WordPress Admin Override Styles - Fix Layout Conflicts */
body.wp-admin .wrap.setia-settings table.form-table,
body.wp-admin .wrap.setia-settings .form-table {
    display: none !important; /* Hide default WordPress form tables */
}

body.wp-admin .wrap.setia-settings .form-table th,
body.wp-admin .wrap.setia-settings .form-table td {
    display: none !important;
}

/* Ensure our custom form elements take precedence */
body.wp-admin .wrap.setia-settings .setia-section {
    background: var(--setia-white) !important;
    border-radius: var(--setia-radius-lg) !important;
    box-shadow: var(--setia-shadow) !important;
    border: 1px solid var(--setia-gray-200) !important;
    margin-bottom: 32px !important;
    overflow: hidden !important;
    display: block !important;
}

body.wp-admin .wrap.setia-settings .setia-section-content {
    padding: 32px !important;
    background: transparent !important;
    display: block !important;
}

/* Fix text alignment and direction for RTL */
body.wp-admin .wrap.setia-settings .setia-label,
body.wp-admin .wrap.setia-settings .setia-input,
body.wp-admin .wrap.setia-settings .setia-select,
body.wp-admin .wrap.setia-settings .setia-help-content {
    text-align: right !important;
    direction: rtl !important;
}

@font-face {
    font-family: 'IRANSans';
    src: url('../fonts/IRANSansWeb.woff2') format('woff2'),
         url('../fonts/IRANSansWeb.woff') format('woff');
    font-weight: 400;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'IRANSans';
    src: url('../fonts/IRANSansWeb_Bold.woff2') format('woff2'),
         url('../fonts/IRANSansWeb_Bold.woff') format('woff');
    font-weight: 700;
    font-style: normal;
    font-display: swap;
}

/* Enhanced Design System - Modern UI/UX */
:root {
    /* Primary Brand Colors - Modern Purple/Blue Palette */
    --setia-primary: #6366f1;
    --setia-primary-50: #eef2ff;
    --setia-primary-100: #e0e7ff;
    --setia-primary-200: #c7d2fe;
    --setia-primary-300: #a5b4fc;
    --setia-primary-400: #818cf8;
    --setia-primary-500: #6366f1;
    --setia-primary-600: #4f46e5;
    --setia-primary-700: #4338ca;
    --setia-primary-800: #3730a3;
    --setia-primary-900: #312e81;

    /* Secondary Colors - Complementary Teal */
    --setia-secondary: #06b6d4;
    --setia-secondary-50: #ecfeff;
    --setia-secondary-100: #cffafe;
    --setia-secondary-200: #a5f3fc;
    --setia-secondary-300: #67e8f9;
    --setia-secondary-400: #22d3ee;
    --setia-secondary-500: #06b6d4;
    --setia-secondary-600: #0891b2;
    --setia-secondary-700: #0e7490;
    --setia-secondary-800: #155e75;
    --setia-secondary-900: #164e63;

    /* Status Colors - Enhanced Semantic Palette */
    --setia-success: #10b981;
    --setia-success-light: #d1fae5;
    --setia-warning: #f59e0b;
    --setia-warning-light: #fef3c7;
    --setia-error: #ef4444;
    --setia-error-light: #fee2e2;
    --setia-info: #3b82f6;
    --setia-info-light: #dbeafe;

    /* Neutral Colors - Modern Gray Scale */
    --setia-white: #ffffff;
    --setia-gray-50: #f8fafc;
    --setia-gray-100: #f1f5f9;
    --setia-gray-200: #e2e8f0;
    --setia-gray-300: #cbd5e1;
    --setia-gray-400: #94a3b8;
    --setia-gray-500: #64748b;
    --setia-gray-600: #475569;
    --setia-gray-700: #334155;
    --setia-gray-800: #1e293b;
    --setia-gray-900: #0f172a;

    /* Modern Gradients */
    --setia-gradient-primary: linear-gradient(135deg, #6366f1 0%, #8b5cf6 50%, #06b6d4 100%);
    --setia-gradient-secondary: linear-gradient(135deg, #06b6d4 0%, #3b82f6 100%);
    --setia-gradient-success: linear-gradient(135deg, #10b981 0%, #059669 100%);
    --setia-gradient-warning: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    --setia-gradient-error: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    --setia-gradient-glass: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);

    /* Enhanced Shadows - Layered Depth */
    --setia-shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --setia-shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --setia-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --setia-shadow-md: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --setia-shadow-lg: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --setia-shadow-xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    --setia-shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(255, 255, 255, 0.05);
    --setia-shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);

    /* Border Radius - Consistent Scaling */
    --setia-radius-xs: 4px;
    --setia-radius-sm: 6px;
    --setia-radius: 8px;
    --setia-radius-md: 12px;
    --setia-radius-lg: 16px;
    --setia-radius-xl: 20px;
    --setia-radius-2xl: 24px;
    --setia-radius-3xl: 32px;
    --setia-radius-full: 9999px;

    /* Enhanced Transitions */
    --setia-transition-fast: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
    --setia-transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --setia-transition-slow: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    --setia-transition-bounce: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);

    /* Typography Scale */
    --setia-text-xs: 0.75rem;
    --setia-text-sm: 0.875rem;
    --setia-text-base: 1rem;
    --setia-text-lg: 1.125rem;
    --setia-text-xl: 1.25rem;
    --setia-text-2xl: 1.5rem;
    --setia-text-3xl: 1.875rem;
    --setia-text-4xl: 2.25rem;

    /* Spacing Scale */
    --setia-space-1: 0.25rem;
    --setia-space-2: 0.5rem;
    --setia-space-3: 0.75rem;
    --setia-space-4: 1rem;
    --setia-space-5: 1.25rem;
    --setia-space-6: 1.5rem;
    --setia-space-8: 2rem;
    --setia-space-10: 2.5rem;
    --setia-space-12: 3rem;
    --setia-space-16: 4rem;
    --setia-space-20: 5rem;
}

/* Modern Base Styles - Enhanced Typography & Layout */
body.wp-admin .wrap.setia-settings,
body.wp-admin .wrap.setia-settings *:not(.dashicons):not([class*="dashicons"]) {
    font-family: 'IRANSans', 'Segoe UI', 'Roboto', 'Helvetica Neue', sans-serif !important;
    box-sizing: border-box !important;
    -webkit-font-smoothing: antialiased !important;
    -moz-osx-font-smoothing: grayscale !important;
}

body.wp-admin .wrap.setia-settings {
    background: linear-gradient(135deg, var(--setia-gray-50) 0%, var(--setia-primary-50) 100%) !important;
    min-height: 100vh !important;
    padding: 0 !important;
    margin: 0 !important;
    direction: rtl !important;
    max-width: none !important;
    position: relative !important;
    overflow-x: hidden !important;
}

/* Animated Background Pattern */
body.wp-admin .wrap.setia-settings::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 20% 80%, rgba(99, 102, 241, 0.03) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(139, 92, 246, 0.03) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(6, 182, 212, 0.02) 0%, transparent 50%);
    animation: backgroundFloat 20s ease-in-out infinite alternate;
    pointer-events: none;
    z-index: 0;
}

@keyframes backgroundFloat {
    0% {
        transform: translateY(0px) rotate(0deg);
        opacity: 0.5;
    }
    100% {
        transform: translateY(-20px) rotate(1deg);
        opacity: 0.8;
    }
}

body.wp-admin .wrap.setia-settings .setia-settings-container {
    max-width: 1400px !important;
    margin: 0 auto !important;
    padding: var(--setia-space-8) var(--setia-space-6) !important;
    background: transparent !important;
    width: 100% !important;
    position: relative !important;
    z-index: 1 !important;
}

/* Modern Header Design - Matching Main Page */
body.wp-admin .wrap.setia-settings .setia-settings-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    color: white !important;
    padding: 40px !important;
    border-radius: 16px !important;
    margin-bottom: 30px !important;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1) !important;
    position: relative !important;
    overflow: hidden !important;
    transform: translateY(0) !important;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

body.wp-admin .wrap.setia-settings .setia-settings-header:hover {
    transform: translateY(-2px) !important;
    box-shadow: var(--setia-shadow-2xl), 0 0 40px rgba(99, 102, 241, 0.2) !important;
}

body.wp-admin .wrap.setia-settings .setia-settings-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
    pointer-events: none;
}

body.wp-admin .wrap.setia-settings .setia-settings-header::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--setia-gradient-glass);
    opacity: 0.3;
    pointer-events: none;
}

@keyframes headerGlow {
    0% {
        opacity: 0.7;
        transform: scale(1) rotate(0deg);
    }
    100% {
        opacity: 1;
        transform: scale(1.02) rotate(0.5deg);
    }
}

body.wp-admin .wrap.setia-settings .setia-header-content {
    display: flex !important;
    align-items: center !important;
    gap: 20px !important;
    position: relative !important;
    z-index: 1 !important;
    margin-bottom: 30px !important;
}

body.wp-admin .wrap.setia-settings .setia-header-main {
    display: flex !important;
    align-items: center !important;
    gap: 20px !important;
    flex: 1 !important;
}

body.wp-admin .wrap.setia-settings .setia-header-icon {
    background: rgba(255, 255, 255, 0.2) !important;
    padding: 15px !important;
    border-radius: 12px !important;
    backdrop-filter: blur(10px) !important;
    transition: all 0.3s ease !important;
}

body.wp-admin .wrap.setia-settings .setia-header-icon:hover {
    background: rgba(255, 255, 255, 0.3) !important;
    transform: scale(1.05) !important;
}

body.wp-admin .wrap.setia-settings .setia-header-icon svg {
    color: white !important;
    width: 48px !important;
    height: 48px !important;
}

body.wp-admin .wrap.setia-settings .setia-header-text h1 {
    margin: 0 0 10px 0 !important;
    font-size: 28px !important;
    font-weight: 700 !important;
    color: white !important;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
    line-height: 1.2 !important;
}

body.wp-admin .wrap.setia-settings .setia-header-text p {
    margin: 0 !important;
    font-size: 16px !important;
    color: rgba(255, 255, 255, 0.9) !important;
    line-height: 1.5 !important;
    font-weight: 400 !important;
}

.setia-status-indicators {
    display: flex;
    gap: 16px;
    flex-wrap: wrap;
    position: relative;
    z-index: 2;
}

.setia-status-item {
    display: flex;
    align-items: center;
    gap: 10px;
    background: rgba(255, 255, 255, 0.12);
    padding: 12px 16px;
    border-radius: var(--setia-radius);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.15);
    transition: var(--setia-transition);
    min-width: 140px;
}

.setia-status-item:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-1px);
}

.status-dot {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background: var(--setia-gray-400);
    transition: var(--setia-transition);
    position: relative;
}

.status-dot::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    border-radius: 50%;
    background: inherit;
    opacity: 0.3;
    transform: scale(0);
    transition: var(--setia-transition);
}

.status-dot.active {
    background: var(--setia-success);
    box-shadow: 0 0 12px rgba(16, 185, 129, 0.4);
}

.status-dot.active::before {
    transform: scale(1);
    animation: statusPulse 2s ease-in-out infinite;
}

.status-dot.inactive {
    background: var(--setia-error);
}

@keyframes statusPulse {
    0%, 100% { transform: scale(1); opacity: 0.3; }
    50% { transform: scale(1.5); opacity: 0.1; }
}

.status-text {
    font-size: 14px;
    font-weight: 600;
    color: var(--setia-white);
    opacity: 0.95;
}

/* Modern Glassmorphism Section Cards */
body.wp-admin .wrap.setia-settings .setia-section {
    background: rgba(255, 255, 255, 0.95) !important;
    backdrop-filter: blur(20px) !important;
    border-radius: var(--setia-radius-2xl) !important;
    margin-bottom: var(--setia-space-8) !important;
    box-shadow: var(--setia-shadow-lg) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    overflow: hidden !important;
    transition: var(--setia-transition) !important;
    position: relative !important;
    transform: translateY(0) !important;
}

body.wp-admin .wrap.setia-settings .setia-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--setia-gradient-primary);
    opacity: 0;
    transition: var(--setia-transition);
    z-index: 1;
}

body.wp-admin .wrap.setia-settings .setia-section::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 10% 20%, rgba(99, 102, 241, 0.02) 0%, transparent 50%),
        radial-gradient(circle at 90% 80%, rgba(139, 92, 246, 0.02) 0%, transparent 50%);
    opacity: 0;
    transition: var(--setia-transition);
    pointer-events: none;
    z-index: 0;
}

body.wp-admin .wrap.setia-settings .setia-section:hover {
    box-shadow: var(--setia-shadow-2xl), 0 0 40px rgba(99, 102, 241, 0.1) !important;
    transform: translateY(-6px) !important;
    border-color: rgba(99, 102, 241, 0.2) !important;
}

body.wp-admin .wrap.setia-settings .setia-section:hover::before {
    opacity: 1;
}

body.wp-admin .wrap.setia-settings .setia-section:hover::after {
    opacity: 1;
}

body.wp-admin .wrap.setia-settings .setia-section-header {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.8) 0%, rgba(248, 250, 252, 0.9) 100%) !important;
    padding: var(--setia-space-8) var(--setia-space-10) !important;
    border-bottom: 1px solid rgba(203, 213, 225, 0.3) !important;
    display: flex !important;
    align-items: center !important;
    gap: var(--setia-space-5) !important;
    position: relative !important;
    z-index: 2 !important;
}

body.wp-admin .wrap.setia-settings .setia-section-icon {
    background: var(--setia-gradient-primary) !important;
    color: var(--setia-white) !important;
    padding: var(--setia-space-4) !important;
    border-radius: var(--setia-radius-xl) !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    box-shadow: var(--setia-shadow-lg) !important;
    transition: var(--setia-transition-bounce) !important;
    min-width: 56px !important;
    min-height: 56px !important;
}

body.wp-admin .wrap.setia-settings .setia-section-icon:hover {
    transform: scale(1.1) rotate(5deg) !important;
    box-shadow: var(--setia-shadow-xl) !important;
}

body.wp-admin .wrap.setia-settings .setia-section-icon svg {
    width: 28px !important;
    height: 28px !important;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2)) !important;
}

body.wp-admin .wrap.setia-settings .setia-section-title h2 {
    margin: 0 0 var(--setia-space-2) 0 !important;
    font-size: var(--setia-text-2xl) !important;
    font-weight: 700 !important;
    color: var(--setia-gray-900) !important;
    line-height: 1.2 !important;
    letter-spacing: -0.025em !important;
}

.section-description {
    margin: 0;
    font-size: 16px;
    color: var(--setia-gray-600);
    line-height: 1.6;
    font-weight: 400;
}

.setia-section-content {
    padding: 36px;
    background: var(--setia-white);
}

/* Modern Form Elements */
.setia-form-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 24px;
    margin-bottom: 32px;
}

body.wp-admin .wrap.setia-settings .setia-form-group {
    display: flex !important;
    flex-direction: column !important;
    gap: var(--setia-space-3) !important;
    margin-bottom: var(--setia-space-8) !important;
    width: 100% !important;
    position: relative !important;
    transition: var(--setia-transition) !important;
}

body.wp-admin .wrap.setia-settings .setia-form-group:hover {
    transform: translateY(-1px) !important;
}

body.wp-admin .wrap.setia-settings .setia-label {
    display: flex !important;
    align-items: center !important;
    gap: var(--setia-space-2) !important;
    font-size: var(--setia-text-base) !important;
    font-weight: 600 !important;
    color: var(--setia-gray-800) !important;
    margin-bottom: var(--setia-space-2) !important;
    line-height: 1.4 !important;
    text-align: right !important;
    direction: rtl !important;
    transition: var(--setia-transition) !important;
}

body.wp-admin .wrap.setia-settings .label-icon {
    font-size: var(--setia-text-lg) !important;
    opacity: 0.8 !important;
    transition: var(--setia-transition) !important;
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1)) !important;
}

body.wp-admin .wrap.setia-settings .required-indicator {
    color: var(--setia-error) !important;
    font-weight: 700 !important;
    margin-left: var(--setia-space-1) !important;
    animation: pulse 2s infinite !important;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.6; }
}

body.wp-admin .wrap.setia-settings .setia-input-wrapper {
    position: relative !important;
    display: flex !important;
    align-items: center !important;
    width: 100% !important;
    direction: rtl !important;
}

body.wp-admin .wrap.setia-settings .setia-input,
body.wp-admin .wrap.setia-settings .setia-select {
    width: 100% !important;
    padding: var(--setia-space-4) var(--setia-space-5) !important;
    border: 2px solid rgba(203, 213, 225, 0.6) !important;
    border-radius: var(--setia-radius-lg) !important;
    font-size: var(--setia-text-base) !important;
    font-family: inherit !important;
    background: rgba(255, 255, 255, 0.9) !important;
    backdrop-filter: blur(10px) !important;
    transition: var(--setia-transition) !important;
    box-shadow: var(--setia-shadow-sm), inset 0 1px 2px rgba(0, 0, 0, 0.05) !important;
    direction: rtl !important;
    text-align: right !important;
    box-sizing: border-box !important;
    position: relative !important;
}

body.wp-admin .wrap.setia-settings .setia-input:hover,
body.wp-admin .wrap.setia-settings .setia-select:hover {
    border-color: rgba(99, 102, 241, 0.4) !important;
    box-shadow: var(--setia-shadow), inset 0 1px 2px rgba(0, 0, 0, 0.05) !important;
    transform: translateY(-1px) !important;
}

body.wp-admin .wrap.setia-settings .setia-input:focus,
body.wp-admin .wrap.setia-settings .setia-select:focus {
    outline: none !important;
    border-color: var(--setia-primary-500) !important;
    box-shadow: 0 0 0 4px rgba(99, 102, 241, 0.15), var(--setia-shadow-md), inset 0 1px 2px rgba(0, 0, 0, 0.05) !important;
    transform: translateY(-2px) !important;
    background: rgba(255, 255, 255, 0.95) !important;
}

.setia-input:hover,
.setia-select:hover {
    border-color: var(--setia-gray-300);
    box-shadow: var(--setia-shadow);
}

.setia-input-small {
    max-width: 200px;
}

.input-unit {
    margin-right: 12px;
    font-size: 14px;
    color: var(--setia-gray-600);
    font-weight: 500;
}

.input-status {
    position: absolute;
    left: 16px;
    top: 50%;
    transform: translateY(-50%);
    width: 12px;
    height: 12px;
    border-radius: 50%;
    opacity: 0;
    transition: var(--setia-transition);
}

.input-status.valid {
    background: var(--setia-success);
    opacity: 1;
}

.input-status.invalid {
    background: var(--setia-error);
    opacity: 1;
}

.setia-field-description {
    font-size: 14px;
    color: var(--setia-gray-600);
    line-height: 1.5;
    margin-top: 8px;
}

.setia-submit-button {
    margin-top: 30px;
    padding-top: 25px;
    border-top: 1px solid #e5e5e5;
}

.setia-submit-button .button-primary {
    background: linear-gradient(to right, #6244EC, #428df5);
    border: none;
    border-radius: 8px;
    padding: 10px 20px;
    height: auto;
    font-weight: 500;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.setia-submit-button .button-primary:hover {
    background: linear-gradient(to right, #5234DB, #3580e8);
    box-shadow: 0 4px 12px rgba(98, 68, 236, 0.35);
}

.setia-troubleshooting {
    background-color: #f8f9fa;
    padding: 20px 25px;
    border-radius: 12px;
    border-right: 4px solid #428df5;
    margin-top: 30px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.03);
}

.setia-troubleshooting p strong {
    font-size: 1.1em;
    color: #242424;
}

.setia-troubleshooting ol {
    margin-right: 20px; 
    line-height: 1.7;
    padding-right: 10px;
}

.setia-troubleshooting ol li {
    margin-bottom: 10px;
}

/* Styles for test image section elements */
#setia-test-image-result {
    margin-top: 20px;
    padding: 20px;
    border: 1px solid #e5e5e5;
    background-color: #ffffff;
    border-radius: 12px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.03);
}

#setia-test-image-result h3 {
    margin-top: 0;
    margin-bottom: 15px;
    font-size: 1.1em;
    color: #242424;
}

#setia-test-image-error {
    color: #b91c1c;
    font-weight: 600;
    background-color: #fef2f2;
    padding: 15px;
    border: 1px solid #fecaca;
    border-radius: 8px;
}

#setia-test-image-loading {
    color: #444654;
    font-style: italic;
    padding: 15px;
    background-color: #f8fafc;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
}

#setia-test-image-preview img {
    max-width: 100%;
    max-height: 350px; 
    object-fit: contain;
    border: 1px solid #e5e5e5;
    border-radius: 8px;
    padding: 5px;
    background-color: #fff;
    margin-top: 15px;
    display: block;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* General Settings Page Improvements */
.setia-settings .form-table th {
    width: 220px;
    padding-right: 20px;
    color: #242424;
}

.setia-settings .form-table td {
    padding-bottom: 20px;
}

.setia-settings .regular-text {
    width: 100%;
    max-width: 500px;
}

.setia-settings select {
    min-width: 280px;
    padding: 8px 12px;
    border-radius: 8px;
}

/* Modern Interactive Help Content */
body.wp-admin .wrap.setia-settings .setia-help-content {
    margin-top: var(--setia-space-4) !important;
    background: rgba(255, 255, 255, 0.8) !important;
    backdrop-filter: blur(10px) !important;
    border-radius: var(--setia-radius-xl) !important;
    border: 1px solid rgba(203, 213, 225, 0.3) !important;
    overflow: hidden !important;
    transition: var(--setia-transition) !important;
    box-shadow: var(--setia-shadow-sm) !important;
}

body.wp-admin .wrap.setia-settings .setia-help-toggle {
    width: 100% !important;
    background: linear-gradient(135deg, rgba(248, 250, 252, 0.9) 0%, rgba(241, 245, 249, 0.9) 100%) !important;
    border: none !important;
    padding: var(--setia-space-4) var(--setia-space-5) !important;
    color: var(--setia-primary-600) !important;
    font-size: var(--setia-text-sm) !important;
    font-weight: 600 !important;
    cursor: pointer !important;
    text-align: right !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
    position: relative !important;
    display: flex !important;
    align-items: center !important;
    justify-content: space-between !important;
    border-radius: 8px !important;
    margin-bottom: 0 !important;
    user-select: none !important;
    -webkit-user-select: none !important;
    -moz-user-select: none !important;
    -ms-user-select: none !important;
}

body.wp-admin .wrap.setia-settings .setia-help-toggle::after {
    content: '▼' !important;
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
    font-size: var(--setia-text-xs) !important;
    opacity: 0.7 !important;
    transform-origin: center !important;
    will-change: transform !important;
}

body.wp-admin .wrap.setia-settings .setia-help-toggle.active::after {
    transform: rotate(180deg) !important;
}

body.wp-admin .wrap.setia-settings .setia-help-toggle:hover {
    background: linear-gradient(135deg, rgba(241, 245, 249, 0.9) 0%, rgba(226, 232, 240, 0.9) 100%) !important;
    color: var(--setia-primary-700) !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15) !important;
}

body.wp-admin .wrap.setia-settings .setia-help-toggle:active {
    transform: translateY(0) !important;
    transition: transform 0.1s ease !important;
}

.setia-help-steps {
    padding: 24px;
    background: var(--setia-white);
    border-top: 1px solid var(--setia-gray-200);
    display: none;
    overflow: hidden;
    border-radius: 0 0 8px 8px;
    box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.1);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    transform-origin: top;
    will-change: height, opacity;
}

.setia-help-steps ol {
    margin: 0 0 20px 0;
    padding-right: 24px;
    counter-reset: step-counter;
}

.setia-help-steps li {
    margin-bottom: 12px;
    line-height: 1.6;
    position: relative;
    counter-increment: step-counter;
    padding-right: 8px;
}

.setia-help-steps li::marker {
    content: counter(step-counter) ". ";
    font-weight: 600;
    color: var(--setia-primary);
}

.setia-help-steps a {
    color: var(--setia-primary);
    text-decoration: none;
    font-weight: 500;
    transition: var(--setia-transition);
}

.setia-help-steps a:hover {
    color: var(--setia-primary-dark);
    text-decoration: underline;
}

.help-note {
    background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
    padding: 16px 20px;
    border-radius: var(--setia-radius-sm);
    font-size: 14px;
    color: var(--setia-primary-dark);
    margin: 0;
    border-right: 4px solid var(--setia-primary);
    line-height: 1.5;
}

/* Prevent animation conflicts and ensure smooth state management */
.setia-help-toggle[data-setia-animating="true"] {
    pointer-events: none !important;
    opacity: 0.8 !important;
}

.setia-help-steps.animating {
    pointer-events: none;
}

/* Enhanced focus states for better accessibility */
.setia-help-toggle:focus-visible {
    outline: 3px solid rgba(102, 126, 234, 0.3) !important;
    outline-offset: 2px !important;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1) !important;
}

/* Modern Glassmorphism Button Design */
body.wp-admin .wrap.setia-settings .setia-button {
    display: inline-flex !important;
    align-items: center !important;
    gap: var(--setia-space-2) !important;
    padding: var(--setia-space-4) var(--setia-space-6) !important;
    border: none !important;
    border-radius: var(--setia-radius-xl) !important;
    font-size: var(--setia-text-base) !important;
    font-weight: 600 !important;
    font-family: inherit !important;
    cursor: pointer !important;
    transition: var(--setia-transition-bounce) !important;
    text-decoration: none !important;
    position: relative !important;
    overflow: hidden !important;
    backdrop-filter: blur(10px) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
}

body.wp-admin .wrap.setia-settings .setia-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.6s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 1;
}

body.wp-admin .wrap.setia-settings .setia-button:hover::before {
    left: 100%;
}

body.wp-admin .wrap.setia-settings .setia-button-primary {
    background: var(--setia-gradient-primary) !important;
    color: var(--setia-white) !important;
    box-shadow: var(--setia-shadow-lg) !important;
    position: relative !important;
    z-index: 2 !important;
}

body.wp-admin .wrap.setia-settings .setia-button-primary:hover {
    transform: translateY(-3px) scale(1.02) !important;
    box-shadow: var(--setia-shadow-2xl), 0 0 30px rgba(99, 102, 241, 0.3) !important;
}

body.wp-admin .wrap.setia-settings .setia-button-primary:active {
    transform: translateY(-1px) scale(0.98) !important;
    box-shadow: var(--setia-shadow-lg) !important;
}

body.wp-admin .wrap.setia-settings .setia-button-large {
    padding: var(--setia-space-5) var(--setia-space-8) !important;
    font-size: var(--setia-text-lg) !important;
}

body.wp-admin .wrap.setia-settings .button-icon {
    font-size: var(--setia-text-xl) !important;
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.2)) !important;
}

/* Submit Section */
.setia-submit-section {
    margin-top: 48px;
    padding: 32px;
    background: var(--setia-gradient-light);
    border-radius: var(--setia-radius-lg);
    border: 1px solid var(--setia-gray-200);
    text-align: center;
}

.setia-submit-wrapper {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 16px;
}

.submit-description {
    margin: 0;
    font-size: 14px;
    color: var(--setia-gray-600);
    font-weight: 500;
}

/* Test Section Styling */
.setia-test-container {
    background: var(--setia-gray-50);
    padding: 24px;
    border-radius: var(--setia-radius);
    border: 1px solid var(--setia-gray-200);
}

.setia-test-actions {
    margin-top: 24px;
    text-align: center;
}

.setia-test-result {
    margin-top: 24px;
    padding: 24px;
    background: var(--setia-white);
    border-radius: var(--setia-radius);
    border: 1px solid var(--setia-gray-200);
}

.setia-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    padding: 32px;
    color: var(--setia-gray-600);
}

.loading-spinner {
    width: 24px;
    height: 24px;
    border: 3px solid var(--setia-gray-200);
    border-top: 3px solid var(--setia-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.setia-image-preview img {
    max-width: 100%;
    height: auto;
    border-radius: var(--setia-radius);
    box-shadow: var(--setia-shadow-md);
}

/* Toggle Wrapper */
.setia-toggle-wrapper {
    position: relative;
}

.setia-toggle-wrapper select {
    appearance: none;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: left 12px center;
    background-repeat: no-repeat;
    background-size: 16px 12px;
    padding-left: 40px;
}

/* Enhanced Responsive Design - Desktop to Tablet */
@media (max-width: 1200px) {
    body.wp-admin .wrap.setia-settings .setia-settings-container {
        max-width: 100% !important;
        padding: var(--setia-space-6) var(--setia-space-5) !important;
    }

    body.wp-admin .wrap.setia-settings .setia-header-content {
        flex-direction: column !important;
        text-align: center !important;
        gap: var(--setia-space-6) !important;
    }

    body.wp-admin .wrap.setia-settings .setia-header-text h1 {
        font-size: var(--setia-text-3xl) !important;
    }

    body.wp-admin .wrap.setia-settings .setia-header-text p {
        font-size: var(--setia-text-base) !important;
    }

    body.wp-admin .wrap.setia-settings .setia-status-indicators {
        justify-content: center !important;
        flex-wrap: wrap !important;
        gap: var(--setia-space-3) !important;
    }

    body.wp-admin .wrap.setia-settings .setia-form-group {
        margin-bottom: var(--setia-space-6) !important;
    }

    body.wp-admin .wrap.setia-settings .setia-input,
    body.wp-admin .wrap.setia-settings .setia-select {
        font-size: var(--setia-text-base) !important;
        padding: var(--setia-space-4) var(--setia-space-5) !important;
    }

    body.wp-admin .wrap.setia-settings .setia-section-header {
        flex-direction: column !important;
        text-align: center !important;
        gap: var(--setia-space-4) !important;
    }
}

@media (max-width: 768px) {
    body.wp-admin .wrap.setia-settings .setia-settings-container {
        padding: 16px 12px !important;
    }

    body.wp-admin .wrap.setia-settings .setia-settings-header {
        padding: 32px 24px !important;
        margin-bottom: 24px !important;
    }

    body.wp-admin .wrap.setia-settings .setia-header-main {
        flex-direction: column !important;
        text-align: center !important;
        gap: 16px !important;
    }

    body.wp-admin .wrap.setia-settings .setia-header-text h1 {
        font-size: 24px !important;
    }

    body.wp-admin .wrap.setia-settings .setia-header-text p {
        font-size: 16px !important;
    }

    body.wp-admin .wrap.setia-settings .setia-input,
    body.wp-admin .wrap.setia-settings .setia-select {
        font-size: var(--setia-text-base) !important;
        padding: var(--setia-space-4) var(--setia-space-4) !important;
        border-radius: var(--setia-radius-lg) !important;
        min-height: 48px !important; /* Touch-friendly */
    }

    body.wp-admin .wrap.setia-settings .setia-label {
        font-size: var(--setia-text-sm) !important;
    }

    body.wp-admin .wrap.setia-settings .setia-section-header {
        padding: var(--setia-space-6) var(--setia-space-5) !important;
        flex-direction: column !important;
        text-align: center !important;
        gap: var(--setia-space-4) !important;
    }

    body.wp-admin .wrap.setia-settings .setia-section-content {
        padding: var(--setia-space-6) var(--setia-space-5) !important;
    }

    body.wp-admin .wrap.setia-settings .setia-button {
        padding: var(--setia-space-4) var(--setia-space-6) !important;
        min-height: 48px !important; /* Touch-friendly */
        font-size: var(--setia-text-base) !important;
    }

    .setia-form-row {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .setia-status-indicators {
        flex-direction: column;
        gap: 12px;
    }

    .setia-status-item {
        min-width: auto;
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .setia-settings-container {
        padding: 12px 8px;
    }

    .setia-settings-header {
        padding: 24px 16px;
        border-radius: var(--setia-radius);
    }

    .setia-header-text h1 {
        font-size: 20px;
    }

    .setia-header-text p {
        font-size: 14px;
    }

    .setia-section {
        border-radius: var(--setia-radius);
        margin-bottom: 20px;
    }

    .setia-section-header {
        padding: 20px 16px;
    }

    .setia-section-content {
        padding: 20px 16px;
    }

    .setia-section-title h2 {
        font-size: 20px;
    }

    .section-description {
        font-size: 14px;
    }

    .setia-input,
    .setia-select {
        padding: 14px 16px;
        font-size: 16px; /* Prevent zoom on iOS */
    }

    .setia-button-large {
        padding: 16px 24px;
        font-size: 16px;
    }

    .setia-submit-section {
        padding: 24px 16px;
        margin-top: 32px;
    }
}

/* Accessibility Enhancements */
.setia-input:focus,
.setia-select:focus,
.setia-button:focus,
.setia-help-toggle:focus {
    outline: 3px solid rgba(102, 126, 234, 0.3);
    outline-offset: 2px;
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
    .setia-section {
        border: 2px solid var(--setia-gray-800);
    }

    .setia-input,
    .setia-select {
        border: 2px solid var(--setia-gray-800);
    }

    .setia-button-primary {
        background: var(--setia-black);
        color: var(--setia-white);
        border: 2px solid var(--setia-white);
    }
}

/* Enhanced Accessibility - Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }

    body.wp-admin .wrap.setia-settings .setia-section:hover,
    body.wp-admin .wrap.setia-settings .setia-button-primary:hover,
    body.wp-admin .wrap.setia-settings .setia-input:hover,
    body.wp-admin .wrap.setia-settings .setia-form-group:hover,
    body.wp-admin .wrap.setia-settings .setia-settings-header:hover {
        transform: none !important;
    }

    body.wp-admin .wrap.setia-settings .setia-settings-header::before {
        animation: none !important;
    }

    body.wp-admin .wrap.setia-settings .required-indicator {
        animation: none !important;
    }

    body.wp-admin .wrap.setia-settings .setia-button.loading::before {
        animation: none !important;
    }
}

/* Print Styles */
@media print {
    .setia-settings-header {
        background: none !important;
        color: var(--setia-black) !important;
        box-shadow: none !important;
    }

    .setia-section {
        box-shadow: none !important;
        border: 1px solid var(--setia-gray-400) !important;
        break-inside: avoid;
    }

    .setia-button {
        display: none !important;
    }

    .setia-help-steps {
        display: block !important;
    }
}

/* Dark Mode Support (if WordPress admin supports it) */
@media (prefers-color-scheme: dark) {
    :root {
        --setia-gray-50: #1e293b;
        --setia-gray-100: #334155;
        --setia-gray-200: #475569;
        --setia-white: #0f172a;
        --setia-gray-800: #f1f5f9;
        --setia-gray-600: #cbd5e1;
    }
}

/* Enhanced Focus Styles for Better Accessibility */
.setia-input:focus-visible,
.setia-select:focus-visible {
    outline: 3px solid var(--setia-primary);
    outline-offset: 2px;
    border-color: var(--setia-primary);
}

.setia-button:focus-visible {
    outline: 3px solid rgba(102, 126, 234, 0.5);
    outline-offset: 2px;
}

/* Enhanced Loading States with Modern Animations */
body.wp-admin .wrap.setia-settings .setia-button.loading {
    pointer-events: none !important;
    opacity: 0.8 !important;
    position: relative !important;
    overflow: hidden !important;
}

body.wp-admin .wrap.setia-settings .setia-button.loading::before {
    content: '' !important;
    position: absolute !important;
    top: 0 !important;
    left: -100% !important;
    width: 100% !important;
    height: 100% !important;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent) !important;
    animation: shimmer 1.5s infinite !important;
}

body.wp-admin .wrap.setia-settings .setia-button.loading::after {
    content: '' !important;
    position: absolute !important;
    top: 50% !important;
    left: 50% !important;
    width: 18px !important;
    height: 18px !important;
    margin: -9px 0 0 -9px !important;
    border: 2px solid transparent !important;
    border-top: 2px solid currentColor !important;
    border-radius: 50% !important;
    animation: spin 1s linear infinite !important;
}

@keyframes shimmer {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* Error States */
.setia-input.error {
    border-color: var(--setia-error);
    box-shadow: 0 0 0 4px rgba(239, 68, 68, 0.1);
}

.setia-error-message {
    color: var(--setia-error);
    font-size: 14px;
    margin-top: 8px;
    display: flex;
    align-items: center;
    gap: 6px;
}

.setia-error-message::before {
    content: '⚠️';
    font-size: 16px;
}

/* Enhanced Success States with Modern Design */
body.wp-admin .wrap.setia-settings .setia-input.success {
    border-color: var(--setia-success) !important;
    box-shadow: 0 0 0 4px rgba(16, 185, 129, 0.15), var(--setia-shadow-sm) !important;
    background: rgba(16, 185, 129, 0.02) !important;
}

body.wp-admin .wrap.setia-settings .setia-success-message {
    color: var(--setia-success) !important;
    font-size: var(--setia-text-sm) !important;
    margin-top: var(--setia-space-2) !important;
    display: flex !important;
    align-items: center !important;
    gap: var(--setia-space-2) !important;
    padding: var(--setia-space-2) var(--setia-space-3) !important;
    background: rgba(16, 185, 129, 0.1) !important;
    border-radius: var(--setia-radius-lg) !important;
    backdrop-filter: blur(10px) !important;
    animation: slideInUp 0.3s ease-out !important;
}

body.wp-admin .wrap.setia-settings .setia-success-message::before {
    content: '✅' !important;
    font-size: var(--setia-text-base) !important;
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1)) !important;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Modern Glassmorphism Notification System */
body.wp-admin .setia-notification {
    position: fixed !important;
    top: var(--setia-space-8) !important;
    right: var(--setia-space-8) !important;
    background: rgba(255, 255, 255, 0.95) !important;
    backdrop-filter: blur(20px) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    border-radius: var(--setia-radius-2xl) !important;
    padding: var(--setia-space-5) var(--setia-space-6) !important;
    box-shadow: var(--setia-shadow-2xl) !important;
    z-index: 10000 !important;
    display: flex !important;
    align-items: center !important;
    gap: var(--setia-space-3) !important;
    min-width: 320px !important;
    max-width: 420px !important;
    transform: translateX(120%) scale(0.9) !important;
    opacity: 0 !important;
    transition: var(--setia-transition-bounce) !important;
}

body.wp-admin .setia-notification.show {
    transform: translateX(0) scale(1) !important;
    opacity: 1 !important;
}

.setia-notification-info {
    border-right: 4px solid var(--setia-primary);
}

.setia-notification-success {
    border-right: 4px solid var(--setia-success);
}

.setia-notification-error {
    border-right: 4px solid var(--setia-error);
}

.setia-notification-warning {
    border-right: 4px solid var(--setia-warning);
}

.notification-close {
    background: none;
    border: none;
    font-size: 20px;
    cursor: pointer;
    color: var(--setia-gray-400);
    transition: var(--setia-transition);
    margin-right: auto;
}

.notification-close:hover {
    color: var(--setia-gray-600);
}

/* Modern Interactive Tooltip System */
body.wp-admin .wrap.setia-settings .setia-tooltip {
    position: absolute !important;
    background: rgba(30, 41, 59, 0.95) !important;
    backdrop-filter: blur(10px) !important;
    color: var(--setia-white) !important;
    padding: var(--setia-space-3) var(--setia-space-4) !important;
    border-radius: var(--setia-radius-lg) !important;
    font-size: var(--setia-text-sm) !important;
    font-weight: 500 !important;
    z-index: 10001 !important;
    pointer-events: none !important;
    opacity: 0 !important;
    transform: translateY(-8px) scale(0.95) !important;
    transition: var(--setia-transition-bounce) !important;
    white-space: nowrap !important;
    max-width: 280px !important;
    text-align: center !important;
    box-shadow: var(--setia-shadow-lg) !important;
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
}

body.wp-admin .wrap.setia-settings .setia-tooltip.show {
    opacity: 1 !important;
    transform: translateY(0) scale(1) !important;
}

body.wp-admin .wrap.setia-settings .setia-tooltip::after {
    content: '' !important;
    position: absolute !important;
    top: 100% !important;
    left: 50% !important;
    transform: translateX(-50%) !important;
    border: 6px solid transparent !important;
    border-top-color: rgba(30, 41, 59, 0.95) !important;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1)) !important;
}

@keyframes tooltipFadeIn {
    from {
        opacity: 0;
        transform: translateY(-8px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* Form Focus States */
.setia-form-group.focused .setia-label {
    color: var(--setia-primary);
    transform: translateY(-2px);
    transition: var(--setia-transition);
}

/* Enhanced Card Hover Effects */
.setia-card {
    background: var(--setia-white);
    border-radius: var(--setia-radius-lg);
    box-shadow: var(--setia-shadow);
    margin-bottom: 32px;
    border: 1px solid var(--setia-gray-200);
    overflow: hidden;
    transition: var(--setia-transition);
    position: relative;
}

.setia-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--setia-gradient-primary);
    opacity: 0;
    transition: var(--setia-transition);
}

.setia-card:hover {
    box-shadow: var(--setia-shadow-lg);
    transform: translateY(-4px);
}

.setia-card:hover::before {
    opacity: 1;
}

/* Mobile Notification Adjustments */
@media (max-width: 768px) {
    .setia-notification {
        right: 16px;
        left: 16px;
        min-width: auto;
        max-width: none;
    }
}

.section-description {
    margin: 0;
    color: #64748b;
    font-size: 14px;
    line-height: 1.4;
}

.setia-section-content {
    padding: 32px;
}

/* Duplicate form styles removed - using main form styles above */

.setia-input:focus {
    outline: none;
    border-color: #667eea;
    background: white;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.setia-input-small {
    max-width: 120px;
}

.input-unit {
    margin-right: 12px;
    color: #6b7280;
    font-size: 14px;
    font-weight: 500;
}

.input-status {
    position: absolute;
    left: 12px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.input-status.valid {
    background: #10b981;
}

.input-status.invalid {
    background: #ef4444;
}

.input-status.empty {
    background: #d1d5db;
}

.setia-select {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    font-size: 14px;
    background: #fafafa;
    transition: all 0.3s ease;
}

.setia-select:focus {
    outline: none;
    border-color: #667eea;
    background: white;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.setia-field-description {
    margin: 8px 0 0 0;
    color: #6b7280;
    font-size: 13px;
    line-height: 1.4;
}

/* Help Content - Consolidated and Fixed Styles */
.setia-help-content {
    margin-top: 12px;
}

/* Remove duplicate styles - using the enhanced version below */

.help-note {
    margin: 0;
    color: #6b7280;
    font-size: 12px;
    font-style: italic;
}

/* Test Image Generation Styles */
.setia-test-container {
    background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
    padding: 24px;
    border-radius: 12px;
    border: 1px solid #bae6fd;
}

.setia-test-actions {
    margin: 24px 0;
    text-align: center;
}

.setia-test-result {
    margin-top: 24px;
}

.setia-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    padding: 24px;
    background: #f8fafc;
    border-radius: 8px;
    color: #64748b;
    font-style: italic;
}

.loading-spinner {
    width: 20px;
    height: 20px;
    border: 2px solid #e5e7eb;
    border-top: 2px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.setia-image-preview {
    text-align: center;
}

.test-image-success h4 {
    margin: 0 0 16px 0;
    color: #059669;
    font-size: 16px;
}

.generated-image {
    max-width: 100%;
    max-height: 400px;
    border-radius: 12px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    border: 3px solid white;
}

.test-image-error {
    padding: 16px;
    background: #fef2f2;
    border: 1px solid #fecaca;
    border-radius: 8px;
    color: #dc2626;
    text-align: center;
}

/* Button Styles */
.setia-button {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 12px 24px;
    border: none;
    border-radius: 12px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    font-family: inherit;
}

.setia-button-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.setia-button-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

.setia-button-primary:active {
    transform: translateY(0);
}

.setia-button-large {
    padding: 16px 32px;
    font-size: 16px;
}

.button-icon {
    font-size: 16px;
}

/* Submit Section */
.setia-submit-section {
    margin-top: 40px;
    padding: 32px;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border-radius: 16px;
    text-align: center;
    border: 1px solid #e2e8f0;
}

.setia-submit-wrapper {
    max-width: 400px;
    margin: 0 auto;
}

.submit-description {
    margin: 12px 0 0 0;
    color: #64748b;
    font-size: 14px;
}

/* Notifications */
.setia-notification {
    position: fixed;
    top: 32px;
    right: 32px;
    padding: 16px 24px;
    border-radius: 12px;
    color: white;
    font-weight: 600;
    z-index: 9999;
    transform: translateX(400px);
    transition: transform 0.3s ease;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.setia-notification.show {
    transform: translateX(0);
}

.setia-notification-success {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.setia-notification-error {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
}

.setia-notification-info {
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
}

/* Responsive Design */
@media (max-width: 768px) {
    .setia-settings-container {
        padding: 0 16px;
    }

    .setia-header-content {
        flex-direction: column;
        text-align: center;
        gap: 16px;
    }

    .setia-status-indicators {
        justify-content: center;
        flex-wrap: wrap;
    }

    .setia-section-content {
        padding: 24px;
    }

    .setia-form-row {
        grid-template-columns: 1fr;
        gap: 16px;
    }

    .setia-section-header {
        padding: 20px 24px;
        flex-direction: column;
        text-align: center;
        gap: 12px;
    }
}

.setia-settings .button-secondary {
    margin-right: 8px;
}

.wrap.setia-settings > h1 {
    margin-bottom: 25px;
    color: #242424;
}

/* Style for API key link descriptions */
.setia-field p.description a {
    text-decoration: none;
    color: #6244EC;
}

.setia-field p.description a:hover {
    text-decoration: underline;
}

/* Cache tools section */
.setia-cache-tools {
    background-color: #f0f9ff;
    padding: 20px;
    border-radius: 12px;
    border-left: 4px solid #428df5;
    margin-top: 20px;
}

.setia-cache-tools .button-secondary {
    background-color: #428df5;
    color: white;
    border: none;
    padding: 8px 16px;
    height: auto;
    border-radius: 8px;
}

.setia-cache-tools .button-secondary:hover {
    background-color: #3580e8;
    box-shadow: 0 2px 8px rgba(66, 141, 245, 0.35);
} 