<?php
/**
 * صفحه سیستم Cron داخلی
 * SETIA Content Generator Plugin
 */

// جلوگیری از دسترسی مستقیم
if (!defined('ABSPATH')) {
    exit;
}
?>

<div class="wrap setia-internal-cron-wrap">
    <div class="setia-header">
        <h1 class="setia-title">
            <span class="setia-icon">🔧</span>
            سیستم Cron داخلی
        </h1>
        <p class="setia-subtitle">مدیریت و نظارت بر سیستم Cron داخلی پلاگین SETIA</p>
    </div>

    <div class="setia-main-container">
        <!-- وضعیت سیستم -->
        <div class="setia-card">
            <div class="setia-card-header">
                <h3>وضعیت سیستم</h3>
                <div class="setia-system-controls">
                    <button id="start-cron" class="setia-button setia-button-success">
                        <span class="dashicons dashicons-controls-play"></span>
                        شروع
                    </button>
                    <button id="stop-cron" class="setia-button setia-button-danger">
                        <span class="dashicons dashicons-controls-pause"></span>
                        توقف
                    </button>
                    <button id="restart-cron" class="setia-button setia-button-secondary">
                        <span class="dashicons dashicons-update"></span>
                        راه‌اندازی مجدد
                    </button>
                </div>
            </div>
            
            <div class="setia-card-body">
                <div class="setia-status-grid">
                    <div class="setia-status-item">
                        <div class="setia-status-icon running">🟢</div>
                        <div class="setia-status-info">
                            <h4>وضعیت سیستم</h4>
                            <span class="setia-status-value">در حال اجرا</span>
                        </div>
                    </div>
                    
                    <div class="setia-status-item">
                        <div class="setia-status-icon">⏰</div>
                        <div class="setia-status-info">
                            <h4>آخرین اجرا</h4>
                            <span class="setia-status-value">2 دقیقه پیش</span>
                        </div>
                    </div>
                    
                    <div class="setia-status-item">
                        <div class="setia-status-icon">📊</div>
                        <div class="setia-status-info">
                            <h4>وظایف در صف</h4>
                            <span class="setia-status-value">3 وظیفه</span>
                        </div>
                    </div>
                    
                    <div class="setia-status-item">
                        <div class="setia-status-icon">💾</div>
                        <div class="setia-status-info">
                            <h4>استفاده از حافظه</h4>
                            <span class="setia-status-value">45 MB</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- صف وظایف -->
        <div class="setia-card">
            <div class="setia-card-header">
                <h3>صف وظایف</h3>
                <div class="setia-card-actions">
                    <button id="clear-queue" class="setia-button setia-button-outline">پاک کردن صف</button>
                    <button id="refresh-queue" class="setia-button setia-button-outline">
                        <span class="dashicons dashicons-update"></span>
                        بروزرسانی
                    </button>
                </div>
            </div>
            
            <div class="setia-queue-list">
                <div class="setia-queue-item processing">
                    <div class="setia-queue-status">
                        <span class="setia-status-indicator processing"></span>
                        در حال پردازش
                    </div>
                    <div class="setia-queue-info">
                        <h4>تولید محتوای خودکار</h4>
                        <p>کلیدواژه: گوشی هوشمند</p>
                        <small>شروع: 1403/04/12 - 14:30</small>
                    </div>
                    <div class="setia-queue-progress">
                        <div class="setia-progress-bar">
                            <div class="setia-progress-fill" style="width: 65%"></div>
                        </div>
                        <span class="setia-progress-text">65%</span>
                    </div>
                    <div class="setia-queue-actions">
                        <button class="setia-button setia-button-small setia-button-danger">لغو</button>
                    </div>
                </div>
                
                <div class="setia-queue-item pending">
                    <div class="setia-queue-status">
                        <span class="setia-status-indicator pending"></span>
                        در انتظار
                    </div>
                    <div class="setia-queue-info">
                        <h4>تولید محصول WooCommerce</h4>
                        <p>کلیدواژه: کیف چرمی</p>
                        <small>زمان‌بندی: 1403/04/12 - 15:00</small>
                    </div>
                    <div class="setia-queue-priority">
                        <span class="setia-priority-badge high">اولویت بالا</span>
                    </div>
                    <div class="setia-queue-actions">
                        <button class="setia-button setia-button-small setia-button-primary">اجرای فوری</button>
                        <button class="setia-button setia-button-small setia-button-danger">حذف</button>
                    </div>
                </div>
                
                <div class="setia-queue-item pending">
                    <div class="setia-queue-status">
                        <span class="setia-status-indicator pending"></span>
                        در انتظار
                    </div>
                    <div class="setia-queue-info">
                        <h4>بروزرسانی Schema Markup</h4>
                        <p>تعداد پست‌ها: 25</p>
                        <small>زمان‌بندی: 1403/04/12 - 16:00</small>
                    </div>
                    <div class="setia-queue-priority">
                        <span class="setia-priority-badge normal">اولویت عادی</span>
                    </div>
                    <div class="setia-queue-actions">
                        <button class="setia-button setia-button-small setia-button-primary">اجرای فوری</button>
                        <button class="setia-button setia-button-small setia-button-danger">حذف</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- آمار عملکرد -->
        <div class="setia-stats-grid">
            <div class="setia-stat-card">
                <div class="setia-stat-icon">✅</div>
                <div class="setia-stat-content">
                    <h4>وظایف موفق امروز</h4>
                    <span class="setia-stat-number">12</span>
                    <span class="setia-stat-change">+3 نسبت به دیروز</span>
                </div>
            </div>
            
            <div class="setia-stat-card">
                <div class="setia-stat-icon">❌</div>
                <div class="setia-stat-content">
                    <h4>وظایف ناموفق</h4>
                    <span class="setia-stat-number">1</span>
                    <span class="setia-stat-change">-2 نسبت به دیروز</span>
                </div>
            </div>
            
            <div class="setia-stat-card">
                <div class="setia-stat-icon">⏱️</div>
                <div class="setia-stat-content">
                    <h4>میانگین زمان اجرا</h4>
                    <span class="setia-stat-number">2.3 دقیقه</span>
                    <span class="setia-stat-change">بهبود 15%</span>
                </div>
            </div>
            
            <div class="setia-stat-card">
                <div class="setia-stat-icon">🔄</div>
                <div class="setia-stat-content">
                    <h4>مدت زمان فعالیت</h4>
                    <span class="setia-stat-number">5 ساعت</span>
                    <span class="setia-stat-change">بدون وقفه</span>
                </div>
            </div>
        </div>

        <!-- تنظیمات سیستم -->
        <div class="setia-card">
            <div class="setia-card-header">
                <h3>تنظیمات سیستم</h3>
            </div>
            
            <div class="setia-card-body">
                <div class="setia-settings-grid">
                    <div class="setia-form-group">
                        <label for="max-concurrent">حداکثر وظایف همزمان:</label>
                        <input type="number" id="max-concurrent" class="setia-input" value="3" min="1" max="10">
                    </div>
                    
                    <div class="setia-form-group">
                        <label for="queue-check-interval">فاصله بررسی صف (ثانیه):</label>
                        <input type="number" id="queue-check-interval" class="setia-input" value="30" min="10" max="300">
                    </div>
                    
                    <div class="setia-form-group">
                        <label for="max-retry">حداکثر تلاش مجدد:</label>
                        <input type="number" id="max-retry" class="setia-input" value="3" min="0" max="10">
                    </div>
                    
                    <div class="setia-form-group">
                        <label for="timeout">مهلت زمانی (ثانیه):</label>
                        <input type="number" id="timeout" class="setia-input" value="300" min="60" max="1800">
                    </div>
                    
                    <div class="setia-form-group">
                        <label for="auto-cleanup">پاکسازی خودکار:</label>
                        <div class="setia-toggle-switch">
                            <input type="checkbox" id="auto-cleanup" class="setia-toggle-input" checked>
                            <label for="auto-cleanup" class="setia-toggle-label">
                                <span class="setia-toggle-slider"></span>
                            </label>
                        </div>
                    </div>
                    
                    <div class="setia-form-group">
                        <label for="debug-mode">حالت دیباگ:</label>
                        <div class="setia-toggle-switch">
                            <input type="checkbox" id="debug-mode" class="setia-toggle-input">
                            <label for="debug-mode" class="setia-toggle-label">
                                <span class="setia-toggle-slider"></span>
                            </label>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- لاگ سیستم -->
        <div class="setia-card">
            <div class="setia-card-header">
                <h3>لاگ سیستم</h3>
                <div class="setia-card-actions">
                    <select id="log-level-filter" class="setia-select">
                        <option value="all">همه سطوح</option>
                        <option value="info">اطلاعات</option>
                        <option value="warning">هشدار</option>
                        <option value="error">خطا</option>
                    </select>
                    <button id="clear-logs" class="setia-button setia-button-outline">پاک کردن لاگ‌ها</button>
                </div>
            </div>
            
            <div class="setia-logs-container">
                <div class="setia-log-entry info">
                    <div class="setia-log-time">14:32:15</div>
                    <div class="setia-log-level">INFO</div>
                    <div class="setia-log-message">سیستم Cron داخلی شروع به کار کرد</div>
                </div>
                
                <div class="setia-log-entry success">
                    <div class="setia-log-time">14:30:45</div>
                    <div class="setia-log-level">SUCCESS</div>
                    <div class="setia-log-message">وظیفه تولید محتوا با موفقیت انجام شد (ID: 1234)</div>
                </div>
                
                <div class="setia-log-entry warning">
                    <div class="setia-log-time">14:28:20</div>
                    <div class="setia-log-level">WARNING</div>
                    <div class="setia-log-message">استفاده از حافظه به 80% رسید</div>
                </div>
                
                <div class="setia-log-entry error">
                    <div class="setia-log-time">14:25:10</div>
                    <div class="setia-log-level">ERROR</div>
                    <div class="setia-log-message">خطا در اتصال به API: کلید نامعتبر</div>
                </div>
            </div>
        </div>

        <!-- دکمه‌های عملیات -->
        <div class="setia-actions-bar">
            <button id="save-internal-settings" class="setia-button setia-button-primary setia-button-large">
                <span class="dashicons dashicons-yes"></span>
                ذخیره تنظیمات
            </button>
            
            <button id="export-logs" class="setia-button setia-button-secondary setia-button-large">
                <span class="dashicons dashicons-download"></span>
                خروجی لاگ‌ها
            </button>
            
            <button id="system-info" class="setia-button setia-button-outline setia-button-large">
                <span class="dashicons dashicons-info"></span>
                اطلاعات سیستم
            </button>
        </div>
    </div>
</div>

<style>
.setia-internal-cron-wrap {
    direction: rtl;
    font-family: 'IRANSans', Tahoma, sans-serif;
}

.setia-system-controls {
    display: flex;
    gap: 10px;
}

.setia-status-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.setia-status-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
    border-right: 4px solid #667eea;
}

.setia-status-icon {
    font-size: 2em;
}

.setia-status-icon.running {
    animation: pulse 2s infinite;
}

.setia-status-info h4 {
    margin: 0 0 5px 0;
    color: #2c3e50;
    font-size: 14px;
}

.setia-status-value {
    font-weight: bold;
    color: #667eea;
}

.setia-queue-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.setia-queue-item {
    display: grid;
    grid-template-columns: 120px 1fr auto auto;
    gap: 20px;
    align-items: center;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
    border-right: 4px solid #667eea;
}

.setia-queue-item.processing {
    border-right-color: #ffc107;
    background: #fff8e1;
}

.setia-queue-item.pending {
    border-right-color: #17a2b8;
    background: #e1f5fe;
}

.setia-queue-status {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 12px;
    font-weight: 600;
}

.setia-status-indicator {
    width: 10px;
    height: 10px;
    border-radius: 50%;
}

.setia-status-indicator.processing {
    background: #ffc107;
    animation: pulse 1.5s infinite;
}

.setia-status-indicator.pending {
    background: #17a2b8;
}

.setia-queue-info h4 {
    margin: 0 0 5px 0;
    color: #2c3e50;
}

.setia-queue-info p {
    margin: 0 0 5px 0;
    color: #666;
    font-size: 14px;
}

.setia-queue-info small {
    color: #999;
    font-size: 12px;
}

.setia-queue-progress {
    display: flex;
    align-items: center;
    gap: 10px;
    min-width: 120px;
}

.setia-progress-bar {
    flex: 1;
    height: 8px;
    background: #e9ecef;
    border-radius: 4px;
    overflow: hidden;
}

.setia-progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #667eea, #764ba2);
    transition: width 0.3s ease;
}

.setia-progress-text {
    font-size: 12px;
    font-weight: 600;
    color: #667eea;
}

.setia-priority-badge {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 11px;
    font-weight: 600;
}

.setia-priority-badge.high {
    background: #f8d7da;
    color: #721c24;
}

.setia-priority-badge.normal {
    background: #d1ecf1;
    color: #0c5460;
}

.setia-queue-actions {
    display: flex;
    gap: 8px;
}

.setia-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin: 20px 0;
}

.setia-stat-card {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.setia-stat-icon {
    font-size: 2em;
}

.setia-stat-content h4 {
    margin: 0 0 5px 0;
    font-size: 14px;
    opacity: 0.9;
}

.setia-stat-number {
    display: block;
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 5px;
}

.setia-stat-change {
    font-size: 12px;
    opacity: 0.8;
}

.setia-settings-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.setia-toggle-switch {
    display: flex;
    align-items: center;
    gap: 10px;
}

.setia-toggle-input {
    display: none;
}

.setia-toggle-label {
    position: relative;
    width: 50px;
    height: 24px;
    background: #ccc;
    border-radius: 12px;
    cursor: pointer;
    transition: background 0.3s;
}

.setia-toggle-slider {
    position: absolute;
    top: 2px;
    right: 2px;
    width: 20px;
    height: 20px;
    background: white;
    border-radius: 50%;
    transition: transform 0.3s;
}

.setia-toggle-input:checked + .setia-toggle-label {
    background: #667eea;
}

.setia-toggle-input:checked + .setia-toggle-label .setia-toggle-slider {
    transform: translateX(-26px);
}

.setia-logs-container {
    max-height: 300px;
    overflow-y: auto;
    background: #1e1e1e;
    border-radius: 8px;
    padding: 15px;
    font-family: 'Courier New', monospace;
}

.setia-log-entry {
    display: grid;
    grid-template-columns: 80px 80px 1fr;
    gap: 15px;
    padding: 8px 0;
    border-bottom: 1px solid #333;
    font-size: 13px;
}

.setia-log-entry:last-child {
    border-bottom: none;
}

.setia-log-time {
    color: #888;
}

.setia-log-level {
    font-weight: bold;
    padding: 2px 6px;
    border-radius: 3px;
    text-align: center;
    font-size: 11px;
}

.setia-log-entry.info .setia-log-level {
    background: #17a2b8;
    color: white;
}

.setia-log-entry.success .setia-log-level {
    background: #28a745;
    color: white;
}

.setia-log-entry.warning .setia-log-level {
    background: #ffc107;
    color: #212529;
}

.setia-log-entry.error .setia-log-level {
    background: #dc3545;
    color: white;
}

.setia-log-message {
    color: #f8f9fa;
}

.setia-actions-bar {
    display: flex;
    gap: 15px;
    justify-content: center;
    margin-top: 30px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}
</style>

<script>
jQuery(document).ready(function($) {
    // کنترل‌های سیستم
    $('#start-cron').on('click', function() {
        alert('سیستم Cron داخلی شروع شد.');
        $('.setia-status-icon.running').text('🟢');
        $('.setia-status-value').first().text('در حال اجرا');
    });

    $('#stop-cron').on('click', function() {
        if (confirm('آیا مطمئن هستید که می‌خواهید سیستم Cron را متوقف کنید؟')) {
            alert('سیستم Cron داخلی متوقف شد.');
            $('.setia-status-icon.running').text('🔴');
            $('.setia-status-value').first().text('متوقف');
        }
    });

    $('#restart-cron').on('click', function() {
        if (confirm('آیا مطمئن هستید که می‌خواهید سیستم را راه‌اندازی مجدد کنید؟')) {
            alert('سیستم در حال راه‌اندازی مجدد است...');
        }
    });

    // مدیریت صف
    $('#clear-queue').on('click', function() {
        if (confirm('آیا مطمئن هستید که می‌خواهید صف وظایف را پاک کنید؟')) {
            $('.setia-queue-list').html('<p style="text-align: center; padding: 20px; color: #666;">صف وظایف خالی است.</p>');
        }
    });

    $('#refresh-queue').on('click', function() {
        alert('صف وظایف بروزرسانی شد.');
    });

    // تنظیمات
    $('#save-internal-settings').on('click', function() {
        alert('تنظیمات سیستم Cron داخلی ذخیره شد.');
    });

    $('#export-logs').on('click', function() {
        alert('لاگ‌ها در حال آماده‌سازی برای دانلود هستند...');
    });

    $('#system-info').on('click', function() {
        alert('اطلاعات سیستم:\n- PHP Version: 8.1\n- Memory Limit: 256MB\n- Max Execution Time: 300s\n- WordPress Version: 6.3');
    });

    // فیلتر لاگ‌ها
    $('#log-level-filter').on('change', function() {
        var level = $(this).val();
        if (level === 'all') {
            $('.setia-log-entry').show();
        } else {
            $('.setia-log-entry').hide();
            $('.setia-log-entry.' + level).show();
        }
    });

    $('#clear-logs').on('click', function() {
        if (confirm('آیا مطمئن هستید که می‌خواهید لاگ‌ها را پاک کنید؟')) {
            $('.setia-logs-container').html('<p style="text-align: center; padding: 20px; color: #888;">لاگ‌ها پاک شدند.</p>');
        }
    });

    // شبیه‌سازی پیشرفت
    setInterval(function() {
        var progressBar = $('.setia-progress-fill');
        if (progressBar.length > 0) {
            var currentWidth = parseInt(progressBar.css('width'));
            var newWidth = Math.min(currentWidth + 1, 100);
            progressBar.css('width', newWidth + '%');
            $('.setia-progress-text').text(newWidth + '%');

            if (newWidth >= 100) {
                setTimeout(function() {
                    $('.setia-queue-item.processing').fadeOut();
                }, 1000);
            }
        }
    }, 2000);
});
</script>
