/**
 * اسکریپت بهبودهای رابط کاربری مودال
 */
(function($) {
    'use strict';
    
    // تابع اصلی بهبودهای UI
    function initModalUIImprovements() {
        console.log('اسکریپت بهبودهای UI مودال بارگذاری شد');
        
        // اضافه کردن کلاس‌ها به فیلدها برای آیکون‌ها
        function addFieldIcons() {
            $('.setia-form-group:has(#title)').addClass('topic');
            $('.setia-form-group:has(#scheduled_date)').addClass('date');
            $('.setia-form-group:has(#scheduled_hour)').addClass('time');
            $('.setia-form-group:has(#keywords)').addClass('description');
            $('.setia-form-group:has(#instructions)').addClass('description');
            $('.setia-form-group:has(#category)').addClass('category');
            $('.setia-form-group:has(#post_status)').addClass('status');
            $('.setia-form-group:has(#tone)').addClass('tone');
            $('.setia-form-group:has(#length)').addClass('length');
        }
        
        // انیمیشن ورود برای فیلدها
        function addFormAnimations() {
            // تنظیم مجدد وضعیت انیمیشن‌ها
            $('.setia-form-group').css('opacity', 0);
            
            // اجرای انیمیشن با تاخیر
            setTimeout(function() {
                $('.setia-form-group').each(function(index) {
                    var $field = $(this);
                    setTimeout(function() {
                        $field.css({
                            'opacity': 1,
                            'transform': 'translateY(0)'
                        });
                    }, index * 50);
                });
            }, 100);
            
            // انیمیشن فوکوس
            $('.setia-form-group input, .setia-form-group textarea, .setia-form-group select').off('focus.animation blur.animation')
            .on('focus.animation', function() {
                $(this).closest('.setia-form-group').addClass('setia-focused');
            }).on('blur.animation', function() {
                $(this).closest('.setia-form-group').removeClass('setia-focused');
            });
        }
        
        // بهبود رفتار چک‌باکس‌ها
        function enhanceCheckboxes() {
            $('.setia-form-check input[type="checkbox"]').each(function() {
                var $checkbox = $(this);
                var $label = $checkbox.closest('label');
                var checkboxId = $checkbox.attr('id');
                
                // اضافه کردن کلاس به والد برای پشتیبانی از مرورگرهای قدیمی‌تر
                if (checkboxId) {
                    $label.attr('for', checkboxId);
                    
                    // اضافه کردن ID پدر برای سلکتورهای CSS ثابت
                    $label.parent().attr('id', checkboxId + '-parent');
                }
                
                // اضافه کردن checkmark اگر وجود نداشته باشد
                if (!$label.find('.checkmark').length) {
                    // ساختار جدید برای چک‌باکس‌ها
                    var checkboxText = $label.text().trim();
                    $label.text(''); // پاک کردن متن
                    $label.append('<span class="checkmark"></span>');
                    $label.append('<span class="checkbox-text">' + checkboxText + '</span>');
                }
                
                // اضافه کردن انیمیشن به چک‌باکس‌ها
                $checkbox.off('change.animation').on('change.animation', function() {
                    var $this = $(this);
                    var $parent = $this.closest('.setia-form-check');
                    
                    if ($this.is(':checked')) {
                        $parent.addClass('checked');
                        $parent.find('.checkmark').addClass('pulse-animation');
                        setTimeout(function() {
                            $parent.find('.checkmark').removeClass('pulse-animation');
                        }, 500);
                    } else {
                        $parent.removeClass('checked');
                    }
                });
                
                // اعمال وضعیت فعلی
                if ($checkbox.is(':checked')) {
                    $label.addClass('checked');
                }
            });
            
            // قرار دادن چک‌باکس‌ها در کانتینر برای استایل بهتر
            if ($('.setia-form-check').length > 0 && $('.setia-checkbox-container').length === 0) {
                $('.setia-form-check').first().parent().addClass('setia-checkbox-container');
            }
        }
        
        // بهبود نمایش فرم دو ستونه
        function enhanceTwoColumnLayout() {
            $('.setia-form-row').each(function() {
                // بررسی تعداد فرزندان
                if ($(this).children('.setia-form-group').length >= 2) {
                    $(this).addClass('setia-form-row-columns');
                }
            });
        }
        
        // بهبود نمایش آیکون‌ها
        function improveIcons() {
            // افزودن افکت هاور برای آیکون‌ها
            $('.setia-form-group label').off('mouseenter mouseleave')
            .on('mouseenter', function() {
                $(this).find('span').addClass('icon-hover');
            })
            .on('mouseleave', function() {
                $(this).find('span').removeClass('icon-hover');
            });
            
            // اطمینان از عملکرد صحیح تصاویر پیش‌نمایش نسبت ابعاد
            $('.setia-aspect-ratio-option').each(function() {
                var ratio = $(this).data('ratio');
                if (ratio) {
                    var parts = ratio.split(':');
                    if (parts.length === 2) {
                        var width = parseInt(parts[0]);
                        var height = parseInt(parts[1]);
                        var percentage = (height / width) * 100;
                        
                        if (!isNaN(percentage)) {
                            $(this).find('.aspect-preview').css('padding-bottom', percentage + '%');
                        }
                    }
                }
            });
        }
        
        // تنظیم عرض مودال بر اساس اندازه صفحه
        function setModalSize() {
            var windowWidth = $(window).width();
            var $modal = $('.setia-modal-content');
            
            if (windowWidth < 768) {
                $modal.css('width', '95%');
            } else if (windowWidth < 992) {
                $modal.css('width', '85%');
            } else {
                $modal.css('width', '80%');
                $modal.css('max-width', '1200px');
            }
        }
        
        // اجرای توابع بهبود UI
        addFieldIcons();
        addFormAnimations();
        enhanceCheckboxes();
        enhanceTwoColumnLayout();
        improveIcons();
        setModalSize();
        
        // تنظیم مجدد اندازه مودال هنگام تغییر اندازه پنجره
        $(window).on('resize', function() {
            setModalSize();
        });
    }
    
    // اجرای اولیه بعد از بارگذاری صفحه
    $(document).ready(function() {
        setTimeout(initModalUIImprovements, 100);
        
        // اجرای مجدد بعد از باز شدن مودال
        $(document).on('click', '#setia-new-schedule, .setia-action-edit', function() {
            setTimeout(initModalUIImprovements, 300);
        });
        
        // اجرای بهبودها هنگام بسته شدن مودال نیز
        $(document).on('click', '.setia-modal-close', function() {
            setTimeout(initModalUIImprovements, 500);
        });
    });
    
})(jQuery); 