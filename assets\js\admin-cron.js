/**
 * SETIA Admin Cron
 * اسکریپت جاوااسکریپت برای اجرای خودکار زمانبندی‌ها در صفحه مدیریت
 */

(function($) {
    'use strict';
    
    // تابع اجرای کرون
    function runCron() {
        // بررسی آخرین زمان اجرای کرون
        var lastRun = localStorage.getItem('setia_admin_cron_last_run') || 0;
        var now = Math.floor(Date.now() / 1000);
        var interval = setia_admin_cron.interval * 60; // تبدیل دقیقه به ثانیه
        
        // اگر زمان اجرای کرون رسیده است
        if (now - lastRun > interval) {
            // نمایش پیام در کنسول
            console.log('SETIA: اجرای کرون داخلی در صفحه مدیریت...');
            
            // ارسال درخواست اجکس
            $.ajax({
                url: setia_admin_cron.ajax_url,
                type: 'POST',
                data: {
                    action: 'setia_run_admin_cron',
                    nonce: setia_admin_cron.nonce
                },
                success: function(response) {
                    console.log('SETIA: پاسخ کرون داخلی:', response);
                    
                    // بروزرسانی زمان آخرین اجرا
                    localStorage.setItem('setia_admin_cron_last_run', now);
                },
                error: function(xhr, status, error) {
                    console.error('SETIA: خطا در اجرای کرون داخلی:', error);
                }
            });
        }
    }
    
    // اجرای کرون پس از بارگذاری صفحه
    $(document).ready(function() {
        // اجرای کرون با تأخیر 5 ثانیه پس از بارگذاری صفحه
        setTimeout(runCron, 5000);
        
        // اجرای مجدد کرون هر 30 ثانیه برای پشتیبانی از زمانبندی‌های دقیقه‌ای
        setInterval(runCron, 30000);
    });
    
})(jQuery); 