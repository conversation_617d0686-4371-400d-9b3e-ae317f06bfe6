<?php
/**
 * اجرای دستی زمانبندی از طریق URL
 */

// بارگذاری وردپرس
require_once('../../../wp-load.php');

// بررسی دسترسی ادمین
if (!current_user_can('manage_options')) {
    die('دسترسی غیرمجاز');
}

echo '<h1>اجرای دستی زمانبندی</h1>';

// دریافت شناسه زمانبندی از پارامتر URL
$schedule_id = isset($_GET['id']) ? sanitize_text_field($_GET['id']) : '';

if (empty($schedule_id)) {
    echo '<div style="color: red; font-weight: bold;">خطا: شناسه زمانبندی مشخص نشده است.</div>';
    echo '<p>برای استفاده از این ابزار، پارامتر id را به URL اضافه کنید:</p>';
    echo '<code>' . site_url() . '/wp-content/plugins/SETIA/manual-scheduler.php?id=YOUR_SCHEDULE_ID</code>';
    die();
}

// دریافت زمانبندی‌ها
$schedules = get_option('setia_content_schedules', array());

if (!isset($schedules[$schedule_id])) {
    echo '<div style="color: red; font-weight: bold;">خطا: زمانبندی با شناسه ' . esc_html($schedule_id) . ' یافت نشد.</div>';
    die();
}

$schedule = $schedules[$schedule_id];
echo '<h2>اطلاعات زمانبندی:</h2>';
echo '<ul>';
echo '<li><strong>عنوان:</strong> ' . esc_html($schedule['title']) . '</li>';
echo '<li><strong>موضوع:</strong> ' . esc_html($schedule['topic']) . '</li>';
echo '<li><strong>کلمات کلیدی:</strong> ' . esc_html($schedule['keywords']) . '</li>';
echo '<li><strong>وضعیت:</strong> ' . ($schedule['status'] === 'active' ? 'فعال' : 'غیرفعال') . '</li>';
echo '</ul>';

// بررسی وجود کلاس زمانبندی
if (!class_exists('SETIA_Scheduler')) {
    require_once('includes/scheduler.php');
    
    // اگر کلاس هنوز موجود نیست، خطا بده
    if (!class_exists('SETIA_Scheduler')) {
        echo '<div style="color: red; font-weight: bold;">خطا: کلاس زمانبندی یافت نشد.</div>';
        die();
    }
}

// بارگذاری کلاس اصلی
if (!class_exists('SETIA_Content_Generator')) {
    require_once('setia-content-generator.php');
}

echo '<h2>در حال اجرای زمانبندی...</h2>';

try {
    // ایجاد نمونه از کلاس اصلی
    $setia = new SETIA_Content_Generator();
    
    // ایجاد نمونه از کلاس زمانبندی
    $scheduler = new SETIA_Scheduler($setia);
    
    // دریافت زمانبندی‌ها
    $current_schedules = get_option('setia_content_schedules', array());
    
    if (!isset($current_schedules[$schedule_id])) {
        echo '<div style="color: red; font-weight: bold;">خطا: زمانبندی با شناسه ' . esc_html($schedule_id) . ' یافت نشد.</div>';
        die();
    }
    
    $current_schedule = $current_schedules[$schedule_id];
    
    // اگر زمانبندی غیرفعال است، نمایش پیام هشدار
    if ($current_schedule['status'] !== 'active') {
        echo '<div style="color: orange; font-weight: bold;">هشدار: زمانبندی غیرفعال است، اما به صورت دستی اجرا می‌شود.</div>';
    }
    
    // تغییر موقت وضعیت به فعال اگر غیرفعال است
    $was_inactive = false;
    if ($current_schedule['status'] !== 'active') {
        $was_inactive = true;
        $current_schedules[$schedule_id]['status'] = 'active';
        update_option('setia_content_schedules', $current_schedules);
    }
    
    // اجرای زمانبندی
    $scheduler->generate_scheduled_content($schedule_id);
    
    // بازگرداندن وضعیت به حالت قبلی
    if ($was_inactive) {
        $current_schedules = get_option('setia_content_schedules', array());
        if (isset($current_schedules[$schedule_id])) {
            $current_schedules[$schedule_id]['status'] = 'inactive';
            update_option('setia_content_schedules', $current_schedules);
        }
    }
    
    // بررسی اجرای موفق با بررسی آخرین زمان اجرا
    $updated_schedules = get_option('setia_content_schedules', array());
    
    if (isset($updated_schedules[$schedule_id]) && !empty($updated_schedules[$schedule_id]['last_run'])) {
        $last_run = $updated_schedules[$schedule_id]['last_run'];
        echo '<div style="color: green; font-weight: bold;">زمانبندی با موفقیت اجرا شد.</div>';
        echo '<p>آخرین اجرا: ' . esc_html($last_run) . '</p>';
    } else {
        echo '<div style="color: orange; font-weight: bold;">زمانبندی اجرا شد اما نتیجه نامشخص است.</div>';
        echo '<p>لطفاً لاگ‌های خطا را بررسی کنید.</p>';
    }
} catch (Exception $e) {
    echo '<div style="color: red; font-weight: bold;">خطا در اجرای زمانبندی:</div>';
    echo '<pre>' . esc_html($e->getMessage()) . '</pre>';
}

echo '<p><a href="' . admin_url('admin.php?page=setia-scheduler') . '">بازگشت به صفحه زمانبندی</a></p>'; 