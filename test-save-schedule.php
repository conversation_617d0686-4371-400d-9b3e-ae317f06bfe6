<?php
/**
 * فایل تست برای بررسی مستقیم تابع save_schedule
 */

// بارگذاری وردپرس
require_once('../../../wp-load.php');

// بررسی دسترسی ادمین
if (!current_user_can('manage_options')) {
    die('دسترسی غیرمجاز');
}

// بارگذاری کلاس زمانبندی
require_once('includes/scheduler.php');

// بارگذاری کلاس اصلی افزونه
require_once('setia-content-generator.php');

echo '<h1>تست تابع save_schedule</h1>';

// ایجاد نمونه از کلاس اصلی
$setia = new SETIA_Content_Generator();

// ایجاد نمونه از کلاس زمانبندی
$scheduler = new SETIA_Scheduler($setia);

// شبیه‌سازی داده‌های POST
$_POST = [
    'action' => 'setia_save_schedule',
    'nonce' => wp_create_nonce('setia-nonce'),
    'title' => 'عنوان تست',
    'topic' => 'موضوع تست',
    'keywords' => 'کلمه کلیدی تست',
    'category' => 1,
    'tone' => 'عادی',
    'length' => 'متوسط',
    'frequency' => 'daily',
    'status' => 'active',
    'generate_image' => 'yes',
    'schedule_id' => 0
];

echo '<h2>داده‌های POST</h2>';
echo '<pre>';
print_r($_POST);
echo '</pre>';

// تست فراخوانی مستقیم تابع
echo '<h2>نتیجه فراخوانی تابع</h2>';

// تنظیم خروجی برای جلوگیری از ارسال هدر JSON
ob_start();
$scheduler->save_schedule();
$result = ob_get_clean();

echo '<pre>';
echo htmlspecialchars($result);
echo '</pre>';

// بررسی نتیجه
echo '<h2>بررسی نتیجه</h2>';
$decoded = json_decode($result, true);

echo '<pre>';
print_r($decoded);
echo '</pre>';

// بررسی تنظیمات بعد از اجرا
echo '<h2>تنظیمات زمانبندی بعد از اجرا</h2>';
$schedules = get_option('setia_content_schedules', array());

echo '<pre>';
print_r($schedules);
echo '</pre>'; 