<?php
/**
 * SETIA Fix Scheduler
 * این فایل برای اجرای اجباری زمانبندی‌ها استفاده می‌شود
 */

// بارگذاری وردپرس
$wp_load_path = $_SERVER['DOCUMENT_ROOT'] . '/wp-load.php';

if (!file_exists($wp_load_path)) {
    // بررسی مسیرهای دیگر
    $possible_paths = array(
        dirname(dirname(dirname(__FILE__))) . '/wp-load.php',
        dirname(__FILE__, 4) . '/wp-load.php',
        dirname(dirname(dirname(dirname(__FILE__)))) . '/wp-load.php',
        dirname(dirname(__FILE__)) . '/wp-load.php',
        dirname(__FILE__) . '/../wp-load.php',
        dirname(__FILE__) . '/../../wp-load.php',
        dirname(__FILE__) . '/../../../wp-load.php',
        dirname(__FILE__) . '/../../../../wp-load.php',
        realpath($_SERVER['DOCUMENT_ROOT'] . '/../wp-load.php'),
    );
    
    foreach ($possible_paths as $path) {
        if (file_exists($path)) {
            $wp_load_path = $path;
            break;
        }
    }
}

if (file_exists($wp_load_path)) {
    require_once($wp_load_path);
} else {
    die('خطا: فایل wp-load.php یافت نشد.');
}

// بررسی دسترسی ادمین
if (!current_user_can('manage_options')) {
    wp_die('شما اجازه دسترسی به این صفحه را ندارید.');
}

// تنظیم محدودیت زمانی اجرا
ignore_user_abort(true);
set_time_limit(300); // 5 دقیقه زمان اجرا

// بررسی وجود کلاس زمانبندی
if (!class_exists('SETIA_Scheduler')) {
    $scheduler_path = plugin_dir_path(__FILE__) . 'includes/scheduler.php';
    
    if (file_exists($scheduler_path)) {
        require_once($scheduler_path);
    } else {
        die('خطا: فایل scheduler.php یافت نشد.');
    }
}

// بررسی وجود کلاس SETIA
if (!class_exists('SETIA')) {
    $setia_path = plugin_dir_path(__FILE__) . 'setia-content-generator.php';
    
    if (file_exists($setia_path)) {
        require_once($setia_path);
    } else {
        // تلاش برای بارگذاری کلاس‌های مورد نیاز
        $generator_path = plugin_dir_path(__FILE__) . 'includes/class-content-generator.php';
        if (file_exists($generator_path)) {
            require_once($generator_path);
            
            class SETIA {
                public $content_generator;
                
                public function __construct() {
                    $this->content_generator = new SETIA_Content_Generator();
                }
            }
        } else {
            die('خطا: فایل class-content-generator.php یافت نشد.');
        }
    }
}

// اجرای زمانبندی‌ها
if (class_exists('SETIA_Scheduler') && class_exists('SETIA')) {
    $setia = new SETIA();
    $scheduler = new SETIA_Scheduler($setia->content_generator);
    
    // دریافت زمانبندی‌های فعال
    $schedules = get_option('setia_content_schedules', array());
    
    // اجرای تمام زمانبندی‌های فعال
    $executed = array();
    
    echo '<html><head><title>اجرای اجباری زمانبندی‌ها</title>';
    echo '<meta charset="utf-8">';
    echo '<style>
        body { font-family: Tahoma, Arial, sans-serif; direction: rtl; padding: 20px; }
        h1 { color: #2271b1; }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
        .schedule-item { border: 1px solid #ccc; padding: 10px; margin-bottom: 10px; border-radius: 5px; }
        pre { background: #f5f5f5; padding: 10px; overflow: auto; }
    </style>';
    echo '</head><body>';
    echo '<h1>اجرای اجباری زمانبندی‌ها</h1>';
    
    if (empty($schedules)) {
        echo '<p class="warning">هیچ زمانبندی یافت نشد!</p>';
    } else {
        echo '<p>تعداد کل زمانبندی‌ها: ' . count($schedules) . '</p>';
        
        foreach ($schedules as $schedule_id => $schedule) {
            echo '<div class="schedule-item">';
            echo '<h3>زمانبندی: ' . esc_html($schedule['title']) . ' (ID: ' . $schedule_id . ')</h3>';
            echo '<p>وضعیت: ' . ($schedule['status'] === 'active' ? '<span class="success">فعال</span>' : '<span class="warning">غیرفعال</span>') . '</p>';
            echo '<p>تناوب: ' . esc_html($schedule['frequency']) . '</p>';
            echo '<p>آخرین اجرا: ' . (empty($schedule['last_run']) ? 'هرگز' : esc_html($schedule['last_run'])) . '</p>';
            
            // اجرای زمانبندی بدون توجه به وضعیت فعال یا غیرفعال
            try {
                echo '<p>در حال اجرای زمانبندی...</p>';
                ob_flush();
                flush();
                
                $result = $scheduler->generate_scheduled_content($schedule_id);
                
                if ($result) {
                    echo '<p class="success">زمانبندی با موفقیت اجرا شد.</p>';
                    $executed[] = $schedule_id;
                } else {
                    echo '<p class="error">خطا در اجرای زمانبندی.</p>';
                }
            } catch (Exception $e) {
                echo '<p class="error">خطا: ' . $e->getMessage() . '</p>';
            }
            
            echo '</div>';
        }
        
        echo '<h2>نتیجه:</h2>';
        echo '<p>تعداد زمانبندی‌های اجرا شده: ' . count($executed) . '</p>';
        
        if (!empty($executed)) {
            echo '<p class="success">زمانبندی‌های زیر با موفقیت اجرا شدند:</p>';
            echo '<ul>';
            foreach ($executed as $id) {
                echo '<li>' . $schedules[$id]['title'] . ' (ID: ' . $id . ')</li>';
            }
            echo '</ul>';
        }
    }
    
    echo '<p><a href="' . admin_url('admin.php?page=setia-scheduler') . '">بازگشت به صفحه زمانبندی</a></p>';
    echo '</body></html>';
} else {
    die('خطا: کلاس‌های مورد نیاز یافت نشدند.');
}
