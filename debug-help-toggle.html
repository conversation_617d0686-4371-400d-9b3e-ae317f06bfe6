<!DOCTYPE html>
<html dir="rtl" lang="fa">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تست Debug Help Toggle</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <style>
        body {
            font-family: 'Tahoma', sans-serif;
            background: #f0f0f1;
            margin: 0;
            padding: 20px;
            direction: rtl;
        }
        .debug-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .setia-help-toggle {
            background: linear-gradient(135deg, rgba(248, 250, 252, 0.9) 0%, rgba(241, 245, 249, 0.9) 100%);
            border: none;
            padding: 12px 20px;
            color: #667eea;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            text-align: right;
            transition: all 0.3s ease;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: space-between;
            border-radius: 8px;
            margin-bottom: 0;
            width: 100%;
        }
        .setia-help-toggle::after {
            content: '▼';
            transition: transform 0.3s ease;
            font-size: 12px;
            opacity: 0.7;
        }
        .setia-help-toggle.active::after {
            transform: rotate(180deg);
        }
        .setia-help-toggle:hover {
            background: linear-gradient(135deg, rgba(241, 245, 249, 0.9) 0%, rgba(226, 232, 240, 0.9) 100%);
            color: #5a67d8;
            transform: translateY(-1px);
        }
        .setia-help-steps {
            padding: 24px;
            background: #f8fafc;
            border-top: 1px solid #e5e7eb;
            display: none;
            border-radius: 0 0 8px 8px;
        }
        .setia-help-steps ol {
            margin: 0 0 12px 0;
            padding-right: 20px;
        }
        .setia-help-steps li {
            margin-bottom: 8px;
            color: #374151;
            font-size: 13px;
            line-height: 1.4;
        }
        .debug-info {
            background: #1f2937;
            color: #f9fafb;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
        .test-button {
            background: #667eea;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #5a67d8;
        }
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-weight: bold;
        }
        .status.success { background: #d1fae5; color: #065f46; }
        .status.error { background: #fee2e2; color: #991b1b; }
        .status.info { background: #dbeafe; color: #1e40af; }
    </style>
</head>
<body>
    <div class="debug-container">
        <h1>🔧 Debug Help Toggle - SETIA</h1>
        
        <div class="status info">
            <strong>مرحله ۱:</strong> بررسی بارگذاری jQuery
            <div id="jquery-status">در حال بررسی...</div>
        </div>

        <div class="status info">
            <strong>مرحله ۲:</strong> تست ساختار HTML
        </div>

        <!-- تست ۱: Help Toggle اول -->
        <div style="margin: 20px 0; border: 1px solid #e5e7eb; border-radius: 8px; overflow: hidden;">
            <button type="button" class="setia-help-toggle" id="toggle-1">راهنمای دریافت کلید Gemini</button>
            <div class="setia-help-steps">
                <ol>
                    <li>به <a href="https://makersuite.google.com/app/apikey" target="_blank">Google AI Studio</a> وارد شوید</li>
                    <li>روی «Create API key» کلیک کرده و پروژه را انتخاب یا ایجاد کنید</li>
                    <li>کلید تولیدشده را کپی کرده و در این فیلد جای‌گذاری کنید</li>
                </ol>
            </div>
        </div>

        <!-- تست ۲: Help Toggle دوم -->
        <div style="margin: 20px 0; border: 1px solid #e5e7eb; border-radius: 8px; overflow: hidden;">
            <button type="button" class="setia-help-toggle" id="toggle-2">راهنمای دریافت کلید Imagine Art</button>
            <div class="setia-help-steps">
                <ol>
                    <li>در <a href="https://imagine.art/dashboard/api" target="_blank">Imagine Art Dashboard</a> لاگین کنید</li>
                    <li>دکمه «Generate new key» را انتخاب کنید</li>
                    <li>کلید صادرشده را کپی کرده و اینجا وارد کنید</li>
                </ol>
            </div>
        </div>

        <div class="status info">
            <strong>مرحله ۳:</strong> تست دستی
            <br>
            <button class="test-button" onclick="testToggle1()">تست Toggle ۱</button>
            <button class="test-button" onclick="testToggle2()">تست Toggle ۲</button>
            <button class="test-button" onclick="testBoth()">تست هر دو</button>
            <button class="test-button" onclick="clearLog()">پاک کردن Log</button>
        </div>

        <div class="debug-info" id="debug-log">
            <div>🚀 شروع Debug...</div>
        </div>
    </div>

    <script>
        let debugLog = [];
        
        function addLog(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString('fa-IR');
            debugLog.push(`[${timestamp}] ${message}`);
            updateDebugDisplay();
        }
        
        function updateDebugDisplay() {
            const logElement = document.getElementById('debug-log');
            logElement.innerHTML = debugLog.map(log => `<div>${log}</div>`).join('');
            logElement.scrollTop = logElement.scrollHeight;
        }
        
        function clearLog() {
            debugLog = [];
            updateDebugDisplay();
            addLog('🧹 Log پاک شد');
        }
        
        function testToggle1() {
            addLog('🧪 تست Toggle 1...');
            $('#toggle-1').trigger('click');
        }
        
        function testToggle2() {
            addLog('🧪 تست Toggle 2...');
            $('#toggle-2').trigger('click');
        }
        
        function testBoth() {
            addLog('🧪 تست هر دو Toggle...');
            setTimeout(() => $('#toggle-1').trigger('click'), 100);
            setTimeout(() => $('#toggle-2').trigger('click'), 600);
        }

        $(document).ready(function() {
            addLog('✅ jQuery بارگذاری شد');
            $('#jquery-status').html('✅ jQuery آماده است');
            
            addLog(`📊 تعداد دکمه‌های toggle یافت شده: ${$('.setia-help-toggle').length}`);
            
            // تست ساده toggle
            $('.setia-help-toggle').on('click', function(e) {
                e.preventDefault();
                addLog(`🖱️ کلیک روی: ${$(this).attr('id') || 'نامشخص'}`);
                
                const $toggle = $(this);
                const $steps = $toggle.siblings('.setia-help-steps');
                const isActive = $toggle.hasClass('active');
                
                addLog(`📋 وضعیت فعلی: ${isActive ? 'باز' : 'بسته'}`);
                
                // بستن سایر toggle ها
                $('.setia-help-toggle').not($toggle).removeClass('active');
                $('.setia-help-steps').not($steps).slideUp(300);
                
                // toggle کردن toggle فعلی
                if (isActive) {
                    $toggle.removeClass('active');
                    $steps.slideUp(300, function() {
                        addLog('📤 راهنما بسته شد');
                    });
                } else {
                    $toggle.addClass('active');
                    $steps.slideDown(300, function() {
                        addLog('📥 راهنما باز شد');
                    });
                }
            });
            
            addLog('🎯 Event handler ها تنظیم شدند');
            addLog('👆 حالا روی دکمه‌های "راهنمای دریافت کلید" کلیک کنید');
        });
    </script>
</body>
</html>
