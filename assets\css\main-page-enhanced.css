/**
 * Enhanced Main Page Styles for SETIA Content Generator
 */

/* Header Styles */
.setia-main-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 40px;
    border-radius: 16px;
    margin-bottom: 30px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
}

.setia-main-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
}

.setia-header-content {
    display: flex;
    align-items: center;
    gap: 20px;
    position: relative;
    z-index: 1;
    margin-bottom: 30px;
}

.setia-header-icon {
    background: rgba(255, 255, 255, 0.2);
    padding: 15px;
    border-radius: 12px;
    backdrop-filter: blur(10px);
}

.setia-header-icon svg {
    color: white;
}

.setia-header-text h1 {
    margin: 0 0 10px 0;
    font-size: 28px;
    font-weight: 700;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.setia-header-text p {
    margin: 0;
    font-size: 16px;
    opacity: 0.9;
    line-height: 1.5;
}

/* Progress Indicator */
.setia-progress-indicator {
    display: flex;
    justify-content: center;
    gap: 40px;
    position: relative;
    z-index: 1;
}

.progress-step {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    opacity: 0.6;
    transition: all 0.3s ease;
}

.progress-step.active {
    opacity: 1;
}

.step-number {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    font-size: 16px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    transition: all 0.3s ease;
}

.progress-step.active .step-number {
    background: white;
    color: #667eea;
    border-color: white;
}

.step-title {
    font-size: 14px;
    font-weight: 500;
    text-align: center;
}

/* Card Styles */
.setia-card {
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    margin-bottom: 24px;
    border: 1px solid #f0f0f0;
    overflow: hidden;
    transition: all 0.3s ease;
}

.setia-card:hover {
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
    transform: translateY(-2px);
}

.setia-card-header {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 24px 32px;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border-bottom: 1px solid #e2e8f0;
}

.setia-card-icon {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 12px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.setia-card-title h2 {
    margin: 0 0 4px 0;
    font-size: 20px;
    font-weight: 700;
    color: #1e293b;
}

.setia-card-title p {
    margin: 0;
    color: #64748b;
    font-size: 14px;
    line-height: 1.4;
}

.setia-card-content {
    padding: 32px;
}

/* Form Styles */
.setia-form-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 24px;
    margin-bottom: 24px;
}

.setia-form-grid-3 {
    grid-template-columns: 1fr 1fr 1fr;
}

.setia-form-grid-2 {
    grid-template-columns: 1fr 1fr;
}

.setia-form-group {
    margin-bottom: 24px;
}

.setia-label {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
    color: #374151;
    margin-bottom: 8px;
    font-size: 14px;
}

.label-icon {
    font-size: 16px;
}

.required-indicator {
    color: #ef4444;
    font-weight: 700;
}

.setia-input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
}

.setia-input, .setia-select, .setia-textarea {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    font-size: 14px;
    transition: all 0.3s ease;
    background: #fafafa;
    font-family: inherit;
}

.setia-input:focus, .setia-select:focus, .setia-textarea:focus {
    outline: none;
    border-color: #667eea;
    background: white;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.setia-textarea {
    min-height: 100px;
    resize: vertical;
}

.input-validation {
    position: absolute;
    left: 12px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.input-validation.valid {
    background: #10b981;
}

.input-validation.invalid {
    background: #ef4444;
}

.setia-field-description {
    margin: 8px 0 0 0;
    color: #6b7280;
    font-size: 13px;
    line-height: 1.4;
}

.keywords-counter, .character-counter {
    margin-top: 8px;
    font-size: 12px;
    color: #9ca3af;
    text-align: left;
}

/* Options Section */
.setia-options-section {
    margin: 32px 0;
    padding: 24px;
    background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
    border-radius: 12px;
    border: 1px solid #bae6fd;
}

.setia-subsection-title {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 18px;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 16px;
}

.subsection-icon {
    font-size: 20px;
}

.subsection-description {
    color: #64748b;
    font-size: 14px;
    margin-bottom: 20px;
}

.setia-toggle-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.setia-toggle-item {
    display: flex;
    align-items: flex-start;
    gap: 16px;
    padding: 20px;
    background: white;
    border-radius: 12px;
    border: 2px solid #e5e7eb;
    transition: all 0.3s ease;
}

.setia-toggle-item:hover {
    border-color: #667eea;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.1);
}

/* Toggle Switch */
.setia-toggle {
    display: none;
}

.setia-toggle-label {
    position: relative;
    width: 50px;
    height: 28px;
    background: #e5e7eb;
    border-radius: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.toggle-slider {
    position: absolute;
    top: 2px;
    left: 2px;
    width: 24px;
    height: 24px;
    background: white;
    border-radius: 50%;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.setia-toggle:checked + .setia-toggle-label {
    background: #667eea;
}

.setia-toggle:checked + .setia-toggle-label .toggle-slider {
    transform: translateX(22px);
}

.toggle-title {
    display: flex;
    align-items: center;
    gap: 8px;
    margin: 0 0 8px 0;
    font-size: 16px;
    font-weight: 600;
    color: #1e293b;
}

.toggle-icon {
    font-size: 18px;
}

.toggle-description {
    margin: 0;
    color: #64748b;
    font-size: 14px;
    line-height: 1.4;
}

/* Image Options */
.setia-image-options-container {
    margin-top: 24px;
    padding: 24px;
    background: linear-gradient(135deg, #fef3e2 0%, #fde68a 100%);
    border-radius: 12px;
    border: 1px solid #fbbf24;
    animation: slideDown 0.3s ease;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.setia-image-options-header {
    margin-bottom: 24px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .setia-main-header {
        padding: 24px;
    }
    
    .setia-header-content {
        flex-direction: column;
        text-align: center;
        gap: 16px;
    }
    
    .setia-progress-indicator {
        gap: 20px;
    }
    
    .setia-form-grid,
    .setia-form-grid-3,
    .setia-form-grid-2 {
        grid-template-columns: 1fr;
        gap: 16px;
    }
    
    .setia-toggle-grid {
        grid-template-columns: 1fr;
    }
    
    .setia-card-header {
        padding: 20px 24px;
        flex-direction: column;
        text-align: center;
        gap: 12px;
    }
    
    .setia-card-content {
        padding: 24px;
    }
}

/* Button Styles */
.setia-button {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 12px 24px;
    border: none;
    border-radius: 12px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    font-family: inherit;
    position: relative;
    overflow: hidden;
}

.setia-button-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.setia-button-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

.setia-button-secondary {
    background: #f8fafc;
    color: #475569;
    border: 2px solid #e2e8f0;
}

.setia-button-secondary:hover {
    background: #f1f5f9;
    border-color: #cbd5e1;
}

.setia-button-success {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

.setia-button-large {
    padding: 16px 32px;
    font-size: 16px;
}

.button-icon {
    font-size: 16px;
}

.button-loader {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* Form Actions */
.setia-form-actions {
    margin-top: 40px;
    padding-top: 24px;
    border-top: 1px solid #e5e7eb;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 20px;
}

.setia-generation-info {
    display: flex;
    gap: 24px;
    align-items: center;
}

.info-item {
    display: flex;
    align-items: center;
    gap: 6px;
    color: #6b7280;
    font-size: 13px;
}

.info-icon {
    font-size: 14px;
}

/* Result Container */
.setia-result-container {
    animation: fadeInUp 0.5s ease;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.setia-result-header {
    display: flex;
    align-items: center;
    gap: 20px;
    padding: 24px 32px;
    background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%);
    border-bottom: 1px solid #a7f3d0;
}

.setia-result-icon {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
    padding: 12px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

.setia-result-title h2 {
    margin: 0 0 4px 0;
    font-size: 20px;
    font-weight: 700;
    color: #1e293b;
}

.setia-result-title p {
    margin: 0;
    color: #64748b;
    font-size: 14px;
}

.setia-result-stats {
    display: flex;
    gap: 20px;
    margin-right: auto;
}

.stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
}

.stat-value {
    font-size: 20px;
    font-weight: 700;
    color: #059669;
}

.stat-label {
    font-size: 12px;
    color: #6b7280;
}

/* Tabs */
.setia-result-tabs {
    display: flex;
    background: #f8fafc;
    border-bottom: 1px solid #e2e8f0;
}

.setia-tab-button {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 16px 24px;
    background: none;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
    color: #64748b;
    border-bottom: 3px solid transparent;
}

.setia-tab-button.active {
    color: #667eea;
    background: white;
    border-bottom-color: #667eea;
}

.setia-tab-button:hover:not(.active) {
    background: #f1f5f9;
    color: #475569;
}

.tab-icon {
    font-size: 16px;
}

.setia-tab-content {
    display: none;
    padding: 32px;
}

.setia-tab-content.active {
    display: block;
}

/* Content Preview */
.setia-content-preview {
    background: #fafafa;
    border: 1px solid #e5e7eb;
    border-radius: 12px;
    padding: 24px;
    margin: 20px 0;
    max-height: 500px;
    overflow-y: auto;
    line-height: 1.7;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.05);
}

.setia-optimized-title-container {
    margin-bottom: 20px;
}

.setia-title-badge {
    display: inline-block;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    margin-bottom: 8px;
}

.setia-optimized-title {
    margin: 0;
    font-size: 24px;
    font-weight: 700;
    color: #1e293b;
    line-height: 1.3;
}

/* SEO Preview */
.setia-seo-preview {
    max-width: 600px;
}

.seo-preview-title {
    margin: 0 0 20px 0;
    font-size: 18px;
    font-weight: 600;
    color: #1e293b;
}

.setia-seo-card {
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.setia-seo-title {
    font-size: 18px;
    color: #1a0dab;
    margin-bottom: 4px;
    font-weight: 400;
    text-decoration: none;
    cursor: pointer;
}

.setia-seo-title:hover {
    text-decoration: underline;
}

.setia-seo-url {
    color: #006621;
    font-size: 14px;
    margin-bottom: 4px;
}

.setia-seo-description {
    color: #545454;
    font-size: 14px;
    line-height: 1.4;
}

.seo-metrics {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16px;
}

.seo-metric {
    background: #f8fafc;
    padding: 12px;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
}

.metric-label {
    font-weight: 600;
    color: #374151;
    font-size: 13px;
    display: block;
    margin-bottom: 4px;
}

/* Image Section */
.setia-image-section {
    text-align: center;
}

.image-section-title {
    margin: 0 0 20px 0;
    font-size: 18px;
    font-weight: 600;
    color: #1e293b;
}

.setia-image-preview-container {
    background: #f8fafc;
    border: 2px dashed #cbd5e1;
    border-radius: 12px;
    padding: 40px;
    text-align: center;
    transition: all 0.3s ease;
}

.setia-image-preview-container img {
    max-width: 100%;
    height: auto;
    border-radius: 8px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    transition: all 0.3s ease;
}

.setia-image-preview-container img:hover {
    transform: scale(1.02);
}

/* Result Actions */
.setia-result-actions {
    margin-top: 32px;
    padding-top: 24px;
    border-top: 1px solid #e5e7eb;
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.primary-actions {
    display: flex;
    justify-content: center;
    gap: 12px;
}

.secondary-actions {
    display: flex;
    justify-content: center;
    gap: 8px;
    flex-wrap: wrap;
}

/* Loading States */
.setia-button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
}

.setia-button.loading .button-text {
    opacity: 0;
}

.setia-button.loading .button-loader {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
}

/* Responsive for Result Section */
@media (max-width: 768px) {
    .setia-result-header {
        flex-direction: column;
        text-align: center;
        gap: 16px;
    }

    .setia-result-stats {
        margin: 0;
    }

    .setia-tab-content {
        padding: 24px;
    }

    .seo-metrics {
        grid-template-columns: 1fr;
    }

    .primary-actions,
    .secondary-actions {
        flex-direction: column;
        align-items: center;
    }

    .setia-generation-info {
        flex-direction: column;
        gap: 12px;
    }
}

/* Notifications */
.setia-notification {
    position: fixed;
    top: 32px;
    right: 32px;
    padding: 16px 24px;
    border-radius: 12px;
    color: white;
    font-weight: 600;
    z-index: 9999;
    transform: translateX(400px);
    transition: transform 0.3s ease;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    max-width: 400px;
}

.setia-notification.show {
    transform: translateX(0);
}

.setia-notification-success {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.setia-notification-error {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
}

.setia-notification-info {
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
}

/* Enhanced Generation Progress Modal */
.setia-generation-progress {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 10000;
    display: flex;
    align-items: center;
    justify-content: center;
    animation: fadeIn 0.4s ease;
}

.progress-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(8px);
}

.progress-content {
    background: white;
    border-radius: 20px;
    max-width: 600px;
    width: 95%;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
    position: relative;
    z-index: 1;
    animation: slideInUp 0.4s ease;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* Progress Header */
.progress-header {
    text-align: center;
    padding: 32px 32px 24px 32px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 20px 20px 0 0;
    position: relative;
    overflow: hidden;
}

.progress-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
}

.progress-main-icon {
    position: relative;
    z-index: 1;
    margin-bottom: 16px;
}

.progress-main-icon svg {
    color: white;
    animation: float 3s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

.progress-header h2 {
    margin: 0 0 8px 0;
    font-size: 24px;
    font-weight: 700;
    position: relative;
    z-index: 1;
}

.progress-subtitle {
    margin: 0;
    opacity: 0.9;
    font-size: 16px;
    position: relative;
    z-index: 1;
}

/* Progress Bar */
.progress-bar-container {
    padding: 24px 32px;
    background: #f8fafc;
    border-bottom: 1px solid #e2e8f0;
}

.progress-bar {
    width: 100%;
    height: 12px;
    background: #e5e7eb;
    border-radius: 6px;
    overflow: hidden;
    margin-bottom: 12px;
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #667eea 0%, #764ba2 50%, #10b981 100%);
    border-radius: 6px;
    transition: width 0.6s ease;
    position: relative;
    overflow: hidden;
}

.progress-fill::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent 0%, rgba(255,255,255,0.3) 50%, transparent 100%);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.progress-percentage {
    text-align: center;
    font-weight: 700;
    font-size: 18px;
    color: #667eea;
}

/* Progress Steps */
.progress-steps-container {
    padding: 24px 32px;
    max-height: 400px;
    overflow-y: auto;
}

.progress-step-item {
    display: flex;
    align-items: flex-start;
    gap: 16px;
    padding: 16px 0;
    border-bottom: 1px solid #f1f5f9;
    transition: all 0.3s ease;
    opacity: 0.5;
}

.progress-step-item:last-child {
    border-bottom: none;
}

.progress-step-item.active {
    opacity: 1;
    background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
    margin: 0 -16px;
    padding: 16px;
    border-radius: 12px;
    border-bottom: 1px solid transparent;
}

.progress-step-item.completed {
    opacity: 0.8;
}

.step-circle {
    position: relative;
    width: 48px;
    height: 48px;
    border-radius: 50%;
    background: #f1f5f9;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    transition: all 0.3s ease;
    border: 3px solid #e2e8f0;
}

.progress-step-item.active .step-circle {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-color: #667eea;
    color: white;
}

.progress-step-item.completed .step-circle {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    border-color: #10b981;
    color: white;
}

.step-circle.pulsing {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { box-shadow: 0 0 0 0 rgba(102, 126, 234, 0.7); }
    70% { box-shadow: 0 0 0 10px rgba(102, 126, 234, 0); }
    100% { box-shadow: 0 0 0 0 rgba(102, 126, 234, 0); }
}

.step-icon {
    font-size: 20px;
    z-index: 2;
    position: relative;
}

.step-loader {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 24px;
    height: 24px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    opacity: 0;
    z-index: 1;
}

.progress-step-item.active .step-loader {
    opacity: 1;
}

.step-content {
    flex: 1;
    text-align: right;
}

.step-title {
    margin: 0 0 4px 0;
    font-size: 16px;
    font-weight: 600;
    color: #1e293b;
}

.step-description {
    margin: 0 0 8px 0;
    font-size: 14px;
    color: #64748b;
    line-height: 1.4;
}

.step-status {
    font-size: 12px;
    font-weight: 600;
    padding: 4px 8px;
    border-radius: 12px;
    display: inline-block;
    background: #f1f5f9;
    color: #64748b;
}

.progress-step-item.active .step-status {
    background: #dbeafe;
    color: #1d4ed8;
}

.progress-step-item.completed .step-status {
    background: #dcfce7;
    color: #166534;
}

/* Progress Footer */
.progress-footer {
    padding: 24px 32px;
    background: #f8fafc;
    border-radius: 0 0 20px 20px;
    border-top: 1px solid #e2e8f0;
}

.progress-tips {
    margin-bottom: 20px;
}

.tip-item {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
    font-size: 14px;
    color: #64748b;
}

.tip-icon {
    font-size: 16px;
}

.progress-actions {
    text-align: center;
}

.cancel-generation-btn {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 6px;
}

.cancel-generation-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
}

.btn-icon {
    font-size: 14px;
}

/* Enhanced Scrollbar for Progress Modal */
.progress-steps-container::-webkit-scrollbar {
    width: 6px;
}

.progress-steps-container::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 3px;
}

.progress-steps-container::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 3px;
}

.progress-steps-container::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
}

/* Mobile Responsive for Progress Modal */
@media (max-width: 768px) {
    .progress-content {
        width: 98%;
        margin: 10px;
        max-height: 95vh;
    }

    .progress-header {
        padding: 24px 20px 20px 20px;
    }

    .progress-header h2 {
        font-size: 20px;
    }

    .progress-subtitle {
        font-size: 14px;
    }

    .progress-bar-container,
    .progress-steps-container,
    .progress-footer {
        padding: 20px;
    }

    .progress-step-item {
        gap: 12px;
    }

    .step-circle {
        width: 40px;
        height: 40px;
    }

    .step-icon {
        font-size: 18px;
    }

    .step-title {
        font-size: 15px;
    }

    .step-description {
        font-size: 13px;
    }

    .progress-step-item.active {
        margin: 0 -12px;
        padding: 12px;
    }
}

/* Additional Animations */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* Success Animation for Completed Steps */
.progress-step-item.completed .step-circle {
    animation: successPulse 0.6s ease;
}

@keyframes successPulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

/* Notification Enhancement for Progress */
.setia-notification.progress-notification {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    border-left: 4px solid #1e40af;
}

/* Loading State for Main Button */
.setia-button.generating {
    background: linear-gradient(135deg, #94a3b8 0%, #64748b 100%);
    cursor: not-allowed;
    transform: none !important;
}

.setia-button.generating:hover {
    transform: none !important;
    box-shadow: 0 4px 12px rgba(148, 163, 184, 0.3) !important;
}

/* Progress Bar Glow Effect */
.progress-fill {
    box-shadow: 0 0 10px rgba(102, 126, 234, 0.5);
}

/* Step Connection Lines */
.progress-step-item:not(:last-child)::after {
    content: '';
    position: absolute;
    left: 24px;
    top: 64px;
    width: 2px;
    height: 32px;
    background: #e2e8f0;
    z-index: 0;
}

.progress-step-item.completed:not(:last-child)::after {
    background: linear-gradient(to bottom, #10b981, #e2e8f0);
}

.progress-steps-container {
    position: relative;
}

/* Enhanced Hover Effects */
.progress-step-item:hover {
    background: rgba(102, 126, 234, 0.05);
    margin: 0 -16px;
    padding: 16px;
    border-radius: 12px;
    border-bottom: 1px solid transparent;
}

.progress-step-item.active:hover {
    background: linear-gradient(135deg, #e0f2fe 0%, #bae6fd 100%);
}

/* Accessibility Improvements */
.progress-content {
    outline: none;
}

.cancel-generation-btn:focus {
    outline: 2px solid #ef4444;
    outline-offset: 2px;
}

/* Print Styles for Progress Modal */
@media print {
    .setia-generation-progress {
        display: none !important;
    }
}

/* Enhanced Form Elements */
.setia-input.error,
.setia-textarea.error {
    border-color: #ef4444;
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.setia-input.success,
.setia-textarea.success {
    border-color: #10b981;
    box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
}

/* Improved Scrollbar */
.setia-content-preview::-webkit-scrollbar {
    width: 8px;
}

.setia-content-preview::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 4px;
}

.setia-content-preview::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 4px;
}

.setia-content-preview::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
}

/* Enhanced Hover Effects */
.setia-form-group:hover .setia-input,
.setia-form-group:hover .setia-select,
.setia-form-group:hover .setia-textarea {
    border-color: #cbd5e1;
}

.setia-toggle-item:hover .toggle-title {
    color: #667eea;
}

/* Loading Animation for Content Preview */
.setia-content-preview.loading {
    position: relative;
    min-height: 200px;
}

.setia-content-preview.loading::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 40px;
    height: 40px;
    border: 4px solid #f3f4f6;
    border-top: 4px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* Skeleton Loading for Results */
.skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

.skeleton-text {
    height: 16px;
    border-radius: 4px;
    margin-bottom: 8px;
}

.skeleton-title {
    height: 24px;
    border-radius: 4px;
    margin-bottom: 16px;
    width: 70%;
}

/* Enhanced Focus States */
.setia-input:focus,
.setia-select:focus,
.setia-textarea:focus {
    transform: translateY(-1px);
}

.setia-button:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.3);
}

/* Print Styles */
@media print {
    .setia-main-header,
    .setia-form-container,
    .setia-result-actions,
    .setia-result-tabs {
        display: none !important;
    }

    .setia-result-container {
        box-shadow: none !important;
        border: none !important;
    }

    .setia-content-preview {
        max-height: none !important;
        overflow: visible !important;
        box-shadow: none !important;
        border: 1px solid #ccc !important;
    }
}
