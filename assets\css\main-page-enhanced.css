/**
 * Enhanced Main Page Styles for SETIA Content Generator
 */

/* ===== MAIN TABS ===== */
.setia-main-tabs {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin: 1.5rem 0;
    padding: 0.5rem;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
    border-radius: 15px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.setia-main-tab-button {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    background: transparent;
    border: none;
    border-radius: 10px;
    color: #666;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.setia-main-tab-button:hover {
    background: linear-gradient(135deg, rgba(138, 43, 226, 0.1), rgba(75, 0, 130, 0.1));
    color: #8a2be2;
    transform: translateY(-2px);
}

.setia-main-tab-button.active {
    background: linear-gradient(135deg, #8a2be2, #4b0082);
    color: white;
    box-shadow: 0 4px 15px rgba(138, 43, 226, 0.3);
}

.setia-main-tab-button .tab-icon {
    font-size: 16px;
}

.setia-main-tab-content {
    display: none;
    animation: fadeIn 0.3s ease;
}

.setia-main-tab-content.active {
    display: block;
}

/* ===== WOOCOMMERCE NOTICE ===== */
.setia-woocommerce-notice {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1.5rem;
    background: linear-gradient(135deg, rgba(255, 193, 7, 0.1), rgba(255, 152, 0, 0.1));
    border: 1px solid rgba(255, 193, 7, 0.3);
    border-radius: 12px;
    margin-bottom: 2rem;
}

.setia-woocommerce-notice .notice-icon {
    font-size: 2rem;
    color: #ff9800;
}

.setia-woocommerce-notice .notice-content h3 {
    margin: 0 0 0.5rem 0;
    color: #ff9800;
    font-size: 1.1rem;
}

.setia-woocommerce-notice .notice-content p {
    margin: 0 0 1rem 0;
    color: #666;
    line-height: 1.5;
}

/* ===== PRODUCT FORM STYLES ===== */
.setia-product-preview-container {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 1.5rem;
    border: 1px solid #e9ecef;
}

.setia-product-images-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

.setia-product-image-item {
    background: white;
    border-radius: 12px;
    padding: 1rem;
    border: 1px solid #e9ecef;
    text-align: center;
    transition: all 0.3s ease;
}

.setia-product-image-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.setia-product-image-item img {
    width: 100%;
    height: 150px;
    object-fit: cover;
    border-radius: 8px;
    margin-bottom: 0.5rem;
}

.setia-product-schema-container {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 1.5rem;
    border: 1px solid #e9ecef;
}

.setia-product-schema-container pre {
    background: #2d3748;
    color: #e2e8f0;
    padding: 1rem;
    border-radius: 8px;
    overflow-x: auto;
    font-size: 0.9rem;
    line-height: 1.4;
}

/* Header Styles */
.setia-main-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 40px;
    border-radius: 16px;
    margin-bottom: 30px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
}

.setia-main-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
}

.setia-header-content {
    display: flex;
    align-items: center;
    gap: 20px;
    position: relative;
    z-index: 1;
    margin-bottom: 30px;
}

.setia-header-icon {
    background: rgba(255, 255, 255, 0.2);
    padding: 15px;
    border-radius: 12px;
    backdrop-filter: blur(10px);
}

.setia-header-icon svg {
    color: white;
}

.setia-header-text h1 {
    margin: 0 0 10px 0;
    font-size: 28px;
    font-weight: 700;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.setia-header-text p {
    margin: 0;
    font-size: 16px;
    opacity: 0.9;
    line-height: 1.5;
}

/* Modern Progress Indicator */
.setia-progress-indicator {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 0;
    position: relative;
    z-index: 1;
    margin: 40px 0;
    padding: 0 20px;
}

.progress-step {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12px;
    position: relative;
    flex: 1;
    max-width: 200px;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    opacity: 0.5;
    transform: scale(0.95);
}

.progress-step.active {
    opacity: 1;
    transform: scale(1);
}

.progress-step.completed {
    opacity: 0.8;
}

.step-number {
    width: 56px;
    height: 56px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.15);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    font-size: 18px;
    border: 3px solid rgba(255, 255, 255, 0.2);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    backdrop-filter: blur(10px);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.progress-step.active .step-number {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    color: #667eea;
    border-color: #ffffff;
    box-shadow: 0 12px 40px rgba(102, 126, 234, 0.3), 0 0 0 4px rgba(255, 255, 255, 0.2);
    transform: scale(1.1);
}

.progress-step.completed .step-number {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
    border-color: #10b981;
    box-shadow: 0 8px 32px rgba(16, 185, 129, 0.4);
}

.progress-step.completed .step-number::after {
    content: '✓';
    position: absolute;
    font-size: 20px;
    font-weight: 900;
    animation: checkmarkPop 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

@keyframes checkmarkPop {
    0% { transform: scale(0) rotate(-180deg); opacity: 0; }
    50% { transform: scale(1.2) rotate(-90deg); opacity: 1; }
    100% { transform: scale(1) rotate(0deg); opacity: 1; }
}

.step-title {
    font-size: 15px;
    font-weight: 600;
    text-align: center;
    color: rgba(255, 255, 255, 0.8);
    transition: all 0.3s ease;
    line-height: 1.4;
    min-height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.progress-step.active .step-title {
    color: white;
    font-weight: 700;
    text-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

/* Progress Connection Lines */
.progress-step:not(:last-child)::before {
    content: '';
    position: absolute;
    top: 28px;
    right: -50%;
    width: 100%;
    height: 3px;
    background: linear-gradient(90deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.1) 100%);
    z-index: -1;
    transition: all 0.6s ease;
    border-radius: 2px;
}

.progress-step.completed:not(:last-child)::before {
    background: linear-gradient(90deg, #10b981 0%, rgba(16, 185, 129, 0.6) 100%);
    box-shadow: 0 0 12px rgba(16, 185, 129, 0.4);
}

.progress-step.active:not(:last-child)::before {
    background: linear-gradient(90deg, rgba(255, 255, 255, 0.4) 0%, rgba(255, 255, 255, 0.2) 100%);
    animation: progressPulse 2s infinite;
}

@keyframes progressPulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.6; }
}

/* Card Styles */
.setia-card {
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    margin-bottom: 24px;
    border: 1px solid #f0f0f0;
    overflow: hidden;
    transition: all 0.3s ease;
}

.setia-card:hover {
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
    transform: translateY(-2px);
}

.setia-card-header {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 24px 32px;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border-bottom: 1px solid #e2e8f0;
}

.setia-card-icon {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 12px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.setia-card-title h2 {
    margin: 0 0 4px 0;
    font-size: 20px;
    font-weight: 700;
    color: #1e293b;
}

.setia-card-title p {
    margin: 0;
    color: #64748b;
    font-size: 14px;
    line-height: 1.4;
}

.setia-card-content {
    padding: 32px;
}

/* Form Styles */
.setia-form-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 24px;
    margin-bottom: 24px;
}

.setia-form-grid-3 {
    grid-template-columns: 1fr 1fr 1fr;
}

.setia-form-grid-2 {
    grid-template-columns: 1fr 1fr;
}

.setia-form-group {
    margin-bottom: 24px;
}

.setia-label {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
    color: #374151;
    margin-bottom: 8px;
    font-size: 14px;
}

.label-icon {
    font-size: 16px;
}

.required-indicator {
    color: #ef4444;
    font-weight: 700;
}

.setia-input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
}

.setia-input, .setia-select, .setia-textarea {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    font-size: 14px;
    transition: all 0.3s ease;
    background: #fafafa;
    font-family: inherit;
}

.setia-input:focus, .setia-select:focus, .setia-textarea:focus {
    outline: none;
    border-color: #667eea;
    background: white;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.setia-textarea {
    min-height: 100px;
    resize: vertical;
}

.input-validation {
    position: absolute;
    left: 12px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.input-validation.valid {
    background: #10b981;
}

.input-validation.invalid {
    background: #ef4444;
}

.setia-field-description {
    margin: 8px 0 0 0;
    color: #6b7280;
    font-size: 13px;
    line-height: 1.4;
}

.keywords-counter, .character-counter {
    margin-top: 8px;
    font-size: 12px;
    color: #9ca3af;
    text-align: left;
}

/* Options Section */
.setia-options-section {
    margin: 32px 0;
    padding: 24px;
    background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
    border-radius: 12px;
    border: 1px solid #bae6fd;
}

.setia-subsection-title {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 18px;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 16px;
}

.subsection-icon {
    font-size: 20px;
}

.subsection-description {
    color: #64748b;
    font-size: 14px;
    margin-bottom: 20px;
}

.setia-toggle-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.setia-toggle-item {
    display: flex;
    align-items: flex-start;
    gap: 16px;
    padding: 20px;
    background: white;
    border-radius: 12px;
    border: 2px solid #e5e7eb;
    transition: all 0.3s ease;
}

.setia-toggle-item:hover {
    border-color: #667eea;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.1);
}

/* Toggle Switch */
.setia-toggle {
    display: none;
}

.setia-toggle-label {
    position: relative;
    width: 50px;
    height: 28px;
    background: #e5e7eb;
    border-radius: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.toggle-slider {
    position: absolute;
    top: 2px;
    left: 2px;
    width: 24px;
    height: 24px;
    background: white;
    border-radius: 50%;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.setia-toggle:checked + .setia-toggle-label {
    background: #667eea;
}

.setia-toggle:checked + .setia-toggle-label .toggle-slider {
    transform: translateX(22px);
}

.toggle-title {
    display: flex;
    align-items: center;
    gap: 8px;
    margin: 0 0 8px 0;
    font-size: 16px;
    font-weight: 600;
    color: #1e293b;
}

.toggle-icon {
    font-size: 18px;
}

.toggle-description {
    margin: 0;
    color: #64748b;
    font-size: 14px;
    line-height: 1.4;
}

/* Image Options */
.setia-image-options-container {
    margin-top: 24px;
    padding: 24px;
    background: linear-gradient(135deg, #fef3e2 0%, #fde68a 100%);
    border-radius: 12px;
    border: 1px solid #fbbf24;
    animation: slideDown 0.3s ease;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.setia-image-options-header {
    margin-bottom: 24px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .setia-main-header {
        padding: 24px;
    }
    
    .setia-header-content {
        flex-direction: column;
        text-align: center;
        gap: 16px;
    }
    
    .setia-progress-indicator {
        gap: 20px;
    }
    
    .setia-form-grid,
    .setia-form-grid-3,
    .setia-form-grid-2 {
        grid-template-columns: 1fr;
        gap: 16px;
    }
    
    .setia-toggle-grid {
        grid-template-columns: 1fr;
    }
    
    .setia-card-header {
        padding: 20px 24px;
        flex-direction: column;
        text-align: center;
        gap: 12px;
    }
    
    .setia-card-content {
        padding: 24px;
    }
}

/* Button Styles */
.setia-button {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 12px 24px;
    border: none;
    border-radius: 12px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    font-family: inherit;
    position: relative;
    overflow: hidden;
}

.setia-button-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.setia-button-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

.setia-button-secondary {
    background: #f8fafc;
    color: #475569;
    border: 2px solid #e2e8f0;
}

.setia-button-secondary:hover {
    background: #f1f5f9;
    border-color: #cbd5e1;
}

.setia-button-success {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

.setia-button-large {
    padding: 16px 32px;
    font-size: 16px;
}

.button-icon {
    font-size: 16px;
}

.button-loader {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* Form Actions */
.setia-form-actions {
    margin-top: 40px;
    padding-top: 24px;
    border-top: 1px solid #e5e7eb;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 20px;
}

.setia-generation-info {
    display: flex;
    gap: 24px;
    align-items: center;
}

.info-item {
    display: flex;
    align-items: center;
    gap: 6px;
    color: #6b7280;
    font-size: 13px;
}

.info-icon {
    font-size: 14px;
}

/* Result Container */
.setia-result-container {
    animation: fadeInUp 0.5s ease;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.setia-result-header {
    display: flex;
    align-items: center;
    gap: 20px;
    padding: 24px 32px;
    background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%);
    border-bottom: 1px solid #a7f3d0;
}

.setia-result-icon {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
    padding: 12px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

.setia-result-title h2 {
    margin: 0 0 4px 0;
    font-size: 20px;
    font-weight: 700;
    color: #1e293b;
}

.setia-result-title p {
    margin: 0;
    color: #64748b;
    font-size: 14px;
}

.setia-result-stats {
    display: flex;
    gap: 20px;
    margin-right: auto;
}

.stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
}

.stat-value {
    font-size: 20px;
    font-weight: 700;
    color: #059669;
}

.stat-label {
    font-size: 12px;
    color: #6b7280;
}

/* Tabs */
.setia-result-tabs {
    display: flex;
    background: #f8fafc;
    border-bottom: 1px solid #e2e8f0;
}

.setia-tab-button {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 16px 24px;
    background: none;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
    color: #64748b;
    border-bottom: 3px solid transparent;
}

.setia-tab-button.active {
    color: #667eea;
    background: white;
    border-bottom-color: #667eea;
}

.setia-tab-button:hover:not(.active) {
    background: #f1f5f9;
    color: #475569;
}

.tab-icon {
    font-size: 16px;
}

.setia-tab-content {
    display: none;
    padding: 32px;
}

.setia-tab-content.active {
    display: block;
}

/* Content Preview */
.setia-content-preview {
    background: #fafafa;
    border: 1px solid #e5e7eb;
    border-radius: 12px;
    padding: 24px;
    margin: 20px 0;
    max-height: 500px;
    overflow-y: auto;
    line-height: 1.7;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.05);
}

.setia-optimized-title-container {
    margin-bottom: 20px;
}

.setia-title-badge {
    display: inline-block;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    margin-bottom: 8px;
}

.setia-optimized-title {
    margin: 0;
    font-size: 24px;
    font-weight: 700;
    color: #1e293b;
    line-height: 1.3;
}

/* SEO Preview */
.setia-seo-preview {
    max-width: 600px;
}

.seo-preview-title {
    margin: 0 0 20px 0;
    font-size: 18px;
    font-weight: 600;
    color: #1e293b;
}

.setia-seo-card {
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.setia-seo-title {
    font-size: 18px;
    color: #1a0dab;
    margin-bottom: 4px;
    font-weight: 400;
    text-decoration: none;
    cursor: pointer;
}

.setia-seo-title:hover {
    text-decoration: underline;
}

.setia-seo-url {
    color: #006621;
    font-size: 14px;
    margin-bottom: 4px;
}

.setia-seo-description {
    color: #545454;
    font-size: 14px;
    line-height: 1.4;
}

.seo-metrics {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16px;
}

.seo-metric {
    background: #f8fafc;
    padding: 12px;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
}

.metric-label {
    font-weight: 600;
    color: #374151;
    font-size: 13px;
    display: block;
    margin-bottom: 4px;
}

/* Image Section */
.setia-image-section {
    text-align: center;
}

.image-section-title {
    margin: 0 0 20px 0;
    font-size: 18px;
    font-weight: 600;
    color: #1e293b;
}

.setia-image-preview-container {
    background: #f8fafc;
    border: 2px dashed #cbd5e1;
    border-radius: 12px;
    padding: 40px;
    text-align: center;
    transition: all 0.3s ease;
}

.setia-image-preview-container img {
    max-width: 100%;
    height: auto;
    border-radius: 8px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    transition: all 0.3s ease;
}

.setia-image-preview-container img:hover {
    transform: scale(1.02);
}

/* Result Actions */
.setia-result-actions {
    margin-top: 32px;
    padding-top: 24px;
    border-top: 1px solid #e5e7eb;
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.primary-actions {
    display: flex;
    justify-content: center;
    gap: 12px;
}

.secondary-actions {
    display: flex;
    justify-content: center;
    gap: 8px;
    flex-wrap: wrap;
}

/* Loading States */
.setia-button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
}

.setia-button.loading .button-text {
    opacity: 0;
}

.setia-button.loading .button-loader {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    animation: spin 1s linear infinite;
}

/* Enhanced Button States */
.setia-button:not(:disabled):hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
}

.setia-button-primary:not(:disabled) {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    color: white;
}

.setia-button-success:not(:disabled) {
    background: linear-gradient(135deg, #4ade80 0%, #22c55e 100%);
    border: none;
    color: white;
}

.setia-button-secondary:not(:disabled) {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border: 1px solid #cbd5e1;
    color: #475569;
}

.setia-button-secondary:not(:disabled):hover {
    background: linear-gradient(135deg, #e2e8f0 0%, #cbd5e1 100%);
    color: #334155;
}

/* Button Icons Animation */
.setia-button .button-icon {
    transition: transform 0.2s ease;
}

.setia-button:hover .button-icon {
    transform: scale(1.1);
}

/* Result Actions Enhanced Styling */
.setia-result-actions {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border-radius: 16px;
    padding: 24px;
    margin-top: 32px;
    border: 1px solid #e2e8f0;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

.setia-result-actions.actions-enabled {
    background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
    border-color: #0ea5e9;
    box-shadow: 0 8px 25px rgba(14, 165, 233, 0.15);
    animation: actionsGlow 0.6s ease-out;
}

@keyframes actionsGlow {
    0% {
        transform: scale(1);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    }
    50% {
        transform: scale(1.02);
        box-shadow: 0 12px 30px rgba(14, 165, 233, 0.25);
    }
    100% {
        transform: scale(1);
        box-shadow: 0 8px 25px rgba(14, 165, 233, 0.15);
    }
}

/* Responsive for Result Section */
@media (max-width: 768px) {
    .setia-result-header {
        flex-direction: column;
        text-align: center;
        gap: 16px;
    }

    .setia-result-stats {
        margin: 0;
    }

    .setia-tab-content {
        padding: 24px;
    }

    .seo-metrics {
        grid-template-columns: 1fr;
    }

    .primary-actions,
    .secondary-actions {
        flex-direction: column;
        align-items: center;
    }

    .setia-generation-info {
        flex-direction: column;
        gap: 12px;
    }
}

/* Notifications */
.setia-notification {
    position: fixed;
    top: 32px;
    right: 32px;
    padding: 16px 24px;
    border-radius: 12px;
    color: white;
    font-weight: 600;
    z-index: 9999;
    transform: translateX(400px);
    transition: transform 0.3s ease;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    max-width: 400px;
}

.setia-notification.show {
    transform: translateX(0);
}

.setia-notification-success {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.setia-notification-error {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
}

.setia-notification-info {
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
}

/* Modern Generation Progress Modal */
.setia-generation-progress {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 10000;
    display: flex;
    align-items: center;
    justify-content: center;
    animation: modalFadeIn 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(12px);
    background: rgba(0, 0, 0, 0.6);
}

.progress-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
    backdrop-filter: blur(20px);
}

.progress-content {
    background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
    border-radius: 24px;
    max-width: 520px;
    width: 90%;
    max-height: 85vh;
    overflow: hidden;
    box-shadow:
        0 32px 64px rgba(0, 0, 0, 0.2),
        0 0 0 1px rgba(255, 255, 255, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.8);
    position: relative;
    z-index: 1;
    animation: modalSlideIn 0.6s cubic-bezier(0.34, 1.56, 0.64, 1);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

@keyframes modalFadeIn {
    from {
        opacity: 0;
        backdrop-filter: blur(0px);
    }
    to {
        opacity: 1;
        backdrop-filter: blur(12px);
    }
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(40px) scale(0.9);
        filter: blur(4px);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
        filter: blur(0px);
    }
}

/* Modern Progress Header */
.progress-header {
    text-align: center;
    padding: 40px 32px 32px 32px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
    color: white;
    border-radius: 24px 24px 0 0;
    position: relative;
    overflow: hidden;
}

.progress-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%, rgba(255, 255, 255, 0.1) 100%);
    animation: headerShimmer 3s infinite;
}

@keyframes headerShimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

/* Modern Progress Icon */
.progress-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 80px;
    height: 80px;
    margin: 0 auto 24px auto;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.1) 100%);
    border-radius: 50%;
    border: 2px solid rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(10px);
    position: relative;
    z-index: 1;
    animation: iconPulse 2s ease-in-out infinite;
}

.progress-icon svg {
    color: white;
    animation: iconRotate 3s linear infinite;
    filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.2));
}

@keyframes iconPulse {
    0%, 100% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.4);
    }
    50% {
        transform: scale(1.05);
        box-shadow: 0 0 0 10px rgba(255, 255, 255, 0);
    }
}

@keyframes iconRotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.progress-header h3 {
    margin: 0 0 12px 0;
    font-size: 26px;
    font-weight: 800;
    position: relative;
    z-index: 1;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    letter-spacing: -0.5px;
}

.progress-header p {
    margin: 0;
    opacity: 0.95;
    font-size: 16px;
    position: relative;
    z-index: 1;
    line-height: 1.5;
    font-weight: 400;
}

/* Progress Bar */
.progress-bar-container {
    padding: 24px 32px;
    background: #f8fafc;
    border-bottom: 1px solid #e2e8f0;
}

.progress-bar {
    width: 100%;
    height: 12px;
    background: #e5e7eb;
    border-radius: 6px;
    overflow: hidden;
    margin-bottom: 12px;
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #667eea 0%, #764ba2 50%, #10b981 100%);
    border-radius: 6px;
    transition: width 0.6s ease;
    position: relative;
    overflow: hidden;
}

.progress-fill::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent 0%, rgba(255,255,255,0.3) 50%, transparent 100%);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.progress-percentage {
    text-align: center;
    font-weight: 700;
    font-size: 18px;
    color: #667eea;
}

/* Modern Progress Steps */
.progress-steps {
    padding: 32px;
    background: linear-gradient(145deg, #f8fafc 0%, #ffffff 100%);
}

.progress-steps-container {
    display: flex;
    flex-direction: column;
    gap: 20px;
    max-height: 350px;
    overflow-y: auto;
    padding-right: 8px;
}

.progress-steps-container::-webkit-scrollbar {
    width: 6px;
}

.progress-steps-container::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 3px;
}

.progress-steps-container::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 3px;
}

.progress-step-item {
    display: flex;
    align-items: center;
    gap: 20px;
    padding: 20px 24px;
    background: white;
    border-radius: 16px;
    border: 2px solid #f1f5f9;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    opacity: 0.6;
    transform: translateX(-10px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    position: relative;
    overflow: hidden;
}

.progress-step-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: #e2e8f0;
    transition: all 0.4s ease;
}

.progress-step-item.active {
    opacity: 1;
    transform: translateX(0);
    border-color: rgba(102, 126, 234, 0.2);
    background: linear-gradient(135deg, #f8faff 0%, #f0f4ff 100%);
    box-shadow: 0 8px 32px rgba(102, 126, 234, 0.15);
}

.progress-step-item.active::before {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    width: 6px;
}

.progress-step-item.completed {
    opacity: 0.9;
    transform: translateX(0);
    border-color: rgba(16, 185, 129, 0.2);
    background: linear-gradient(135deg, #f0fdf4 0%, #ecfdf5 100%);
}

.progress-step-item.completed::before {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    width: 6px;
}

.step-icon {
    font-size: 24px;
    width: 56px;
    height: 56px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border-radius: 50%;
    border: 3px solid #e2e8f0;
    transition: all 0.4s ease;
    flex-shrink: 0;
    position: relative;
}

.progress-step-item.active .step-icon {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-color: #667eea;
    color: white;
    transform: scale(1.1);
    box-shadow: 0 8px 24px rgba(102, 126, 234, 0.3);
}

.progress-step-item.completed .step-icon {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    border-color: #10b981;
    color: white;
    animation: completedBounce 0.6s ease;
}

@keyframes completedBounce {
    0% { transform: scale(1); }
    50% { transform: scale(1.2); }
    100% { transform: scale(1); }
}

.step-text {
    font-size: 16px;
    font-weight: 600;
    color: #64748b;
    transition: all 0.3s ease;
}

.progress-step-item.active .step-text {
    color: #667eea;
    font-weight: 700;
}

.progress-step-item.completed .step-text {
    color: #10b981;
    font-weight: 600;
}

/* Progress Footer */
.progress-footer {
    padding: 24px 32px;
    background: linear-gradient(145deg, #f8fafc 0%, #ffffff 100%);
    border-top: 1px solid #e2e8f0;
    text-align: center;
}

.cancel-generation-btn {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 12px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
}

.cancel-generation-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(239, 68, 68, 0.4);
}

.cancel-generation-btn:active {
    transform: translateY(0);
}
    color: white;
}

.progress-step-item.completed .step-circle {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    border-color: #10b981;
    color: white;
}

.step-circle.pulsing {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { box-shadow: 0 0 0 0 rgba(102, 126, 234, 0.7); }
    70% { box-shadow: 0 0 0 10px rgba(102, 126, 234, 0); }
    100% { box-shadow: 0 0 0 0 rgba(102, 126, 234, 0); }
}

.step-icon {
    font-size: 20px;
    z-index: 2;
    position: relative;
}

.step-loader {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 24px;
    height: 24px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    opacity: 0;
    z-index: 1;
}

.progress-step-item.active .step-loader {
    opacity: 1;
}

.step-content {
    flex: 1;
    text-align: right;
}

.step-title {
    margin: 0 0 4px 0;
    font-size: 16px;
    font-weight: 600;
    color: #1e293b;
}

.step-description {
    margin: 0 0 8px 0;
    font-size: 14px;
    color: #64748b;
    line-height: 1.4;
}

.step-status {
    font-size: 12px;
    font-weight: 600;
    padding: 4px 8px;
    border-radius: 12px;
    display: inline-block;
    background: #f1f5f9;
    color: #64748b;
}

.progress-step-item.active .step-status {
    background: #dbeafe;
    color: #1d4ed8;
}

.progress-step-item.completed .step-status {
    background: #dcfce7;
    color: #166534;
}

/* Progress Footer */
.progress-footer {
    padding: 24px 32px;
    background: #f8fafc;
    border-radius: 0 0 20px 20px;
    border-top: 1px solid #e2e8f0;
}

.progress-tips {
    margin-bottom: 20px;
}

.tip-item {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
    font-size: 14px;
    color: #64748b;
}

.tip-icon {
    font-size: 16px;
}

.progress-actions {
    text-align: center;
}

.cancel-generation-btn {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 6px;
}

.cancel-generation-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
}

.btn-icon {
    font-size: 14px;
}

/* Enhanced Scrollbar for Progress Modal */
.progress-steps-container::-webkit-scrollbar {
    width: 6px;
}

.progress-steps-container::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 3px;
}

.progress-steps-container::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 3px;
}

.progress-steps-container::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
}

/* Mobile Responsive Design */
@media (max-width: 768px) {
    /* Main Progress Indicator */
    .setia-progress-indicator {
        gap: 20px;
        padding: 0 10px;
        margin: 30px 0;
    }

    .progress-step {
        max-width: 120px;
    }

    .step-number {
        width: 48px;
        height: 48px;
        font-size: 16px;
    }

    .step-title {
        font-size: 13px;
        min-height: 32px;
    }

    .progress-step:not(:last-child)::before {
        right: -60%;
        width: 120%;
    }

    /* Progress Modal */
    .progress-content {
        width: 95%;
        margin: 20px auto;
        max-height: 90vh;
        border-radius: 20px;
    }

    .progress-header {
        padding: 32px 24px 24px 24px;
    }

    .progress-icon {
        width: 64px;
        height: 64px;
        margin-bottom: 20px;
    }

    .progress-header h3 {
        font-size: 22px;
    }

    .progress-header p {
        font-size: 14px;
    }

    .progress-bar-container {
        padding: 20px 24px;
    }

    .progress-steps {
        padding: 24px;
    }

    .progress-step-item {
        padding: 16px 20px;
        gap: 16px;
    }

    .step-icon {
        width: 48px;
        height: 48px;
        font-size: 20px;
    }

    .step-text {
        font-size: 15px;
    }

    .progress-footer {
        padding: 20px 24px;
    }

    .cancel-generation-btn {
        padding: 10px 20px;
        font-size: 13px;
    }
}

@media (max-width: 480px) {
    /* Extra small screens */
    .setia-progress-indicator {
        flex-direction: column;
        gap: 16px;
        align-items: center;
    }

    .progress-step {
        max-width: none;
        width: 100%;
        text-align: center;
    }

    .progress-step:not(:last-child)::before {
        display: none;
    }

    .progress-content {
        width: 98%;
        margin: 10px auto;
    }

    .progress-header {
        padding: 24px 20px 20px 20px;
    }

    .progress-icon {
        width: 56px;
        height: 56px;
    }

    .progress-header h3 {
        font-size: 20px;
    }

    .progress-steps {
        padding: 20px;
    }

    .progress-step-item {
        padding: 14px 16px;
        gap: 12px;
    }

    .step-icon {
        width: 40px;
        height: 40px;
        font-size: 18px;
    }

    .step-text {
        font-size: 14px;
    }
}

/* Enhanced Animations and Effects */
@keyframes progressGlow {
    0%, 100% {
        box-shadow: 0 0 5px rgba(102, 126, 234, 0.3);
    }
    50% {
        box-shadow: 0 0 20px rgba(102, 126, 234, 0.6);
    }
}

.progress-step.active .step-number {
    animation: progressGlow 2s ease-in-out infinite;
}

.progress-step.pulse-animation .step-number {
    animation: stepPulse 0.6s ease-out;
}

@keyframes stepPulse {
    0% { transform: scale(1.1); }
    50% { transform: scale(1.3); }
    100% { transform: scale(1.1); }
}

/* Accessibility Improvements */
.progress-step:focus,
.progress-step-item:focus,
.cancel-generation-btn:focus {
    outline: 3px solid rgba(102, 126, 234, 0.5);
    outline-offset: 2px;
}

.progress-content {
    outline: none;
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
    .progress-step,
    .step-number,
    .progress-step-item,
    .step-icon,
    .progress-icon svg,
    .progress-fill::before {
        animation: none !important;
        transition: none !important;
    }

    .progress-content {
        animation: none !important;
    }

    .setia-generation-progress {
        animation: none !important;
    }
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
    .progress-step.active .step-number {
        background: #000000;
        color: #ffffff;
        border-color: #000000;
    }

    .progress-step-item.active {
        border-color: #000000;
        background: #ffffff;
    }

    .step-icon {
        border-color: #000000;
    }
}

/* Keyframe Animations */
@keyframes spin {
    from {
        transform: translate(-50%, -50%) rotate(0deg);
    }
    to {
        transform: translate(-50%, -50%) rotate(360deg);
    }
}

/* Print Styles */
@media print {
    .setia-generation-progress,
    .progress-overlay {
        display: none !important;
    }

    .setia-progress-indicator {
        background: none !important;
        color: #000000 !important;
    }
}

/* Additional Animations */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* Success Animation for Completed Steps */
.progress-step-item.completed .step-circle {
    animation: successPulse 0.6s ease;
}

@keyframes successPulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

/* Notification Enhancement for Progress */
.setia-notification.progress-notification {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    border-left: 4px solid #1e40af;
}

/* Loading State for Main Button */
.setia-button.generating {
    background: linear-gradient(135deg, #94a3b8 0%, #64748b 100%);
    cursor: not-allowed;
    transform: none !important;
}

.setia-button.generating:hover {
    transform: none !important;
    box-shadow: 0 4px 12px rgba(148, 163, 184, 0.3) !important;
}

/* Progress Bar Glow Effect */
.progress-fill {
    box-shadow: 0 0 10px rgba(102, 126, 234, 0.5);
}

/* Step Connection Lines */
.progress-step-item:not(:last-child)::after {
    content: '';
    position: absolute;
    left: 24px;
    top: 64px;
    width: 2px;
    height: 32px;
    background: #e2e8f0;
    z-index: 0;
}

.progress-step-item.completed:not(:last-child)::after {
    background: linear-gradient(to bottom, #10b981, #e2e8f0);
}

.progress-steps-container {
    position: relative;
}

/* Enhanced Hover Effects */
.progress-step-item:hover {
    background: rgba(102, 126, 234, 0.05);
    margin: 0 -16px;
    padding: 16px;
    border-radius: 12px;
    border-bottom: 1px solid transparent;
}

.progress-step-item.active:hover {
    background: linear-gradient(135deg, #e0f2fe 0%, #bae6fd 100%);
}

/* Accessibility Improvements */
.progress-content {
    outline: none;
}

.cancel-generation-btn:focus {
    outline: 2px solid #ef4444;
    outline-offset: 2px;
}

/* Print Styles for Progress Modal */
@media print {
    .setia-generation-progress {
        display: none !important;
    }
}

/* Enhanced Form Elements */
.setia-input.error,
.setia-textarea.error {
    border-color: #ef4444;
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.setia-input.success,
.setia-textarea.success {
    border-color: #10b981;
    box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
}

/* Improved Scrollbar */
.setia-content-preview::-webkit-scrollbar {
    width: 8px;
}

.setia-content-preview::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 4px;
}

.setia-content-preview::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 4px;
}

.setia-content-preview::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
}

/* Enhanced Hover Effects */
.setia-form-group:hover .setia-input,
.setia-form-group:hover .setia-select,
.setia-form-group:hover .setia-textarea {
    border-color: #cbd5e1;
}

.setia-toggle-item:hover .toggle-title {
    color: #667eea;
}

/* Loading Animation for Content Preview */
.setia-content-preview.loading {
    position: relative;
    min-height: 200px;
}

.setia-content-preview.loading::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 40px;
    height: 40px;
    border: 4px solid #f3f4f6;
    border-top: 4px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* Skeleton Loading for Results */
.skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

.skeleton-text {
    height: 16px;
    border-radius: 4px;
    margin-bottom: 8px;
}

.skeleton-title {
    height: 24px;
    border-radius: 4px;
    margin-bottom: 16px;
    width: 70%;
}

/* Enhanced Focus States */
.setia-input:focus,
.setia-select:focus,
.setia-textarea:focus {
    transform: translateY(-1px);
}

.setia-button:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.3);
}

/* Print Styles */
@media print {
    .setia-main-header,
    .setia-form-container,
    .setia-result-actions,
    .setia-result-tabs {
        display: none !important;
    }

    .setia-result-container {
        box-shadow: none !important;
        border: none !important;
    }

    .setia-content-preview {
        max-height: none !important;
        overflow: visible !important;
        box-shadow: none !important;
        border: 1px solid #ccc !important;
    }
}

/* Product Preview Enhanced Styles */
.setia-product-preview {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(248, 249, 250, 0.9));
    border: 1px solid rgba(102, 126, 234, 0.2);
    border-radius: 12px;
    padding: 20px;
    margin-top: 15px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.setia-product-preview h3 {
    color: #333;
    margin-bottom: 15px;
    font-size: 18px;
    font-weight: 600;
}

.setia-product-preview .price {
    color: #4CAF50;
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 10px;
}

.setia-product-preview .sku {
    color: #FF9800;
    font-size: 14px;
    margin-bottom: 15px;
}

.setia-product-preview .description {
    background: rgba(248, 249, 250, 0.8);
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 20px;
}

.setia-product-preview .description h4 {
    color: #333;
    margin-bottom: 10px;
    font-size: 14px;
}

.setia-product-preview .description p {
    color: #666;
    line-height: 1.6;
    margin: 0;
}

.setia-product-preview .actions {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.setia-product-preview .actions .button {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 6px;
    text-decoration: none;
    font-size: 12px;
    transition: all 0.3s ease;
}

.setia-product-preview .actions .button:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

/* Loading States */
.setia-product-loading {
    text-align: center;
    padding: 40px 20px;
    color: #666;
}

.setia-product-loading .spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(102, 126, 234, 0.3);
    border-top: 4px solid #667eea;
    border-radius: 50%;
    animation: setia-spin 1s linear infinite;
    margin: 0 auto 20px;
}

@keyframes setia-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Error States */
.setia-product-error {
    background: linear-gradient(135deg, rgba(244, 67, 54, 0.1), rgba(244, 67, 54, 0.05));
    border: 1px solid rgba(244, 67, 54, 0.3);
    border-radius: 12px;
    padding: 20px;
    margin-top: 15px;
    color: #d32f2f;
}

/* Success States */
.setia-product-success {
    background: linear-gradient(135deg, rgba(76, 175, 80, 0.1), rgba(76, 175, 80, 0.05));
    border: 1px solid rgba(76, 175, 80, 0.3);
    border-radius: 12px;
    padding: 20px;
    margin-top: 15px;
    color: #388e3c;
}
