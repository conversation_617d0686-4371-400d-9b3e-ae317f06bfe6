<!DOCTYPE html>
<html dir="rtl" lang="fa">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تست رفع مشکل Help Toggle</title>
    <link rel="stylesheet" href="assets/css/admin-settings.css">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <style>
        body {
            font-family: 'Tahoma', sans-serif;
            background: #f0f0f1;
            margin: 0;
            padding: 20px;
        }
        .test-container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .test-header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #667eea;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
        }
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-weight: bold;
        }
        .status.success { background: #d1fae5; color: #065f46; }
        .status.error { background: #fee2e2; color: #991b1b; }
        .status.info { background: #dbeafe; color: #1e40af; }
    </style>
</head>
<body class="wp-admin">
    <div class="wrap setia-settings">
        <div class="test-container">
            <div class="test-header">
                <h1>🔧 تست رفع مشکل Help Toggle</h1>
                <p>این صفحه برای تست عملکرد صحیح دکمه‌های راهنما استفاده می‌شود</p>
            </div>

            <div class="test-section">
                <h3>📋 تست ۱: Help Toggle اصلی</h3>
                <div class="setia-form-group">
                    <label class="setia-label">
                        <span class="label-icon">🔑</span>
                        کلید API تست
                    </label>
                    <div class="setia-input-wrapper">
                        <input type="text" class="setia-input" placeholder="کلید API خود را وارد کنید">
                    </div>
                    <div class="setia-help-content">
                        <button type="button" class="setia-help-toggle" id="test-toggle-1">راهنمای دریافت کلید</button>
                        <div class="setia-help-steps">
                            <ol>
                                <li>مرحله اول: ثبت‌نام در سایت</li>
                                <li>مرحله دوم: تأیید ایمیل</li>
                                <li>مرحله سوم: دریافت کلید API</li>
                            </ol>
                            <p class="help-note">این راهنما برای تست عملکرد toggle است</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="test-section">
                <h3>📋 تست ۲: Help Toggle دوم</h3>
                <div class="setia-form-group">
                    <label class="setia-label">
                        <span class="label-icon">🎨</span>
                        کلید API تصویر
                    </label>
                    <div class="setia-input-wrapper">
                        <input type="text" class="setia-input" placeholder="کلید API تصویر">
                    </div>
                    <div class="setia-help-content">
                        <button type="button" class="setia-help-toggle" id="test-toggle-2">راهنمای دریافت کلید</button>
                        <div class="setia-help-steps">
                            <ol>
                                <li>وارد داشبورد شوید</li>
                                <li>روی تولید کلید کلیک کنید</li>
                                <li>کلید را کپی کنید</li>
                            </ol>
                            <p class="help-note">این راهنما برای تست toggle دوم است</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="test-section">
                <h3>📊 نتایج تست</h3>
                <div id="test-results">
                    <div class="status info">در حال آماده‌سازی تست...</div>
                </div>
            </div>

            <div class="test-section">
                <h3>🎯 راهنمای تست</h3>
                <ul>
                    <li>روی هر دکمه "راهنمای دریافت کلید" کلیک کنید</li>
                    <li>بررسی کنید که راهنما به آرامی باز می‌شود</li>
                    <li>مجدداً کلیک کنید تا راهنما بسته شود</li>
                    <li>تست کنید که کلیک‌های سریع مشکل ایجاد نمی‌کند</li>
                    <li>بررسی کنید که فقط یک راهنما در هر زمان باز است</li>
                </ul>
            </div>
        </div>
    </div>

    <script src="assets/js/settings-enhanced.js"></script>
    <script>
        $(document).ready(function() {
            let testResults = [];
            let clickCount = 0;
            
            // تست عملکرد toggle
            function runToggleTests() {
                const $results = $('#test-results');
                $results.html('<div class="status info">شروع تست‌ها...</div>');
                
                // تست ۱: بررسی وجود event handler
                setTimeout(() => {
                    const hasHandler = $('.setia-help-toggle').length > 0;
                    if (hasHandler) {
                        testResults.push('✅ دکمه‌های toggle یافت شدند');
                    } else {
                        testResults.push('❌ دکمه‌های toggle یافت نشدند');
                    }
                    updateResults();
                }, 500);
                
                // تست ۲: بررسی ARIA attributes
                setTimeout(() => {
                    const hasAria = $('.setia-help-toggle[aria-expanded]').length > 0;
                    if (hasAria) {
                        testResults.push('✅ ویژگی‌های ARIA تنظیم شده‌اند');
                    } else {
                        testResults.push('❌ ویژگی‌های ARIA تنظیم نشده‌اند');
                    }
                    updateResults();
                }, 1000);
                
                // تست ۳: شمارش کلیک‌ها
                $('.setia-help-toggle').on('click.test', function() {
                    clickCount++;
                    testResults.push(`🖱️ کلیک ${clickCount}: ${$(this).attr('id') || 'نامشخص'}`);
                    updateResults();
                });
                
                setTimeout(() => {
                    testResults.push('🎉 تست‌ها تکمیل شد - حالا دکمه‌ها را امتحان کنید');
                    updateResults();
                }, 1500);
            }
            
            function updateResults() {
                const $results = $('#test-results');
                let html = '';
                testResults.forEach(result => {
                    if (result.includes('✅')) {
                        html += `<div class="status success">${result}</div>`;
                    } else if (result.includes('❌')) {
                        html += `<div class="status error">${result}</div>`;
                    } else {
                        html += `<div class="status info">${result}</div>`;
                    }
                });
                $results.html(html);
            }
            
            // شروع تست‌ها
            runToggleTests();
            
            // تست کیبورد
            $(document).on('keydown', '.setia-help-toggle', function(e) {
                if (e.key === 'Enter' || e.key === ' ') {
                    testResults.push(`⌨️ کلید ${e.key} فشرده شد`);
                    updateResults();
                }
            });
        });
    </script>
</body>
</html>
