/**
 * استایل‌های بهبود یافته برای بخش تنظیمات تصویر
 * در فرم زمانبندی تولید محتوا
 */

/* کانتینر اصلی تنظیمات تصویر */
#setia-image-options-container {
    background-color: #f0f4ff;
    border-radius: 12px;
    padding: 25px;
    margin: 20px 0 30px;
    border: 1px solid rgba(25, 103, 210, 0.15);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

#setia-image-options-container:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 5px;
    background: linear-gradient(90deg, #0d47a1, #1976d2, #2196f3);
    border-radius: 5px 5px 0 0;
}

#setia-image-options-container:hover {
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08);
    transform: translateY(-2px);
}

#setia-image-options-container h3 {
    margin-top: 0;
    margin-bottom: 20px;
    font-size: 16px;
    font-weight: 600;
    color: #0d47a1;
    display: flex;
    align-items: center;
}

#setia-image-options-container h3:before {
    content: "\f128";
    font-family: dashicons;
    margin-left: 10px;
    font-size: 20px;
    color: #1976d2;
}

/* استایل فیلدهای تنظیمات تصویر */
#setia-image-options-container .setia-form-row {
    margin-bottom: 20px;
    padding-bottom: 20px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

#setia-image-options-container .setia-form-row:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}

#setia-image-options-container label {
    font-weight: 600;
    display: block;
    margin-bottom: 12px;
    color: #333;
}

/* بهبود نسبت ابعاد */
.setia-aspect-ratio-options {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
    margin-top: 15px;
    margin-bottom: 10px;
    direction: ltr;
}

.setia-aspect-ratio-option {
    position: relative;
    width: calc(20% - 10px);
    background-color: #fff;
    border: 2px solid #ddd;
    border-radius: 8px;
    transition: all 0.3s ease;
    cursor: pointer;
    overflow: hidden;
    padding: 8px;
    text-align: center;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.setia-aspect-ratio-option:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
    border-color: #1976d2;
}

.setia-aspect-ratio-option.selected {
    border-color: #1976d2;
    background-color: rgba(25, 103, 210, 0.05);
    box-shadow: 0 5px 15px rgba(25, 103, 210, 0.15);
}

.setia-aspect-ratio-option input[type="radio"] {
    position: absolute;
    opacity: 0;
}

.aspect-preview {
    height: 60px;
    background-color: #e3f2fd;
    border-radius: 6px;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    border: 1px dashed rgba(25, 103, 210, 0.3);
    position: relative;
}

/* افزودن نمایش بهتر پیش‌نمایش نسبت‌ها */
.aspect-preview:after {
    content: '';
    position: absolute;
    background-color: rgba(25, 103, 210, 0.2);
    border-radius: 3px;
    width: 60%;
    height: 60%;
    z-index: 1;
}

.setia-aspect-ratio-option.selected .aspect-preview:after {
    background-color: rgba(25, 103, 210, 0.3);
}

.aspect-label {
    font-size: 12px;
    font-weight: 600;
    color: #333;
    direction: rtl;
}

.setia-aspect-ratio-option.selected .aspect-label {
    color: #1976d2;
}

/* بهبود سلکت باکس‌ها در تنظیمات تصویر */
#setia-image-options-container select {
    width: 100%;
    padding: 12px 15px;
    border: 1px solid #ddd;
    border-radius: 8px;
    font-size: 14px;
    transition: all 0.3s ease;
    background-color: #fff;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.03);
    appearance: none;
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="12" height="6" viewBox="0 0 12 6"><path fill="%231976d2" d="M0 0h12L6 6z"/></svg>');
    background-repeat: no-repeat;
    background-position: left 15px center;
    padding-left: 35px;
}

#setia-image-options-container select:focus {
    border-color: #1976d2;
    box-shadow: 0 0 0 3px rgba(25, 103, 210, 0.15);
    outline: none;
}

/* حالت‌های موبایل */
@media (max-width: 991px) {
    .setia-aspect-ratio-option {
        width: calc(25% - 10px);
    }
}

@media (max-width: 767px) {
    .setia-aspect-ratio-option {
        width: calc(33.33% - 10px);
    }
}

@media (max-width: 480px) {
    .setia-aspect-ratio-option {
        width: calc(50% - 6px);
    }
}

/* انیمیشن برای نمایش و مخفی کردن */
#setia-image-options-container.opening {
    animation: slideDown 0.3s ease-out forwards;
}

#setia-image-options-container.closing {
    animation: slideUp 0.3s ease-out forwards;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideUp {
    from {
        opacity: 1;
        transform: translateY(0);
    }
    to {
        opacity: 0;
        transform: translateY(-20px);
    }
}

/* افزودن نمایش محتوا در پیش‌نمایش تصاویر */
.aspect-preview:before {
    content: '';
    position: absolute;
    background: linear-gradient(45deg, rgba(25, 118, 210, 0.1), rgba(25, 118, 210, 0.2));
    width: 80%;
    height: 80%;
    border-radius: 4px;
    z-index: 1;
    opacity: 0.8;
    transition: all 0.3s ease;
}

.setia-aspect-ratio-option.selected .aspect-preview:before {
    background: linear-gradient(45deg, rgba(25, 118, 210, 0.2), rgba(25, 118, 210, 0.4));
    box-shadow: 0 0 0 2px rgba(25, 118, 210, 0.3);
}

/* اضافه کردن آیکون به پیش‌نمایش */
.aspect-preview:after {
    content: '\f128';
    font-family: dashicons;
    position: absolute;
    font-size: 20px;
    color: rgba(25, 118, 210, 0.5);
    z-index: 2;
}

.setia-aspect-ratio-option[data-ratio="1:1"] .aspect-preview:after {
    content: '\f128'; /* آیکون تصویر */
}

.setia-aspect-ratio-option[data-ratio="16:9"] .aspect-preview:after {
    content: '\f232'; /* آیکون فیلم */
}

.setia-aspect-ratio-option[data-ratio="9:16"] .aspect-preview:after {
    content: '\f235'; /* آیکون موبایل */
}

.setia-aspect-ratio-option[data-ratio="4:3"] .aspect-preview:after {
    content: '\f479'; /* آیکون دسکتاپ */
}

.setia-aspect-ratio-option[data-ratio="3:4"] .aspect-preview:after {
    content: '\f481'; /* آیکون تبلت */
}

/* بهبود انیمیشن انتخاب */
.setia-aspect-ratio-option.selected {
    animation: selectPulse 0.5s ease-out;
}

@keyframes selectPulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

/* استایل سلکت باکس ارتقا یافته */
.setia-enhanced-select {
    position: relative;
    transition: all 0.3s ease;
}

.setia-enhanced-select:hover {
    border-color: #1976d2;
}

.pulse-animation {
    animation: pulse 0.5s ease-out;
} 