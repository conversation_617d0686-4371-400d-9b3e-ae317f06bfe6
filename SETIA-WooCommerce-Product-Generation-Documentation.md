# 📦 مستندات ویژگی تولید محصول WooCommerce در افزونه SETIA

## 🎯 معرفی کلی

ویژگی تولید محصول WooCommerce یکی از قابلیت‌های پیشرفته افزونه SETIA است که به کاربران امکان تولید خودکار محصولات WooCommerce با استفاده از هوش مصنوعی را می‌دهد.

## ✨ ویژگی‌های کلیدی

### 🔧 قابلیت‌های اصلی
- **تولید خودکار توضیحات محصول** با استفاده از Gemini AI
- **تولید تصاویر محصول** با استفاده از Imagine Art API
- **تنظیم خودکار قیمت** بر اساس نوع محصول
- **تولید SKU یکتا** برای هر محصول
- **دسته‌بندی خودکار** محصولات
- **تولید برچسب‌های مرتبط** 
- **ایجاد Schema Markup** برای بهینه‌سازی SEO
- **پیش‌نمایش کامل** قبل از انتشار

### 🎨 رابط کاربری
- **سیستم تب‌های اصلی** برای جابجایی بین تولید محتوا و محصول
- **فرم پیشرفته** با تنظیمات کامل
- **نمایش نتایج تعاملی** با تب‌های جداگانه
- **پشتیبانی کامل از RTL** برای زبان فارسی
- **طراحی مدرن** با افکت‌های glassmorphism

## 🏗️ ساختار فنی

### 📁 فایل‌های اصلی

#### 1. **templates/main-page.php**
- **خطوط 27-53**: سیستم تب‌های اصلی
- **خطوط 400-654**: رابط کاربری تولید محصول
- **ویژگی‌ها**:
  - بررسی وجود WooCommerce
  - فرم تولید محصول
  - تنظیمات پیشرفته
  - نمایش نتایج

#### 2. **ajax-handlers.php**
- **خط 41**: ثبت AJAX action برای `setia_generate_product`
- **خطوط 1816-1947**: متد اصلی `generate_woocommerce_product()`
- **خطوط 1948-2314**: متدهای کمکی

#### 3. **assets/css/main-page-enhanced.css**
- **خطوط 1-58**: استایل تب‌های اصلی
- **خطوط 59-141**: استایل فرم محصول
- **خطوط 2161-2272**: استایل‌های پیش‌نمایش و حالت‌های مختلف

#### 4. **assets/js/main-page-enhanced.js**
- **خطوط 773-843**: مدیریت تب‌های اصلی
- **خطوط 844-953**: منطق تولید محصول

### 🔧 متدهای کلیدی

#### **generate_woocommerce_product()**
متد اصلی که فرآیند تولید محصول را مدیریت می‌کند:
```php
// بررسی امنیتی و اعتبارسنجی
// تولید توضیحات با AI
// تولید تصاویر
// ایجاد محصول WooCommerce
// تنظیم متادیتا
// بازگشت نتایج
```

#### **generate_product_descriptions($product_name)**
تولید توضیحات کامل و کوتاه محصول:
- استفاده از Gemini AI
- پرامپت‌های بهینه‌شده
- مدیریت خطا

#### **generate_product_images($product_name, $count)**
تولید تصاویر محصول:
- استفاده از Imagine Art API
- آپلود به کتابخانه رسانه WordPress
- تنظیم تصویر شاخص و گالری

#### **generate_product_price($product_name)**
تولید قیمت هوشمند:
- تشخیص نوع محصول
- محاسبه قیمت بر اساس دسته‌بندی
- قیمت‌گذاری منطقی

#### **generate_product_sku($product_name, $product_id)**
تولید SKU یکتا:
- استخراج کلمات کلیدی
- ترکیب با ID محصول
- بررسی یکتا بودن

#### **generate_product_tags($product_name)**
تولید برچسب‌های مرتبط:
- برچسب‌های عمومی
- برچسب‌های خاص بر اساس نوع محصول

#### **generate_product_category($product_name)**
دسته‌بندی خودکار:
- تشخیص نوع محصول
- ایجاد دسته‌بندی در صورت عدم وجود

#### **generate_product_schema_data($product_id, $product_name, $descriptions)**
تولید Schema Markup:
- ساختار JSON-LD
- اطلاعات محصول، قیمت، موجودی
- بهینه‌سازی SEO

## 🎮 نحوه استفاده

### 1. **دسترسی به ویژگی**
- ورود به پنل مدیریت WordPress
- رفتن به صفحه SETIA
- کلیک روی تب "تولید محصول"

### 2. **تنظیمات اولیه**
- اطمینان از نصب WooCommerce
- تنظیم کلیدهای API (Gemini و Imagine Art)
- بررسی مجوزهای کاربری

### 3. **تولید محصول**
- وارد کردن نام محصول
- انتخاب دسته‌بندی (اختیاری)
- تنظیم وضعیت انتشار
- انتخاب تعداد تصاویر
- فعال‌سازی گزینه‌های پیشرفته
- کلیک روی "تولید محصول"

### 4. **بررسی نتایج**
- مشاهده اطلاعات محصول
- بررسی تصاویر تولید شده
- مطالعه Schema Markup
- استفاده از دکمه‌های عملیات

## 🔒 ملاحظات امنیتی

### ✅ بررسی‌های امنیتی پیاده‌شده
- **Nonce Verification**: تأیید اعتبار درخواست‌ها
- **Capability Check**: بررسی مجوزهای کاربری
- **Data Sanitization**: پاک‌سازی داده‌های ورودی
- **CSRF Protection**: محافظت در برابر حملات CSRF

### 🛡️ محدودیت‌های دسترسی
- فقط کاربران با مجوز `manage_options`
- بررسی وجود WooCommerce
- اعتبارسنجی کلیدهای API

## 🚀 بهینه‌سازی عملکرد

### ⚡ تکنیک‌های بهینه‌سازی
- **AJAX Loading**: بارگذاری غیرهمزمان
- **Progress Tracking**: نمایش پیشرفت
- **Error Handling**: مدیریت خطاهای احتمالی
- **Caching**: ذخیره‌سازی نتایج

### 📊 مدیریت منابع
- محدودیت تعداد تصاویر
- تایم‌اوت مناسب برای API ها
- مدیریت حافظه

## 🔧 عیب‌یابی

### ❗ مشکلات رایج

#### **WooCommerce نصب نیست**
```
خطا: WooCommerce plugin is not active
راه‌حل: نصب و فعال‌سازی WooCommerce
```

#### **کلید API موجود نیست**
```
خطا: API key not configured
راه‌حل: تنظیم کلیدهای API در بخش تنظیمات
```

#### **خطا در تولید تصویر**
```
خطا: Image generation failed
راه‌حل: بررسی کلید Imagine Art API
```

### 🔍 لاگ‌های سیستم
- بررسی فایل `wp-content/debug.log`
- جستجوی کلمه کلیدی `SETIA Product`
- مطالعه پیام‌های خطا

## 📈 توسعه آینده

### 🎯 ویژگی‌های برنامه‌ریزی شده
- **تولید انبوه محصولات** از فایل CSV
- **قالب‌های محصول** قابل تنظیم
- **یکپارچگی با سایر افزونه‌ها**
- **گزارش‌گیری پیشرفته**
- **API عمومی** برای توسعه‌دهندگان

### 🔄 بهبودهای احتمالی
- **بهینه‌سازی سرعت** تولید
- **کیفیت بالاتر تصاویر**
- **پشتیبانی از زبان‌های بیشتر**
- **رابط کاربری بهتر**

## 📞 پشتیبانی

### 🆘 راه‌های دریافت کمک
- **مستندات آنلاین**: مراجعه به وب‌سایت رسمی
- **انجمن پشتیبانی**: ارسال سوال در انجمن
- **تیکت پشتیبانی**: ارسال درخواست مستقیم
- **ایمیل**: تماس با تیم فنی

---

**نسخه مستندات**: 1.0  
**تاریخ به‌روزرسانی**: 2025-01-07  
**سازگاری**: WordPress 5.0+ | WooCommerce 3.0+ | PHP 7.4+
