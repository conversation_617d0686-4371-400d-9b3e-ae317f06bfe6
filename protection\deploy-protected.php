<?php
/**
 * SETIA Protected Deployment Script
 * Creates production-ready protected version of the plugin
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    // Allow CLI execution for deployment
    if (php_sapi_name() !== 'cli') {
        exit('Direct access not allowed');
    }
}

require_once __DIR__ . '/protection-manager.php';

class SETIA_Deployment {
    
    private $plugin_dir;
    private $output_dir;
    private $protection_manager;
    
    public function __construct($plugin_dir = null, $output_dir = null) {
        $this->plugin_dir = $plugin_dir ?: dirname(__DIR__);
        $this->output_dir = $output_dir ?: $this->plugin_dir . '/dist';
        $this->protection_manager = new SETIA_Protection_Manager($this->plugin_dir);
    }
    
    /**
     * Create protected distribution
     */
    public function create_protected_distribution() {
        echo "Starting SETIA protected deployment...\n";
        
        // Step 1: Validate source
        if (!$this->validate_source()) {
            echo "ERROR: Source validation failed\n";
            return false;
        }
        echo "✓ Source validation passed\n";
        
        // Step 2: Create distribution package
        $package_result = $this->protection_manager->create_distribution_package($this->output_dir);
        
        if (!empty($package_result['protection_results']['errors'])) {
            echo "WARNING: Protection errors occurred:\n";
            foreach ($package_result['protection_results']['errors'] as $error) {
                echo "  - $error\n";
            }
        }
        
        echo "✓ Distribution package created\n";
        echo "  Distribution directory: " . $package_result['distribution_dir'] . "\n";
        echo "  ZIP package: " . $package_result['zip_file'] . "\n";
        
        // Step 3: Validate protected package
        if ($this->validate_protected_package($package_result['distribution_dir'])) {
            echo "✓ Protected package validation passed\n";
        } else {
            echo "WARNING: Protected package validation failed\n";
        }
        
        // Step 4: Generate deployment report
        $this->generate_deployment_report($package_result);
        
        echo "✓ Deployment completed successfully\n";
        return $package_result;
    }
    
    /**
     * Validate source files
     */
    private function validate_source() {
        $required_files = [
            'setia-content-generator.php',
            'ajax-handlers.php',
            'assets/js/main-page-enhanced.js',
            'assets/js/admin.js',
            'templates/main-page.php'
        ];
        
        foreach ($required_files as $file) {
            if (!file_exists($this->plugin_dir . '/' . $file)) {
                echo "ERROR: Required file missing: $file\n";
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * Validate protected package
     */
    private function validate_protected_package($dist_dir) {
        // Check if protection was applied
        $main_file = $dist_dir . '/setia-content-generator.php';
        if (!file_exists($main_file)) {
            return false;
        }
        
        $content = file_get_contents($main_file);
        
        // Check for obfuscation markers
        if (strpos($content, 'base64_decode') === false) {
            echo "WARNING: PHP obfuscation may not have been applied\n";
        }
        
        // Check for integrity checks
        if (strpos($content, 'SETIA_INTEGRITY_CHECK') === false) {
            echo "WARNING: Integrity checks may not have been added\n";
        }
        
        return true;
    }
    
    /**
     * Generate deployment report
     */
    private function generate_deployment_report($package_result) {
        $report = array(
            'deployment_date' => date('Y-m-d H:i:s'),
            'plugin_version' => '1.0.0',
            'protection_applied' => $package_result['protection_results'],
            'package_info' => array(
                'distribution_dir' => $package_result['distribution_dir'],
                'zip_file' => $package_result['zip_file'],
                'zip_size' => file_exists($package_result['zip_file']) ? filesize($package_result['zip_file']) : 0
            )
        );
        
        $report_file = $this->output_dir . '/deployment-report.json';
        file_put_contents($report_file, json_encode($report, JSON_PRETTY_PRINT));
        
        echo "✓ Deployment report saved: $report_file\n";
    }
    
    /**
     * Create development version (unprotected)
     */
    public function create_development_version() {
        echo "Creating development version...\n";
        
        $dev_dir = $this->output_dir . '/setia-development';
        
        // Copy files without protection
        $this->copy_directory($this->plugin_dir, $dev_dir);
        
        // Remove protection directory
        $protection_dir = $dev_dir . '/protection';
        if (is_dir($protection_dir)) {
            $this->remove_directory($protection_dir);
        }
        
        echo "✓ Development version created: $dev_dir\n";
    }
    
    /**
     * Copy directory recursively
     */
    private function copy_directory($src, $dst) {
        if (!is_dir($src)) {
            return false;
        }
        
        if (!is_dir($dst)) {
            mkdir($dst, 0755, true);
        }
        
        $iterator = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator($src, RecursiveDirectoryIterator::SKIP_DOTS),
            RecursiveIteratorIterator::SELF_FIRST
        );
        
        foreach ($iterator as $item) {
            $target = $dst . DIRECTORY_SEPARATOR . $iterator->getSubPathName();
            
            if ($item->isDir()) {
                mkdir($target, 0755, true);
            } else {
                copy($item, $target);
            }
        }
        
        return true;
    }
    
    /**
     * Remove directory recursively
     */
    private function remove_directory($dir) {
        if (!is_dir($dir)) {
            return false;
        }
        
        $files = array_diff(scandir($dir), array('.', '..'));
        
        foreach ($files as $file) {
            $path = $dir . DIRECTORY_SEPARATOR . $file;
            if (is_dir($path)) {
                $this->remove_directory($path);
            } else {
                unlink($path);
            }
        }
        
        return rmdir($dir);
    }
}

// CLI execution
if (php_sapi_name() === 'cli') {
    $plugin_dir = isset($argv[1]) ? $argv[1] : dirname(__DIR__);
    $output_dir = isset($argv[2]) ? $argv[2] : $plugin_dir . '/dist';
    
    $deployment = new SETIA_Deployment($plugin_dir, $output_dir);
    
    if (isset($argv[3]) && $argv[3] === '--dev-only') {
        $deployment->create_development_version();
    } else {
        $deployment->create_protected_distribution();
    }
}

// WordPress admin execution
if (defined('ABSPATH') && is_admin()) {
    add_action('wp_ajax_setia_create_protected_distribution', function() {
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
            return;
        }
        
        if (!wp_verify_nonce($_POST['nonce'], 'setia_deployment_nonce')) {
            wp_send_json_error('Security check failed');
            return;
        }
        
        $deployment = new SETIA_Deployment();
        $result = $deployment->create_protected_distribution();
        
        if ($result) {
            wp_send_json_success(array(
                'message' => 'Protected distribution created successfully',
                'package_info' => $result['package_info']
            ));
        } else {
            wp_send_json_error('Failed to create protected distribution');
        }
    });
}
