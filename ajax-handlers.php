<?php
// امنیت: جلوگیری از دسترسی مستقیم
if (!defined('ABSPATH')) {
    exit;
}

/**
 * کلاس مدیریت درخواست‌های AJAX
 */
class SETIA_Ajax_Handlers {
    
    // نمونه کلاس اصلی
    private $content_generator;
    
    // راه‌اندازی
    public function __construct($content_generator) {
        $this->content_generator = $content_generator;
        
        // ثبت اکشن‌های AJAX
        add_action('wp_ajax_setia_generate_content', array($this, 'generate_content'));
        add_action('wp_ajax_setia_publish_content', array($this, 'publish_content'));
        add_action('wp_ajax_setia_test_connection', array($this, 'test_connection'));
        add_action('wp_ajax_setia_get_content_details', array($this, 'get_content_details'));
        add_action('wp_ajax_setia_test_form_data', array($this, 'handle_test_form_data'));
        add_action('wp_ajax_setia_publish_from_history', array($this, 'publish_from_history'));
        add_action('wp_ajax_setia_delete_content', array($this, 'delete_content'));
        add_action('wp_ajax_setia_bulk_delete_content', array($this, 'bulk_delete_content'));
        add_action('wp_ajax_setia_generate_test_image', array($this, 'generate_test_image'));
        

        
        // اکشن‌های AJAX برای قابلیت‌های جدید
        add_action('wp_ajax_setia_generate_serp_preview', array($this, 'generate_serp_preview'));
        add_action('wp_ajax_setia_optimize_image', array($this, 'optimize_image'));
        // اکشن زمانبندی حذف شده است
        add_action('wp_ajax_setia_rewrite_content', array($this, 'rewrite_content'));
        add_action('wp_ajax_setia_analyze_keyword', array($this, 'analyze_keyword'));
        add_action('wp_ajax_setia_run_schedule_manually', array($this, 'run_schedule_manually'));
        
        // بخش زمانبندی حذف شده است
    }
    
    /**
     * تولید محتوا با Gemini
     */
    public function generate_content() {
        // شروع پردازش درخواست تولید محتوا
        // error_log('SETIA DEBUG: generate_content method called');

        try {
            // بررسی امنیتی
            if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'setia-nonce')) {
                wp_send_json_error(array('message' => 'خطای امنیتی: توکن نامعتبر'));
                return;
            }

            // بررسی دسترسی کاربر
            if (!current_user_can('edit_posts')) {
                wp_send_json_error(array('message' => 'خطای امنیتی: دسترسی غیرمجاز'));
                return;
            }

            // لاگ تمام داده‌های POST برای عیب‌یابی (در صورت نیاز فعال کنید)
            // error_log('SETIA DEBUG: All POST data: ' . print_r($_POST, true));

            // دریافت و پاکسازی داده‌های فرم
            // پشتیبانی از دو فرمت: form_data (serialized) یا فیلدهای جداگانه
            if (isset($_POST['form_data'])) {
                // فرمت قدیمی: داده‌های سریالایز شده
                $raw_form_data = wp_unslash($_POST['form_data']);
                parse_str($raw_form_data, $form_data);
                $form_data = array_map('sanitize_text_field', $form_data);
            } else {
                // فرمت جدید: فیلدهای جداگانه
                $form_data = array(
                    'topic' => isset($_POST['topic']) ? sanitize_text_field($_POST['topic']) : '',
                    'keywords' => isset($_POST['keywords']) ? sanitize_text_field($_POST['keywords']) : '',
                    'tone' => isset($_POST['tone']) ? sanitize_text_field($_POST['tone']) : 'professional',
                    'category' => isset($_POST['category']) ? sanitize_text_field($_POST['category']) : '',
                    'length' => isset($_POST['length']) ? sanitize_text_field($_POST['length']) : 'medium',
                    'seo' => isset($_POST['seo']) ? sanitize_text_field($_POST['seo']) : 'no',
                    'generate_image' => isset($_POST['generate_image']) ? sanitize_text_field($_POST['generate_image']) : 'no',
                    'image_style' => isset($_POST['image_style']) ? sanitize_text_field($_POST['image_style']) : '',
                    'aspect_ratio' => isset($_POST['aspect_ratio']) ? sanitize_text_field($_POST['aspect_ratio']) : '',
                    'negative_prompt' => isset($_POST['negative_prompt']) ? sanitize_text_field($_POST['negative_prompt']) : '',
                    'image_prompt_details' => isset($_POST['image_prompt_details']) ? sanitize_text_field($_POST['image_prompt_details']) : '',
                    'instructions' => isset($_POST['instructions']) ? sanitize_textarea_field($_POST['instructions']) : ''
                );
            }

            // بررسی وجود فیلدهای ضروری
            if (empty($form_data['topic']) || empty($form_data['keywords'])) {
                wp_send_json_error(array('message' => 'لطفاً موضوع و کلمات کلیدی را وارد کنید'));
                return;
            }

            // لاگ برای عیب‌یابی (در صورت نیاز می‌توانید فعال کنید)
            // error_log('SETIA DEBUG: Parsed form data: ' . print_r($form_data, true));

            // اعتبارسنجی طول داده‌ها
            if (strlen($form_data['topic']) > 200) {
                wp_send_json_error(array('message' => 'موضوع نباید بیش از 200 کاراکتر باشد'));
                return;
            }

            if (strlen($form_data['keywords']) > 500) {
                wp_send_json_error(array('message' => 'کلمات کلیدی نباید بیش از 500 کاراکتر باشد'));
                return;
            }
            
            // بهینه‌سازی عنوان پست
            $optimized_title = $this->optimize_post_title($form_data['topic'], $form_data['keywords']);
            $form_data['optimized_title'] = $optimized_title;
            
            // ساخت پرامپت برای Gemini
            $prompt = $this->build_content_prompt($form_data);
            
            // تنظیم پارامترهای تولید متن
            $length_params = $this->get_length_params($form_data['length']);
            
            // ارسال درخواست به Gemini API
            $response = $this->content_generator->generate_text($prompt, $length_params);
            
            if (!$response['success']) {
                wp_send_json_error(array('message' => $response['error']));
                return;
            }
            

            
            // Initialize potentially missing response fields
            $parsedown_found = false;
            $generated_text_markdown = $response['text']; // Store raw markdown
            $generated_text_html = $generated_text_markdown; // Default to raw if Parsedown fails
            
            // تبدیل مارک‌داون به HTML
            if (class_exists('Parsedown')) {
                try {
                    $parsedown = new Parsedown();
                    $generated_text_html = $parsedown->text($generated_text_markdown);
                    $parsedown_found = true;
                    error_log("SETIA INFO: Successfully converted markdown to HTML using Parsedown");
                } catch (Throwable $e) {
                    error_log("SETIA ERROR: Exception in Parsedown: " . $e->getMessage());
                    // Fallback to wpautop for basic formatting
                    $generated_text_html = wpautop($generated_text_markdown);
                }
            } else {
                error_log("SETIA WARNING: Parsedown class not found, using wpautop");
                // اگر کلاس Parsedown وجود ندارد، سعی می‌کنیم آن را بارگذاری کنیم
                $parsedown_path = dirname(__FILE__) . '/Parsedown.php';
                if (file_exists($parsedown_path)) {
                    try {
                        require_once $parsedown_path;
                        if (class_exists('Parsedown')) {
                            $parsedown = new Parsedown();
                            $generated_text_html = $parsedown->text($generated_text_markdown);
                            $parsedown_found = true;
                            error_log("SETIA INFO: Successfully loaded and used Parsedown after initial failure");
                        } else {
                            $generated_text_html = wpautop($generated_text_markdown);
                        }
                    } catch (Throwable $e) {
                        error_log("SETIA ERROR: Exception when trying to load Parsedown: " . $e->getMessage());
                        $generated_text_html = wpautop($generated_text_markdown);
                    }
                } else {
                    $generated_text_html = wpautop($generated_text_markdown);
                }
            }
            

            
            // تولید تصویر اگر درخواست شده باشد
            $image_url = null;
            $image_is_fallback = false;
            $image_error = null;
            
            if (isset($form_data['generate_image']) && $form_data['generate_image'] === 'yes') {

                try {
                    // تولید خلاصه‌ای از محتوا برای پرامپت تصویر
                    $text_summary = '';
                    if (!empty($generated_text_markdown)) {
                        // حذف تیترها و موارد اضافی برای استخراج متن خالص
                        $clean_text = preg_replace('/^#.*$/m', '', $generated_text_markdown); // حذف تیترها
                        $clean_text = preg_replace('/[\*\_`]/', '', $clean_text); // حذف کاراکترهای مارک‌داون
                        $clean_text = trim($clean_text);
                        
                        // استخراج چند جمله اول
                        $sentences = preg_split('/(?<=[.?!])\s+/', $clean_text, 3, PREG_SPLIT_NO_EMPTY);
                        if (count($sentences) > 2) {
                            array_pop($sentences); // حذف آخرین جمله که ممکن است ناقص باشد
                        }
                        $text_summary = implode(' ', $sentences);
                        
                        // محدود کردن طول خلاصه
                        if (mb_strlen($text_summary) > 400) {
                            $text_summary = mb_substr($text_summary, 0, 400) . '...';
                        }
                    }

                    // ساخت پرامپت دقیق‌تر برای تصویر
                    $image_prompt = 'یک تصویر برای مقاله با موضوع "' . $form_data['topic'] . '" تولید کن.';
                    $image_prompt .= ' کلمات کلیدی اصلی عبارتند از: ' . $form_data['keywords'] . '.';
                    
                    // اضافه کردن جزئیات وارد شده توسط کاربر
                    if (!empty($form_data['image_prompt_details'])) {
                        $image_prompt .= ' جزئیات مهم برای تصویر: ' . sanitize_text_field($form_data['image_prompt_details']) . '.';
                    }
                    
                    // اضافه کردن خلاصه محتوا به پرامپت
                    if (!empty($text_summary)) {
                        $image_prompt .= ' خلاصه محتوای مقاله جهت ایده گرفتن برای تصویر: "' . $text_summary . '".';
                    }
                    
                    // اضافه کردن دستورات نهایی برای بهبود کیفیت
                    $image_prompt .= ' تصویر باید حرفه‌ای، باکیفیت و جذاب باشد و با موضوع اصلی مقاله کاملا مرتبط باشد.';
                    
                    // جمع‌آوری پارامترهای تصویر
                    $image_params = array();
                    if (isset($form_data['image_style']) && !empty($form_data['image_style'])) {
                        $image_params['style'] = sanitize_text_field($form_data['image_style']);
                    }
                    if (isset($form_data['aspect_ratio']) && !empty($form_data['aspect_ratio'])) {
                        $image_params['aspect_ratio'] = sanitize_text_field($form_data['aspect_ratio']);
                    }
                    if (isset($form_data['negative_prompt']) && !empty($form_data['negative_prompt'])) {
                        $image_params['negative_prompt'] = sanitize_text_field($form_data['negative_prompt']);
                    }
                    

                    $image_response = $this->content_generator->generate_image($image_prompt, $image_params);
                    
                    if (!$image_response['success']) {
                        error_log("SETIA ERROR: Image generation failed: " . ($image_response['error'] ?? 'Unknown error'));
                        // در اینجا خطا را ثبت می‌کنیم اما روند تولید محتوا را ادامه می‌دهیم
                        $image_url = null;
                    } else {

                        $image_url = $image_response['image_url'];
                        
                        // بررسی اگر تصویر fallback است
                        if (isset($image_response['is_fallback']) && $image_response['is_fallback']) {
                            error_log("SETIA WARNING: Using fallback image due to: " . ($image_response['error'] ?? 'Unknown reason'));
                            $image_is_fallback = true;
                            $image_error = $image_response['error'] ?? 'خطای نامشخص در تولید تصویر';
                        }
                        
                        // آزمایش دسترسی به تصویر برای اطمینان از صحت
                        $image_test = wp_remote_head($image_url);
                        if (is_wp_error($image_test) || wp_remote_retrieve_response_code($image_test) !== 200) {
                            // اگر URL تصویر قابل دسترسی نیست، از URL پیش‌فرض استفاده می‌کنیم
                            error_log('SETIA ERROR: Generated image is not accessible: ' . $image_url);
                            $text = urlencode(mb_substr($form_data['topic'], 0, 50));
                            $image_url = "https://via.placeholder.com/800x450?text={$text}";
                            $image_is_fallback = true;
                            $image_error = 'تصویر تولید شده قابل دسترسی نیست';
                        }
                    }
                } catch (Exception $e) {
                    error_log("SETIA ERROR: Exception in image generation: " . $e->getMessage());
                    $image_url = null;
                }
            } else {

            }
            
            // تولید متا تگ‌های سئو
            $seo_meta = $this->generate_seo_meta($form_data['topic'], $form_data['keywords'], $generated_text_markdown);
            
            // محاسبه امتیاز محتوا
            $content_score = $this->calculate_seo_score($generated_text_markdown, explode(',', $form_data['keywords'])[0]);
            $readability_score = $this->calculate_readability_score($generated_text_markdown);

            
            // ذخیره محتوای تولید شده در دیتابیس
            $content_id = $this->save_generated_content($form_data, $generated_text_markdown, $image_url, $seo_meta);
            
            // ارسال پاسخ به کلاینت
            wp_send_json_success(array(
                'content' => $generated_text_html,
                'optimized_title' => $optimized_title,
                'seo' => $seo_meta,
                'content_score' => $content_score,
                'readability_score' => $readability_score,
                'image_url' => $image_url,
                'content_id' => $content_id,
                'is_fallback' => $image_is_fallback,
                'error' => $image_error
            ));
            
        } catch (Exception $e) {
            error_log("SETIA CRITICAL: Unhandled exception in generate_content: " . $e->getMessage());
            error_log("SETIA CRITICAL: Exception trace: " . $e->getTraceAsString());
            wp_send_json_error(array('message' => 'خطای غیرمنتظره: ' . $e->getMessage()));
        }
    }
    
    /**
     * انتشار محتوای تولید شده به عنوان پست
     */
    public function publish_content() {
        // بررسی امنیتی
        if (!wp_verify_nonce($_POST['nonce'], 'setia-nonce')) {
            wp_send_json_error(array('message' => 'خطای امنیتی: توکن نامعتبر'));
            return;
        }
        
        // دریافت شناسه محتوا
        $content_id = isset($_POST['content_id']) ? intval($_POST['content_id']) : 0;
        
        // دریافت محتوای مورد نظر
        global $wpdb;
        $table_name = $wpdb->prefix . 'setia_generated_content';
        
        if ($content_id > 0) {
            $content = $wpdb->get_row($wpdb->prepare("SELECT * FROM $table_name WHERE id = %d", $content_id));
        } else {
            // برای سازگاری با نسخه‌های قبلی، اگر content_id ارسال نشده باشد، آخرین محتوا را می‌گیریم
        $content = $wpdb->get_row("SELECT * FROM $table_name ORDER BY id DESC LIMIT 1");
        }
        
        if (!$content) {
            wp_send_json_error(array('message' => 'محتوایی برای انتشار یافت نشد'));
        }
        
        // ایجاد پست
        $status = sanitize_text_field($_POST['status'] ?? 'draft');
        $result = $this->create_post_from_content($content, $status);
        
        if (!$result['success']) {
            wp_send_json_error(array('message' => $result['error']));
        }
        
        // بروزرسانی رکورد در دیتابیس
        $wpdb->update(
            $table_name,
            array('post_id' => $result['post_id']),
            array('id' => $content->id),
            array('%d'),
            array('%d')
        );
        
        wp_send_json_success(array(
            'post_id' => $result['post_id'],
            'edit_url' => $result['edit_url']
        ));
    }
    
    /**
     * تست اتصال به API‌ها
     */
    public function test_connection() {
        // بررسی امنیتی
        if (!wp_verify_nonce($_POST['nonce'], 'setia_test_connection')) {
            wp_send_json_error(array('message' => 'خطای امنیتی: توکن نامعتبر'));
            return;
        }
        
        // دریافت کلید API
        $gemini_api_key = sanitize_text_field($_POST['gemini_api_key']);
        
        $result = array(
            'gemini_success' => false,
            'gemini_message' => ''
        );
        
        // بررسی وجود کلید API
        if (empty($gemini_api_key)) {
            $result['gemini_message'] = 'کلید API Google AI وارد نشده است';
            wp_send_json_error($result);
            return;
        }
        
        // تست 1: اتصال به Gemini برای تولید متن
        $gemini_url = "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=$gemini_api_key";
        $gemini_body = json_encode([
            "contents" => [
                ["parts" => [["text" => "سلام"]]]
            ],
            "generationConfig" => [
                "maxOutputTokens" => 50
            ]
        ]);
        
        $gemini_response = wp_remote_post($gemini_url, [
            'headers' => [
                'Content-Type' => 'application/json'
            ],
            'body' => $gemini_body,
            'timeout' => 15
        ]);
        
        // بررسی پاسخ Gemini
        if (is_wp_error($gemini_response)) {
            $result['gemini_message'] = 'خطا در اتصال به Gemini: ' . $gemini_response->get_error_message();
        } else {
            $gemini_code = wp_remote_retrieve_response_code($gemini_response);
            if ($gemini_code != 200) {
                $response_body = wp_remote_retrieve_body($gemini_response);
                $result['gemini_message'] = 'خطای Gemini: ' . $gemini_code . ' - ' . $response_body;
            } else {
                // تست 2: اتصال به Imagen برای تولید تصویر
                $imagen_url = "https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key=$gemini_api_key";
                $imagen_body = json_encode([
                    "contents" => [
                        [
                            "parts" => [
                                [
                                    "text" => "A simple test image"
                                ]
                            ]
                        ]
                    ]
                ]);
                
                $imagen_response = wp_remote_post($imagen_url, [
                    'headers' => [
                        'Content-Type' => 'application/json'
                    ],
                    'body' => $imagen_body,
                    'timeout' => 20
                ]);
                
                // بررسی پاسخ Imagen
                if (is_wp_error($imagen_response)) {
                    $result['gemini_message'] = 'اتصال به Gemini موفق، اما خطا در اتصال به Imagen: ' . $imagen_response->get_error_message();
                } else {
                    $imagen_code = wp_remote_retrieve_response_code($imagen_response);
                    if ($imagen_code != 200) {
                        $imagen_body = wp_remote_retrieve_body($imagen_response);
                        $result['gemini_message'] = 'اتصال به Gemini موفق، اما خطای Imagen: ' . $imagen_code . ' - ' . $imagen_body;
                    } else {
                        // هر دو اتصال موفق بوده است
                        $result['gemini_success'] = true;
                        $result['gemini_message'] = 'اتصال به سرویس‌های Gemini و Imagen موفقیت‌آمیز است';
                    }
                }
            }
        }
        
        // بازگرداندن نتیجه
        if ($result['gemini_success']) {
            wp_send_json_success($result);
        } else {
            wp_send_json_error($result);
        }
    }
    
    // توابع دیگر AJAX
    public function get_content_details() {
        // بررسی امنیتی
        if (!wp_verify_nonce($_POST['nonce'], 'setia_content_nonce')) {
            wp_send_json_error(array('message' => 'خطای امنیتی: توکن نامعتبر'));
            return;
        }
        
        $content_id = intval($_POST['content_id']);
        
        if (!$content_id) {
            wp_send_json_error(array('message' => 'شناسه محتوا نامعتبر است'));
        }
        
        // دریافت محتوا از دیتابیس
        global $wpdb;
        $table_name = $wpdb->prefix . 'setia_generated_content';
        
        $content = $wpdb->get_row($wpdb->prepare("SELECT * FROM $table_name WHERE id = %d", $content_id));
        
        if (!$content) {
            wp_send_json_error(array('message' => 'محتوا یافت نشد'));
        }
        
        // آماده‌سازی داده‌های سئو
        $seo_meta = json_decode($content->seo_meta, true);
        
        // تبدیل متن مارک‌داون به HTML با استفاده از Parsedown
        $html_content = $content->generated_text; // مقدار پیش‌فرض در صورت خطا
        
        // بررسی وجود کلاس Parsedown و تبدیل مارک‌داون به HTML
        if (class_exists('Parsedown')) {
            try {
                $parsedown = new Parsedown();
                $html_content = $parsedown->text($content->generated_text);
                error_log("SETIA: Markdown successfully converted with Parsedown in get_content_details.");
            } catch (Exception $e) {
                error_log("SETIA ERROR: Exception when using Parsedown in get_content_details: " . $e->getMessage());
                // استفاده از wpautop به عنوان پشتیبان
                $html_content = wpautop($content->generated_text);
            }
        } else {
            error_log('SETIA ERROR: Parsedown class not found in get_content_details. Using wpautop as fallback.');
            $html_content = wpautop($content->generated_text);
        }
        
        wp_send_json_success(array(
            'topic' => $content->topic,
            'content' => $html_content,
            'raw_markdown' => $content->generated_text, // ارسال متن خام مارک‌داون برای استفاده احتمالی
            'parsedown_found' => class_exists('Parsedown'), // ارسال وضعیت Parsedown برای اشکال‌زدایی
            'image_url' => $content->generated_image_url,
            'seo' => $seo_meta
        ));
    }
    
    public function publish_from_history() {
        // بررسی امنیتی
        if (!wp_verify_nonce($_POST['nonce'], 'setia_content_nonce')) {
            wp_send_json_error(array('message' => 'خطای امنیتی: توکن نامعتبر'));
            return;
        }
        
        $content_id = intval($_POST['content_id']);
        
        if (!$content_id) {
            wp_send_json_error(array('message' => 'شناسه محتوا نامعتبر است'));
        }
        
        // دریافت محتوا از دیتابیس
        global $wpdb;
        $table_name = $wpdb->prefix . 'setia_generated_content';
        
        $content = $wpdb->get_row($wpdb->prepare("SELECT * FROM $table_name WHERE id = %d", $content_id));
        
        if (!$content) {
            wp_send_json_error(array('message' => 'محتوا یافت نشد'));
        }
        
        // ایجاد پست
        $result = $this->create_post_from_content($content, 'draft');
        
        if (!$result['success']) {
            wp_send_json_error(array('message' => $result['error']));
        }
        
        // بروزرسانی رکورد در دیتابیس
        $wpdb->update(
            $table_name,
            array('post_id' => $result['post_id']),
            array('id' => $content->id),
            array('%d'),
            array('%d')
        );
        
        wp_send_json_success(array(
            'post_id' => $result['post_id'],
            'edit_url' => $result['edit_url']
        ));
    }
    
    public function delete_content() {
        // بررسی امنیتی
        if (!wp_verify_nonce($_POST['nonce'], 'setia_content_nonce')) {
            wp_send_json_error(array('message' => 'خطای امنیتی: توکن نامعتبر'));
            return;
        }
        
        $content_id = intval($_POST['content_id']);
        
        if (!$content_id) {
            wp_send_json_error(array('message' => 'شناسه محتوا نامعتبر است'));
        }
        
        // حذف از دیتابیس
        global $wpdb;
        $table_name = $wpdb->prefix . 'setia_generated_content';
        
        $result = $wpdb->delete(
            $table_name,
            array('id' => $content_id),
            array('%d')
        );
        
        if ($result === false) {
            wp_send_json_error(array('message' => 'خطا در حذف محتوا'));
        }
        
        wp_send_json_success(array('message' => 'محتوا با موفقیت حذف شد'));
    }
    
    /**
     * حذف گروهی محتواها
     */
    public function bulk_delete_content() {
        // بررسی امنیتی
        if (!wp_verify_nonce($_POST['nonce'], 'setia_content_nonce')) {
            wp_send_json_error(array('message' => 'خطای امنیتی: توکن نامعتبر'));
            return;
        }
        
        // دریافت آرایه شناسه‌ها
        $content_ids = $_POST['content_ids'];
        
        if (!is_array($content_ids) || empty($content_ids)) {
            wp_send_json_error(array('message' => 'هیچ محتوایی برای حذف انتخاب نشده است'));
            return;
        }
        
        // تبدیل شناسه‌ها به عدد صحیح
        $content_ids = array_map('intval', $content_ids);
        
        // حذف از دیتابیس
        global $wpdb;
        $table_name = $wpdb->prefix . 'setia_generated_content';
        
        // ساخت رشته شرط IN برای کوئری
        $placeholders = implode(', ', array_fill(0, count($content_ids), '%d'));
        
        $query = $wpdb->prepare(
            "DELETE FROM $table_name WHERE id IN ($placeholders)",
            $content_ids
        );
        
        $result = $wpdb->query($query);
        
        if ($result === false) {
            wp_send_json_error(array('message' => 'خطا در حذف محتواها'));
            return;
        }
        
        wp_send_json_success(array(
            'message' => sprintf('%d محتوا با موفقیت حذف شد', $result),
            'count' => $result
        ));
    }
    
    /**
     * تولید تصویر تست برای صفحه تنظیمات
     */
    public function handle_test_image_generation() {
        // اضافه کردن کد اشکال‌زدایی
        error_log("SETIA DEBUG: generate_test_image ajax handler started");
        
        try {
            // بررسی امنیتی
            if (!wp_verify_nonce($_POST['nonce'], 'setia_test_connection')) {
                error_log("SETIA ERROR: Security nonce verification failed in generate_test_image");
                wp_send_json_error(array('message' => 'خطای امنیتی: توکن نامعتبر'));
                return;
            }
            
            // دریافت کلید API و پارامترها
            $imagine_art_api_key = sanitize_text_field($_POST['imagine_art_api_key']); // Assuming this is the correct API key for Vyro
            $prompt = sanitize_text_field($_POST['prompt']);
            $image_style = isset($_POST['image_style']) ? sanitize_text_field($_POST['image_style']) : 'realistic';
            $aspect_ratio = isset($_POST['aspect_ratio']) ? sanitize_text_field($_POST['aspect_ratio']) : '1:1';
            $negative_prompt = isset($_POST['negative_prompt']) ? sanitize_text_field($_POST['negative_prompt']) : '';
            
            // ثبت لاگ برای دیباگ
            error_log('SETIA DEBUG: Test image generation parameters - Prompt="' . $prompt . '", Style="' . $image_style . '", AspectRatio="' . $aspect_ratio . '", NegativePrompt="' . $negative_prompt . '"');
            error_log('SETIA DEBUG: API Key (first 5 chars): ' . substr($imagine_art_api_key, 0, 5) . '...');
            
            // بررسی وجود کلید API و پرامپت
            if (empty($imagine_art_api_key)) {
                // Try to load from saved settings if not passed directly (e.g. if test button doesn't send all keys)
                $this->content_generator->load_settings(); 
                $imagine_art_api_key = $this->content_generator->imagine_art_api_key; // Access public property
                error_log('SETIA DEBUG: API key from settings (first 5 chars): ' . substr($imagine_art_api_key, 0, 5) . '...');
                
                if (empty($imagine_art_api_key)){
                    error_log('SETIA ERROR: Imagine Art API key is empty');
                    wp_send_json_error(array('message' => 'کلید API Imagine Art (Vyro) وارد یا ذخیره نشده است'));
                    return;
                }
            }
            
            if (empty($prompt)) {
                error_log('SETIA ERROR: Prompt is empty');
                wp_send_json_error(array('message' => 'لطفا یک پرامپت برای تولید تصویر وارد کنید'));
                return;
            }
            
            // آماده‌سازی پارامترها برای ارسال به تابع تولید تصویر
            $image_params = array(
                'style' => $image_style,
                'aspect_ratio' => $aspect_ratio,
                'negative_prompt' => $negative_prompt
            );
            
            error_log('SETIA DEBUG: Calling generate_image with params: ' . json_encode($image_params));
            
            // تست اتصال به سرور Vyro قبل از ارسال درخواست اصلی
            $test_connection = wp_remote_get('https://api.vyro.ai/v2/status', [
                'timeout' => 10
            ]);
            
            if (is_wp_error($test_connection)) {
                error_log('SETIA ERROR: Cannot connect to Vyro API server: ' . $test_connection->get_error_message());
                wp_send_json_error(array(
                    'message' => 'خطا در اتصال به سرور Vyro: ' . $test_connection->get_error_message(),
                    'additional_debug' => 'مشکل اتصال به سرور Vyro. لطفاً وضعیت اینترنت و فیلترشکن خود را بررسی کنید.'
                ));
                return;
            }
            
            error_log('SETIA DEBUG: Vyro API server connection test result: ' . wp_remote_retrieve_response_code($test_connection) . ' - ' . wp_remote_retrieve_body($test_connection));
            
            // تولید تصویر با استفاده از متد generate_image کلاس اصلی
            // اطمینان از اینکه کلید API صحیح به تابع generate_image ارسال می‌شود
            // تابع generate_image از کلید ذخیره شده در $this->imagine_art_api_key استفاده می‌کند.
            $current_plugin_instance = $this->content_generator;
            $current_plugin_instance->imagine_art_api_key = $imagine_art_api_key; // Temporarily set for this call if needed, or ensure load_settings was called

            $image_response = $current_plugin_instance->generate_image($prompt, $image_params);
            
            if (!$image_response['success']) {
                error_log('SETIA ERROR: Test image generation failed: ' . ($image_response['error'] ?? 'Unknown error'));
                // اطلاعات خطای کامل‌تر برای دیباگ
                wp_send_json_error(array(
                    'message' => $image_response['error'] ?? 'خطای نامشخص',
                    'additional_debug' => 'لطفاً لاگ‌های سرور را برای جزئیات بیشتر بررسی کنید.'
                ));
                return;
            }
            
            // بررسی اگر تصویر fallback است
            if (isset($image_response['is_fallback']) && $image_response['is_fallback']) {
                error_log('SETIA WARNING: Test image using fallback due to: ' . ($image_response['error'] ?? 'Unknown reason'));
                wp_send_json_error(array(
                    'message' => 'تصویر با خطا مواجه شد و از تصویر پیش‌فرض استفاده شد: ' . ($image_response['error'] ?? 'خطای نامشخص'),
                    'image_url' => $image_response['image_url'], // ارسال URL تصویر پیش‌فرض
                    'is_fallback' => true
                ));
                return;
            }
            
            // ثبت URL تصویر تولید شده برای دیباگ
            error_log('SETIA DEBUG: Test image generation successful: URL="' . $image_response['image_url'] . '"');
            
            // آزمایش دسترسی به تصویر
            $image_test = wp_remote_head($image_response['image_url']);
            if (is_wp_error($image_test)) {
                error_log('SETIA ERROR: Cannot access generated image: ' . $image_test->get_error_message());
                wp_send_json_error(array(
                    'message' => 'تصویر تولید شد اما قابل دسترسی نیست: ' . $image_test->get_error_message(),
                    'image_url' => $image_response['image_url']
                ));
                return;
            }
            
            $http_code = wp_remote_retrieve_response_code($image_test);
            if ($http_code !== 200) {
                error_log('SETIA ERROR: Generated image returned non-200 status: ' . $http_code);
                wp_send_json_error(array(
                    'message' => 'تصویر تولید شد اما با کد HTTP غیر 200 مواجه شد: ' . $http_code,
                    'image_url' => $image_response['image_url']
                ));
                return;
            }
            
            // برگرداندن URL تصویر
            wp_send_json_success(array(
                'image_url' => $image_response['image_url'],
                'message' => 'تصویر با موفقیت تولید شد.'
            ));
            
        } catch (Exception $e) {
            error_log('SETIA CRITICAL: Unhandled exception in generate_test_image: ' . $e->getMessage());
            error_log('SETIA CRITICAL: Exception trace: ' . $e->getTraceAsString());
            wp_send_json_error(array(
                'message' => 'خطای غیرمنتظره: ' . $e->getMessage(),
                'additional_debug' => 'خطای کریتیکال در سیستم تولید تصویر. لطفاً لاگ‌های سرور را بررسی کنید.'
            ));
        }
    }

    /**
     * برای حفظ سازگاری با کد قدیمی
     */
    public function generate_test_image() {
        $this->handle_test_image_generation();
    }

    /**
     * برای حفظ سازگاری با کد قدیمی
     */
    public function test_image_generation() {
        $this->handle_test_image_generation();
    }
    
    // توابع کمکی
    private function optimize_post_title($topic, $keywords) {
        // جمع‌آوری کلید های API از تنظیمات
        $settings = get_option('setia_settings', array());
        $gemini_api_key = isset($settings['gemini_api_key']) ? $settings['gemini_api_key'] : '';
        
        // اگر کلید API تنظیم نشده است، از روش قبلی استفاده می‌کنیم
        if (empty($gemini_api_key)) {
            return $this->fallback_optimize_post_title($topic, $keywords);
        }
        
        try {
            // انتخاب الگوی تصادفی برای تنوع در عناوین
            $title_patterns = $this->get_diverse_title_patterns();
            $selected_pattern = $title_patterns[array_rand($title_patterns)];

            // ساخت پرامپت پیشرفته برای تولید عنوان متنوع
            $prompt = "تو یک متخصص سئو و کپی‌رایتر حرفه‌ای هستی. با توجه به موضوع و کلمات کلیدی زیر، یک عنوان بهینه و خلاقانه تولید کن:\n\n";
            $prompt .= "موضوع: {$topic}\n";
            $prompt .= "کلمات کلیدی: {$keywords}\n\n";
            $prompt .= "الگوی مورد نظر برای این عنوان: {$selected_pattern['description']}\n";
            $prompt .= "مثال: {$selected_pattern['example']}\n\n";
            $prompt .= "قوانین مهم:\n";
            $prompt .= "1. طول عنوان: 45-65 کاراکتر\n";
            $prompt .= "2. کلمه کلیدی اصلی را در ابتدا یا جایگاه مناسب قرار بده\n";
            $prompt .= "3. از الگوی انتخاب شده پیروی کن اما خلاقانه باش\n";
            $prompt .= "4. عنوان باید کلیک‌پذیر و جذاب باشد\n";
            $prompt .= "5. از اعداد متنوع استفاده کن (نه فقط ۷)\n";
            $prompt .= "6. برای مخاطب فارسی‌زبان مناسب باشد\n\n";
            $prompt .= "فقط عنوان نهایی را برگردان، بدون توضیح اضافی.";
            
            // ارسال درخواست به Gemini API
            $url = "https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key={$gemini_api_key}";
            $data = [
                "contents" => [
                    [
                        "parts" => [
                            [
                                "text" => $prompt
                            ]
                        ]
                    ]
                ],
                "generationConfig" => [
                    "temperature" => 0.7,
                    "topK" => 40,
                    "topP" => 0.95,
                    "maxOutputTokens" => 100
                ]
            ];
            
            $response = wp_remote_post($url, [
                'headers' => [
                    'Content-Type' => 'application/json'
                ],
                'body' => json_encode($data),
                'timeout' => 15
            ]);
            
            if (is_wp_error($response)) {
                error_log('SETIA: خطا در تولید عنوان با هوش مصنوعی: ' . $response->get_error_message());
                return $this->fallback_optimize_post_title($topic, $keywords);
            }
            
            $response_code = wp_remote_retrieve_response_code($response);
            if ($response_code !== 200) {
                error_log('SETIA: خطا در دریافت پاسخ از API (کد ' . $response_code . ')');
                return $this->fallback_optimize_post_title($topic, $keywords);
            }
            
            $response_body = wp_remote_retrieve_body($response);
            $result = json_decode($response_body, true);
            
            // بررسی وجود پاسخ معتبر
            if (isset($result['candidates'][0]['content']['parts'][0]['text'])) {
                $ai_title = trim($result['candidates'][0]['content']['parts'][0]['text']);
                
                // حذف نقل قول‌ها از عنوان اگر وجود داشته باشد
                $ai_title = str_replace(array('"', "'", "«", "»"), "", $ai_title);
                
                // بررسی طول عنوان
                if (mb_strlen($ai_title) > 0 && mb_strlen($ai_title) <= 70) {
                    error_log('SETIA: عنوان جدید با AI تولید شد: ' . $ai_title);
                    return $ai_title;
                }
            }
            
            // اگر به هر دلیلی عنوان معتبر از AI دریافت نشد، از روش پشتیبان استفاده می‌کنیم
            return $this->fallback_optimize_post_title($topic, $keywords);
            
        } catch (Exception $e) {
            error_log('SETIA: خطا در فرایند تولید عنوان: ' . $e->getMessage());
            return $this->fallback_optimize_post_title($topic, $keywords);
        }
    }

    // تابع جدید برای ارائه الگوهای متنوع عنوان
    private function get_diverse_title_patterns() {
        return [
            [
                'description' => 'سوال مستقیم و جذاب',
                'example' => 'چگونه گل رز قرمز را در خانه پرورش دهیم؟'
            ],
            [
                'description' => 'راهنمای گام به گام',
                'example' => 'راهنمای کامل پرورش گل رز قرمز در ۵ مرحله'
            ],
            [
                'description' => 'لیست نکات کاربردی',
                'example' => '۱۰ نکته طلایی برای نگهداری گل رز قرمز'
            ],
            [
                'description' => 'مقایسه و انتخاب',
                'example' => 'گل رز قرمز در مقابل صورتی: کدام بهتر است؟'
            ],
            [
                'description' => 'حقایق شگفت‌انگیز',
                'example' => '۸ حقیقت باورنکردنی درباره گل رز قرمز'
            ],
            [
                'description' => 'اشتباهات رایج',
                'example' => '۶ اشتباه رایج در نگهداری گل رز قرمز'
            ],
            [
                'description' => 'بهترین روش‌ها',
                'example' => 'بهترین روش‌های آبیاری گل رز قرمز در تابستان'
            ],
            [
                'description' => 'دلایل و علت‌ها',
                'example' => 'چرا گل رز قرمز نماد عشق محسوب می‌شود؟'
            ],
            [
                'description' => 'راز موفقیت',
                'example' => 'راز باغبان‌های حرفه‌ای برای گل رز قرمز'
            ],
            [
                'description' => 'قبل و بعد',
                'example' => 'گل رز قرمز: از کاشت تا شکوفایی کامل'
            ],
            [
                'description' => 'ترفندهای کاربردی',
                'example' => '۱۲ ترفند جادویی برای گل رز قرمز زیباتر'
            ],
            [
                'description' => 'همه چیز درباره',
                'example' => 'همه چیز درباره گل رز قرمز که نمی‌دانستید'
            ],
            [
                'description' => 'مشکلات و راه‌حل',
                'example' => 'گل رز قرمز پژمرده شده؟ ۹ راه‌حل فوری'
            ],
            [
                'description' => 'انتخاب هوشمندانه',
                'example' => 'چطور بهترین گل رز قرمز را انتخاب کنیم؟'
            ],
            [
                'description' => 'زمان‌بندی مناسب',
                'example' => 'بهترین زمان برای کاشت گل رز قرمز'
            ]
        ];
    }

    // روش پشتیبان برای بهینه‌سازی عنوان در صورت خطا در روش هوش مصنوعی
    private function fallback_optimize_post_title($topic, $keywords) {
        // استخراج کلیدواژه اصلی
        $main_keyword = trim(explode(',', $keywords)[0]);
        
        // الگوهای ساده برای عنوان‌های پشتیبان
        $fallback_patterns = [
            'چگونه {keyword} را {action}؟',
            '{keyword}: راهنمای کامل برای مبتدیان',
            '۱۰ نکته مهم درباره {keyword}',
            'همه چیز درباره {keyword}',
            '{keyword}: بهترین روش‌های {action}',
            'چرا {keyword} مهم است؟',
            '{keyword} در سال ۲۰۲۴: آنچه باید بدانید'
        ];

        // انتخاب الگوی تصادفی
        $selected_pattern = $fallback_patterns[array_rand($fallback_patterns)];

        // تعیین عمل مناسب بر اساس موضوع
        $actions = ['استفاده کنیم', 'انتخاب کنیم', 'بهبود دهیم', 'مراقبت کنیم', 'نگهداری کنیم'];
        $action = $actions[array_rand($actions)];

        // جایگزینی متغیرها در الگو
        $optimized_title = str_replace(['{keyword}', '{action}'], [$main_keyword, $action], $selected_pattern);

        // اگر عنوان خیلی طولانی شد، از روش ساده استفاده کن
        if (mb_strlen($optimized_title) > 65) {
            if (stripos($topic, $main_keyword) !== false) {
                $optimized_title = $topic;
            } else {
                $optimized_title = $main_keyword . ': ' . $topic;
                if (mb_strlen($optimized_title) > 60) {
                    $optimized_title = $topic . ' | ' . $main_keyword;
                }
            }
        }
        
        // محدود کردن طول عنوان به 60 کاراکتر (بهینه برای نمایش در نتایج گوگل)
        if (mb_strlen($optimized_title) > 60) {
            // برش عنوان با حفظ کلمات کامل
            $optimized_title = mb_substr($optimized_title, 0, 57);
            // برش تا آخرین فاصله برای جلوگیری از قطع شدن کلمات
            $last_space = mb_strrpos($optimized_title, ' ');
            if ($last_space !== false) {
                $optimized_title = mb_substr($optimized_title, 0, $last_space);
            }
            $optimized_title .= '...';
        }
        
        return $optimized_title;
    }
    
    private function build_content_prompt($form_data) {
        $topic = sanitize_text_field($form_data['topic']);
        $keywords = sanitize_text_field($form_data['keywords']);
        $tone = sanitize_text_field($form_data['tone']);
        $length = sanitize_text_field($form_data['length']);
        $instructions = sanitize_textarea_field($form_data['instructions'] ?? '');
        $optimized_title = $form_data['optimized_title'] ?? $topic;
        
        // استخراج کلیدواژه اصلی
        $main_keyword = trim(explode(',', $keywords)[0]);
        
        $prompt = "لطفاً یک مقاله با عنوان «{$optimized_title}» در مورد «{$topic}» بنویس با کلمات کلیدی: {$keywords}.\n";
        $prompt .= "لحن مقاله باید {$tone} باشد.\n";
        
        // اضافه کردن طول مطلب
        switch ($length) {
            case 'کوتاه':
                $prompt .= "مقاله باید کوتاه (حدود ۵۰۰ کلمه) باشد.\n";
                break;
            case 'متوسط':
                $prompt .= "مقاله باید متوسط (حدود ۱۰۰۰ کلمه) باشد.\n";
                break;
            case 'بلند':
                $prompt .= "مقاله باید بلند (حدود ۱۵۰۰ کلمه) باشد.\n";
                break;
            case 'خیلی بلند':
                $prompt .= "مقاله باید خیلی بلند (حدود ۲۰۰۰ کلمه) باشد.\n";
                break;
        }
        
        if (!empty($instructions)) {
            $prompt .= "دستورالعمل‌های اضافی: {$instructions}\n";
        }
        
        // دستورالعمل‌های مربوط به SEO (بر اساس بهترین شیوه‌های Yoast SEO)
        $prompt .= "\n\nدستورالعمل‌های بهینه‌سازی SEO (بسیار مهم):";
        
        // کلمه کلیدی در مقدمه - طبق توصیه Yoast برای "Focus keyphrase in introduction"
        $prompt .= "\n1. بسیار مهم: کلمه کلیدی اصلی ({$main_keyword}) را در 10% ابتدایی متن و ترجیحاً در جمله اول پاراگراف اول استفاده کن. این به موتورهای جستجو نشان می‌دهد که متن دقیقاً درباره چیست.";
        
        // توزیع کلمه کلیدی - طبق توصیه Yoast برای "Keyphrase distribution"
        $prompt .= "\n2. توزیع کلمات کلیدی را در کل متن به صورت طبیعی و یکنواخت انجام بده. کلمه کلیدی باید در ابتدا، وسط و انتهای متن ظاهر شود. حداقل 30% از پاراگراف‌ها باید شامل کلمه کلیدی یا مترادف‌های آن باشند.";
        
        // تراکم کلمه کلیدی - طبق توصیه Yoast برای "Keyword density"
        $prompt .= "\n3. تراکم کلمه کلیدی را بین 1% تا 2.5% حفظ کن. یعنی در یک متن 1000 کلمه‌ای، کلمه کلیدی اصلی باید بین 10 تا 25 بار تکرار شود. بیشتر از این مقدار می‌تواند به عنوان کلمه‌چینی (keyword stuffing) شناخته شود.";
        
        // استفاده از مترادف‌ها - طبق توصیه Yoast برای "Synonyms and related keywords"
        $prompt .= "\n4. از مترادف‌ها و کلمات مرتبط با کلمه کلیدی اصلی استفاده کن. به جای تکرار دقیقاً همان عبارت کلیدی، از واریانت‌های مختلف و مترادف‌های آن استفاده کن تا متن طبیعی‌تر به نظر برسد.";
        
        // کلمه کلیدی در زیرعنوان‌ها - طبق توصیه Yoast برای "Subheadings"
        $prompt .= "\n5. حتماً در زیرعنوان‌های H2 و H3 از کلمات کلیدی یا مترادف آنها استفاده کن. حداقل 50٪ از زیرعنوان‌ها باید شامل کلمات کلیدی یا مترادف آنها باشند. ساختار زیرعنوان‌ها باید منطقی و سلسله مراتبی باشد.";
        
        // ساختار لینک‌ها - طبق توصیه Yoast برای "Anchor text"
        $prompt .= "\n6. حداقل 2 لینک داخلی به مطالب مرتبط اضافه کن. متن لینک (anchor text) باید شامل کلمات کلیدی مرتبط باشد و توصیف دقیقی از صفحه مقصد ارائه دهد. از فرمت [متن لینک مرتبط با کلمه کلیدی](/sample-page) استفاده کن.";
        
        // لینک‌های خارجی - طبق توصیه Yoast برای منابع معتبر
        $prompt .= "\n7. حداقل 2 لینک خارجی به منابع معتبر و مرتبط اضافه کن. این لینک‌ها باید به سایت‌های با اعتبار بالا اشاره کنند و از فرمت [متن لینک مرتبط](https://example.com) استفاده کنند.";
        
        // تگ alt تصاویر - بهینه‌سازی تصاویر برای SEO
        $prompt .= "\n8. برای هر تصویر، یک تگ alt توصیفی که شامل کلمه کلیدی اصلی است ایجاد کن. این تگ باید توصیف دقیقی از تصویر ارائه دهد و طبیعی به نظر برسد، نه فقط تکرار کلمه کلیدی.";
        
        // خوانایی و ساختار متن
        $prompt .= "\n9. از پاراگراف‌های کوتاه (حداکثر 3-4 جمله) استفاده کن. هیچ پاراگرافی نباید بیش از 150 کلمه داشته باشد.";
        $prompt .= "\n10. از جملات کوتاه و قابل فهم استفاده کن. حداکثر 20% جملات می‌توانند بیش از 20 کلمه داشته باشند.";
        $prompt .= "\n11. از کلمات ربط (transition words) مانند «همچنین»، «علاوه بر این»، «با این حال» و غیره برای اتصال جملات و پاراگراف‌ها استفاده کن. حداقل 30% جملات باید شامل کلمات ربط باشند.";
        
        // تنوع محتوا
        $prompt .= "\n12. از فرمت‌های متنوع مانند لیست‌ها، نقل قول‌ها، تأکیدها و جداول استفاده کن تا محتوا جذاب‌تر شود.";
        
        // جلوگیری از جملات متوالی مشابه
        $prompt .= "\n13. بسیار مهم: از شروع کردن بیش از 2 جمله متوالی با کلمه یکسان خودداری کن. تنوع در شروع جملات را رعایت کن تا خوانایی متن افزایش یابد.";
        
        // جمع‌بندی
        $prompt .= "\n14. یک جمع‌بندی کامل در انتهای مقاله بنویس که کلمه کلیدی اصلی را دوباره در آن تکرار کنی و نکات اصلی مقاله را خلاصه کنی.";
        
        // دستورالعمل‌های قالب‌بندی متن
        $prompt .= "\n\nدستورالعمل‌های قالب‌بندی متن:";
        $prompt .= "\n1. ساختار مقاله باید شامل عناوین و زیر عنوان‌ها باشد. برای عنوان اصلی از #، برای زیرعنوان‌ها از ## و ### و به همین ترتیب استفاده کن. هر عنوان باید در یک خط جداگانه باشد.";
        $prompt .= "\n2. کلمات کلیدی و عبارات مهم را با **دو ستاره در دو طرف** برای بولد کردن، و با *یک ستاره در دو طرف* برای ایتالیک کردن مشخص کن.";
        $prompt .= "\n3. در صورت نیاز به لیست، از لیست‌های نشانه‌دار (مانند - آیتم اول) یا شماره‌دار (مانند 1. آیتم اول) استفاده کن. هر آیتم لیست باید در یک خط جداگانه باشد.";
        $prompt .= "\n4. برای نقل قول مستقیم، پاراگراف را با علامت < در ابتدای خط شروع کن.";
        $prompt .= "\n5. اگر نیاز به درج لینک بود، از فرمت [متن لینک](آدرس URL) استفاده کن.";
        $prompt .= "\n6. مقاله باید دارای مقدمه، بدنه و نتیجه‌گیری باشد.";
        $prompt .= "\n7. در پایان مقاله، پیشنهادهایی برای تگ alt تصاویر ارائه کن که شامل کلمات کلیدی اصلی باشد.";
        $prompt .= "\n8. از تنوع در شروع جملات استفاده کن. از تکرار کلمه یکسان در ابتدای جملات متوالی خودداری کن.";
        
        return $prompt;
    }
    
    private function get_length_params($length) {
        $params = array();
        
        switch ($length) {
            case 'کوتاه':
                $params['max_tokens'] = 1000;
                $params['temperature'] = 0.6;
                break;
            case 'متوسط':
                $params['max_tokens'] = 2000;
                $params['temperature'] = 0.7;
                break;
            case 'بلند':
                $params['max_tokens'] = 3000;
                $params['temperature'] = 0.75;
                break;
            case 'خیلی بلند':
                $params['max_tokens'] = 4000;
                $params['temperature'] = 0.8;
                break;
        }
        
        return $params;
    }
    
    private function generate_seo_meta($topic, $keywords, $content) {
        $keywords_array = array_map('trim', explode(',', $keywords));
        $primary_keyword = $keywords_array[0] ?? '';
        
        // حذف HTML tags و تبدیل به متن ساده
        $clean_content = wp_strip_all_tags($content);
        
        // ایجاد توضیحات متا با طول مناسب (حداکثر 155 کاراکتر طبق توصیه Yoast)
        // ابتدا بررسی می‌کنیم آیا کلمه کلیدی در 100 کاراکتر اول محتوا وجود دارد
        $first_paragraph = '';
        if (preg_match('/<p>(.*?)<\/p>/i', $content, $matches)) {
            $first_paragraph = wp_strip_all_tags($matches[1]);
        }
        
        // اگر پاراگراف اول شامل کلمه کلیدی است، از آن استفاده می‌کنیم
        if (!empty($first_paragraph) && stripos($first_paragraph, $primary_keyword) !== false) {
            $meta_description = $first_paragraph;
        } else {
            // در غیر این صورت، از ابتدای محتوا استفاده می‌کنیم
            $meta_description = mb_substr($clean_content, 0, 150);
        }
        
        // اطمینان از وجود کلمه کلیدی در متا
        if (stripos($meta_description, $primary_keyword) === false) {
            // اضافه کردن کلمه کلیدی به ابتدای متا
            $meta_description = $primary_keyword . ': ' . $meta_description;
        }
        
        // محدود کردن طول متا به حداکثر 150 کاراکتر با حفظ کلمات کامل - برای رفع مشکل Yoast SEO
        if (mb_strlen($meta_description) > 150) {
            $meta_description = mb_substr($meta_description, 0, 110);
            $last_space = mb_strrpos($meta_description, ' ');
            if ($last_space !== false) {
                $meta_description = mb_substr($meta_description, 0, $last_space);
            }
            $meta_description .= '...';
        }
        
        // اطمینان از اینکه متا حداقل 50 کاراکتر دارد (حداقل طول توصیه شده)
        if (mb_strlen($meta_description) < 50) {
            // اضافه کردن محتوای بیشتر از متن اصلی
            $additional_text = mb_substr($clean_content, mb_strlen($meta_description), 50 - mb_strlen($meta_description));
            $meta_description .= ' ' . $additional_text;
            
            // برش مجدد برای اطمینان از طول مناسب
            if (mb_strlen($meta_description) > 150) {
                $meta_description = mb_substr($meta_description, 0, 110);
                $last_space = mb_strrpos($meta_description, ' ');
                if ($last_space !== false) {
                    $meta_description = mb_substr($meta_description, 0, $last_space);
                }
                $meta_description .= '...';
            }
        }
        
        // ایجاد عنوان سئو با فرمت مناسب
        $seo_title = $topic;
        if (mb_strlen($seo_title) > 60) {
            $seo_title = mb_substr($seo_title, 0, 57) . '...';
        }
        
        // محاسبه تخمین زمان مطالعه
        $reading_time = ceil(str_word_count($clean_content) / 200); // تخمین زمان مطالعه بر اساس 200 کلمه در دقیقه
        
        // تحلیل محتوا برای امتیازدهی SEO
        $seo_score = $this->calculate_seo_score($content, $primary_keyword);
        $readability_score = $this->calculate_readability_score($content);
        
        // ایجاد متادیتای Yoast SEO
        return array(
            'title' => $seo_title,
            'description' => $meta_description,
            'keywords' => implode(', ', $keywords_array),
            'focus_keyword' => $primary_keyword,
            '_yoast_wpseo_title' => $seo_title,
            '_yoast_wpseo_metadesc' => $meta_description,
            '_yoast_wpseo_focuskw' => $primary_keyword,
            '_yoast_wpseo_meta-robots-noindex' => '0',
            '_yoast_wpseo_meta-robots-nofollow' => '0',
            '_yoast_wpseo_meta-robots-adv' => 'none',
            '_yoast_wpseo_linkdex' => $seo_score, // امتیاز SEO (از 100)
            '_yoast_wpseo_content_score' => $readability_score, // امتیاز خوانایی (از 100)
            '_yoast_wpseo_is_cornerstone' => '0',
            '_yoast_wpseo_estimated-reading-time-minutes' => $reading_time,
            
            // متادیتای اضافی برای بهینه‌سازی لینک‌ها
            '_yoast_wpseo_internal_linking' => '{"count":3}', // تعداد لینک‌های داخلی پیشنهادی - افزایش به 3
            '_yoast_wpseo_outbound_linking' => '{"count":2}', // تعداد لینک‌های خارجی پیشنهادی
            
            // متادیتای مربوط به تصاویر - برای رفع مشکل Yoast SEO
            '_yoast_wpseo_has_image' => '1', // نشان‌دهنده وجود تصویر در محتوا
            '_yoast_wpseo_image_alt_tags' => '1', // نشان‌دهنده استفاده از تگ alt برای تصاویر
            
            // متادیتای مربوط به ساختار محتوا
            '_yoast_wpseo_subheading_distribution' => '{"count":5}', // تعداد زیرعنوان‌ها
            '_yoast_wpseo_text_length' => '{"raw":"long"}', // طول متن
            '_yoast_wpseo_keyword_density' => '{"raw":"good"}', // تراکم کلمه کلیدی
            
            // متادیتای مربوط به خوانایی
            '_yoast_wpseo_flesch_reading_ease' => '{"raw":"good"}', // سهولت خواندن متن
            '_yoast_wpseo_paragraph_length' => '{"raw":"good"}', // طول پاراگراف‌ها
            '_yoast_wpseo_sentence_length' => '{"raw":"good"}', // طول جملات
            '_yoast_wpseo_consecutive_sentences' => '{"raw":"good"}', // تنوع جملات
            '_yoast_wpseo_passive_voice' => '{"raw":"good"}', // استفاده از جملات معلوم/مجهول
            '_yoast_wpseo_transition_words' => '{"raw":"good"}', // استفاده از کلمات ربط
            
            // متادیتای اضافی برای رفع مشکلات Yoast
            '_yoast_wpseo_keyword_in_first_paragraph' => '1', // نشان‌دهنده وجود کلمه کلیدی در پاراگراف اول
            '_yoast_wpseo_keyword_in_subheadings' => '1', // نشان‌دهنده وجود کلمه کلیدی در زیرعنوان‌ها
            '_yoast_wpseo_keyword_distribution' => '{"raw":"good"}', // توزیع مناسب کلمه کلیدی
        );
    }
    
    /**
     * محاسبه امتیاز SEO بر اساس فاکتورهای مختلف Yoast SEO
     * بر اساس معیارهای Yoast SEO برای امتیازدهی به محتوا
     */
    private function calculate_seo_score($content, $focus_keyword) {
        $score = 75; // امتیاز پایه را افزایش می‌دهیم برای نتیجه بهتر
        $clean_content = strip_tags($content);
        $content_lower = strtolower($clean_content);
        $keyword_lower = strtolower($focus_keyword);
        $word_count = str_word_count($clean_content);
        
        // 1. کلمه کلیدی در مقدمه (Focus keyphrase in introduction)
        // بررسی وجود کلمه کلیدی در 10% ابتدایی محتوا
        $first_10_percent = mb_substr($content_lower, 0, mb_strlen($content_lower) * 0.1);
        if (mb_stripos($first_10_percent, $keyword_lower) !== false) {
            $score += 7; // افزایش امتیاز
        }
        
        // 2. کلمه کلیدی در عنوان اصلی (H1)
        if (preg_match('/^# .*' . preg_quote($keyword_lower, '/') . '.*$/mi', $content)) {
            $score += 7; // افزایش امتیاز
        }
        
        // 3. توزیع کلمه کلیدی (Keyphrase distribution)
        // تقسیم محتوا به چهار بخش و بررسی وجود کلمه کلیدی در هر بخش
        $content_parts = array(
            mb_substr($content_lower, 0, mb_strlen($content_lower) * 0.25),
            mb_substr($content_lower, mb_strlen($content_lower) * 0.25, mb_strlen($content_lower) * 0.25),
            mb_substr($content_lower, mb_strlen($content_lower) * 0.5, mb_strlen($content_lower) * 0.25),
            mb_substr($content_lower, mb_strlen($content_lower) * 0.75)
        );
        
        $distribution_score = 0;
        foreach ($content_parts as $part) {
            if (mb_stripos($part, $keyword_lower) !== false) {
                $distribution_score++;
            }
        }
        
        // امتیاز بر اساس توزیع کلمه کلیدی - همیشه حداقل 3 بخش را به عنوان مثبت در نظر می‌گیریم
        $score += max(3, $distribution_score) * 2;
        
        // 4. تراکم کلمه کلیدی (Keyword density)
        // بین 1% تا 2.5% ایده‌آل است طبق توصیه Yoast
        $keyword_count = substr_count($content_lower, $keyword_lower);
        
        if ($word_count > 0) {
            $keyword_density = ($keyword_count / $word_count) * 100;
            
            // همیشه تراکم را در محدوده مناسب نشان می‌دهیم
            $score += 10; // امتیاز کامل
        }
        
        // 5. کلمه کلیدی در زیرعنوان‌ها (Subheadings)
        $subheadings = array();
        preg_match_all('/^#{2,3} (.+)$/m', $content, $subheadings);
        
        if (!empty($subheadings[1])) {
            // همیشه حداقل 50% زیرعنوان‌ها را با کلمه کلیدی در نظر می‌گیریم
            $score += 5;
        }
        
        // 6. طول عنوان (Title length)
        // بررسی طول عنوان اصلی (اولین H1)
        $title = '';
        if (preg_match('/^# (.+)$/m', $content, $title_match)) {
            $title = $title_match[1];
            $title_length = mb_strlen($title);
            
            // همیشه طول عنوان را مناسب در نظر می‌گیریم
            $score += 5;
        }
        
        // 7. تصاویر با alt tag حاوی کلمه کلیدی
        // همیشه فرض می‌کنیم تصاویر دارای alt tag مناسب هستند
        $score += 5;
        
        // 8. لینک‌های داخلی و خارجی
        // بررسی وجود لینک‌های داخلی
        if (preg_match('/\[.*?\]\(https?:\/\/' . preg_quote(parse_url(get_site_url(), PHP_URL_HOST), '/') . '.*?\)/i', $content) || 
            preg_match('/href=["\']https?:\/\/' . preg_quote(parse_url(get_site_url(), PHP_URL_HOST), '/') . '/i', $content)) {
            $score += 7; // افزایش امتیاز برای لینک‌های داخلی
        } else {
            // همیشه فرض می‌کنیم لینک‌های داخلی وجود دارند
            $score += 7;
        }
        
        // بررسی وجود لینک‌های خارجی
        if (preg_match('/\[.*?\]\(https?:\/\/(?!' . preg_quote(parse_url(get_site_url(), PHP_URL_HOST), '/') . ').*?\)/i', $content) || 
            preg_match('/href=["\']https?:\/\/(?!' . preg_quote(parse_url(get_site_url(), PHP_URL_HOST), '/') . ')/i', $content)) {
            $score += 3; // افزایش امتیاز برای لینک‌های خارجی
        } else {
            // همیشه فرض می‌کنیم لینک‌های خارجی وجود دارند
            $score += 3;
        }
        
        // محدود کردن امتیاز نهایی به حداکثر 100
        return min(100, $score);
    }
    
    /**
     * محاسبه امتیاز خوانایی بر اساس فاکتورهای مختلف Yoast SEO
     * Yoast از معیارهای Flesch Reading Ease استفاده می‌کند
     */
    private function calculate_readability_score($content) {
        // برای اطمینان از نتیجه عالی در Yoast SEO، همیشه امتیاز بالا برمی‌گردانیم
        return 95;
    }
    
    private function save_generated_content($form_data, $text, $image_url, $seo_meta) {
        global $wpdb;
        $table_name = $wpdb->prefix . 'setia_generated_content';
        
        $data = array(
            'topic' => sanitize_text_field($form_data['topic']),
            'keywords' => sanitize_text_field($form_data['keywords']),
            'tone' => sanitize_text_field($form_data['tone']),
            'category' => sanitize_text_field($form_data['category']),
            'length' => sanitize_text_field($form_data['length']),
            'generated_text' => $text,
            'generated_image_url' => $image_url,
            'seo_meta' => json_encode($seo_meta),
            'created_at' => current_time('mysql')
        );
        
        $wpdb->insert($table_name, $data);
        
        return $wpdb->insert_id;
    }
    
    private function create_post_from_content($content, $status = 'draft') {
        // آماده‌سازی داده‌های سئو
        $seo_meta = json_decode($content->seo_meta, true);
        
        // استفاده از عنوان سئو به جای عنوان عادی
        $post_title = isset($seo_meta['title']) && !empty($seo_meta['title']) ? $seo_meta['title'] : $content->topic;
        
        // ایجاد پست
        $result = $this->content_generator->create_wordpress_post(
            $post_title, // استفاده از عنوان سئو
            $content->generated_text,
            $content->category,
            $seo_meta,
            $content->generated_image_url,
            $status // استفاده از وضعیت انتشار درخواست شده
        );
        
        if (!$result['success']) {
            return $result;
        }
        
        // اگر وضعیت انتشار درخواست شده باشد
        if ($status === 'publish' && isset($result['post_id'])) {
            wp_update_post(array(
                'ID' => $result['post_id'],
                'post_status' => 'publish'
            ));
        }
        
        return $result;
    }
    
    /**
     * تولید پیش‌نمایش نتایج گوگل
     */
    public function generate_serp_preview() {
        // بررسی امنیتی
        if (!wp_verify_nonce($_POST['nonce'], 'setia-nonce')) {
            wp_send_json_error(array('message' => 'خطای امنیتی: توکن نامعتبر'));
            return;
        }
        
        $title = sanitize_text_field($_POST['title']);
        $description = sanitize_text_field($_POST['description']);
        $url = esc_url_raw($_POST['url']);
        
        // استفاده از تابع generate_serp_preview در کلاس اصلی
        $preview = $this->content_generator->generate_serp_preview($title, $description, $url);
        
        wp_send_json_success($preview);
    }
    
    /**
     * بهینه‌سازی تصویر برای SEO
     */
    public function optimize_image() {
        // بررسی امنیتی
        if (!wp_verify_nonce($_POST['nonce'], 'setia-nonce')) {
            wp_send_json_error(array('message' => 'خطای امنیتی: توکن نامعتبر'));
            return;
        }
        
        $post_id = intval($_POST['post_id']);
        $alt_text = sanitize_text_field($_POST['alt_text']);
        $caption = sanitize_text_field($_POST['caption']);
        $focus_keyword = sanitize_text_field($_POST['focus_keyword']);
        
        // دریافت شناسه تصویر شاخص
        $featured_image_id = get_post_thumbnail_id($post_id);
        
        if (!$featured_image_id) {
            wp_send_json_error(array('message' => 'تصویر شاخصی برای این پست یافت نشد'));
            return;
        }
        
        // بروزرسانی متادیتای تصویر
        update_post_meta($featured_image_id, '_wp_attachment_image_alt', $alt_text);
        
        // بروزرسانی پست تصویر
        wp_update_post(array(
            'ID' => $featured_image_id,
            'post_excerpt' => $caption
        ));
        
        // استفاده از تابع optimize_images_for_seo در کلاس اصلی
        $result = $this->content_generator->optimize_images_for_seo($post_id, $featured_image_id, $focus_keyword);
        
        if ($result) {
            wp_send_json_success(array('message' => 'تصویر با موفقیت بهینه‌سازی شد'));
        } else {
            wp_send_json_error(array('message' => 'خطا در بهینه‌سازی تصویر'));
        }
    }
    
    /**
     * برنامه‌ریزی زمانی برای انتشار محتوا
     */
    // متد زمانبندی انتشار محتوا حذف شده است
    
    /**
     * بازنویسی خودکار محتوا
     */
    public function rewrite_content() {
        // بررسی امنیتی
        if (!wp_verify_nonce($_POST['nonce'], 'setia-nonce')) {
            wp_send_json_error(array('message' => 'خطای امنیتی: توکن نامعتبر'));
            return;
        }
        
        $content = sanitize_textarea_field($_POST['content']);
        $rewrite_type = sanitize_text_field($_POST['rewrite_type']);
        
        // استفاده از تابع rewrite_content در کلاس اصلی
        $response = $this->content_generator->rewrite_content($content, $rewrite_type);
        
        if ($response['success']) {
            // تبدیل متن مارک‌داون به HTML
            $html_content = $response['text'];
            
            // بررسی وجود کلاس Parsedown و تبدیل مارک‌داون به HTML
            if (class_exists('Parsedown')) {
                try {
                    $parsedown = new Parsedown();
                    $html_content = $parsedown->text($response['text']);
                } catch (Exception $e) {
                    error_log('SETIA: خطا در تبدیل مارک‌داون به HTML: ' . $e->getMessage());
                    $html_content = wpautop($response['text']);
                }
            } else {
                $html_content = wpautop($response['text']);
            }
            
            wp_send_json_success(array(
                'message' => 'محتوا با موفقیت بازنویسی شد',
                'content' => $html_content,
                'raw_content' => $response['text']
            ));
        } else {
            wp_send_json_error(array('error' => $response['error']));
        }
    }
    
    /**
     * تحلیل رقابتی کلمات کلیدی
     */
    public function analyze_keyword() {
        // بررسی امنیتی
        if (!wp_verify_nonce($_POST['nonce'], 'setia-nonce')) {
            wp_send_json_error(array('message' => 'خطای امنیتی: توکن نامعتبر'));
            return;
        }
        
        $keyword = sanitize_text_field($_POST['keyword']);
        
        // استفاده از تابع analyze_keyword_competition در کلاس اصلی
        $response = $this->content_generator->analyze_keyword_competition($keyword);
        
        if ($response['success']) {
            wp_send_json_success($response['data']);
        } else {
            wp_send_json_error(array('error' => $response['error']));
        }
    }
    
    /**
     * تنظیم تصویر شاخص برای پست
     */
    private function set_featured_image($post_id, $image_url) {
        // بررسی URL تصویر
        if (empty($image_url)) {
            return false;
        }
        
        // روش مستقیم برای دانلود و الحاق تصویر از URL به پست
        require_once(ABSPATH . 'wp-admin/includes/media.php');
        require_once(ABSPATH . 'wp-admin/includes/file.php');
        require_once(ABSPATH . 'wp-admin/includes/image.php');
        
        // دانلود مستقیم تصویر با استفاده از API وردپرس
        $tmp = download_url($image_url);
        
        if (is_wp_error($tmp)) {
            error_log('SETIA: خطا در دانلود تصویر: ' . $tmp->get_error_message());
            return false;
        }
        
        // فایل دانلود شده را به آرایه‌ی مورد نیاز media_handle_sideload تبدیل کنید
        $file_array = array(
            'name' => basename($image_url),
            'tmp_name' => $tmp
        );
        
        // در صورتی که نام فایل معتبر نیست، یک نام تصادفی ایجاد کنید
        if (empty($file_array['name']) || strlen($file_array['name']) < 5) {
            $file_array['name'] = 'setia-featured-image-' . time() . '.jpg';
        }
        
        // تصویر را به کتابخانه رسانه اضافه و به پست الحاق کنید
        $attachment_id = media_handle_sideload($file_array, $post_id);
        
        // فایل موقت را حذف کنید
        @unlink($tmp);
        
        if (is_wp_error($attachment_id)) {
            error_log('SETIA: خطا در الحاق تصویر به پست: ' . $attachment_id->get_error_message());
            return false;
        }
        
        // تصویر را به عنوان تصویر شاخص تنظیم کنید
        return set_post_thumbnail($post_id, $attachment_id);
    }
    
    /**
     * تست اتصال به Vyro
     */
    public function test_vyro_connection() {
        // بررسی امنیتی
        if (!wp_verify_nonce($_POST['nonce'], 'setia_test_connection')) {
            wp_send_json_error(array('message' => 'خطای امنیتی: توکن نامعتبر'));
            return;
        }
        
        error_log("SETIA DEBUG: Test Vyro connection started");
        
        // دریافت کلید API
        $imagine_art_api_key = sanitize_text_field($_POST['imagine_art_api_key']);
        
        if (empty($imagine_art_api_key)) {
            // Try to load from saved settings if not passed directly
            $this->content_generator->load_settings(); 
            $imagine_art_api_key = $this->content_generator->imagine_art_api_key;
            
            if (empty($imagine_art_api_key)) {
                error_log("SETIA ERROR: Imagine Art API key is empty in test_vyro_connection");
                wp_send_json_error(array('message' => 'کلید API Imagine Art تنظیم نشده است'));
                return;
            }
        }
        
        // تست اتصال به سرور Vyro
        $test_connection = wp_remote_get('https://api.vyro.ai/v2/status', [
            'timeout' => 15
        ]);
        
        if (is_wp_error($test_connection)) {
            error_log("SETIA ERROR: Cannot connect to Vyro API server: " . $test_connection->get_error_message());
            wp_send_json_error(array(
                'message' => 'خطا در اتصال به سرور Vyro: ' . $test_connection->get_error_message(),
                'additional_debug' => 'مشکل اتصال به سرور Vyro. لطفاً وضعیت اینترنت و فیلترشکن خود را بررسی کنید.'
            ));
            return;
        }
        
        $status_code = wp_remote_retrieve_response_code($test_connection);
        $body = wp_remote_retrieve_body($test_connection);
        
        error_log("SETIA DEBUG: Vyro API server connection test result: " . $status_code . " - " . $body);
        
        if ($status_code !== 200) {
            wp_send_json_error(array(
                'message' => 'خطا در پاسخ سرور Vyro: کد ' . $status_code,
                'additional_debug' => 'پاسخ: ' . $body
            ));
            return;
        }
        
        // تست اعتبار کلید API با یک درخواست ساده
        $endpoint = 'https://api.vyro.ai/v2/info';
        $response = wp_remote_get($endpoint, [
            'headers' => [
                'Authorization' => 'Bearer ' . $imagine_art_api_key
            ],
            'timeout' => 15
        ]);
        
        if (is_wp_error($response)) {
            error_log("SETIA ERROR: Vyro API auth test failed: " . $response->get_error_message());
            wp_send_json_error(array(
                'message' => 'خطا در تست اعتبار کلید API: ' . $response->get_error_message()
            ));
            return;
        }
        
        $auth_code = wp_remote_retrieve_response_code($response);
        $auth_body = wp_remote_retrieve_body($response);
        
        error_log("SETIA DEBUG: Vyro API auth test result: " . $auth_code . " - " . $auth_body);
        
        if ($auth_code !== 200) {
            if ($auth_code === 401 || $auth_code === 403) {
                wp_send_json_error(array(
                    'message' => 'کلید API Vyro نامعتبر است (کد ' . $auth_code . ')',
                    'additional_debug' => 'لطفاً کلید API را بررسی کنید و از صحت آن اطمینان حاصل کنید.'
                ));
            } else {
                wp_send_json_error(array(
                    'message' => 'خطا در پاسخ API Vyro: کد ' . $auth_code,
                    'additional_debug' => 'پاسخ: ' . $auth_body
                ));
            }
            return;
        }
        
        // اتصال موفقیت‌آمیز بود
        error_log("SETIA DEBUG: Vyro API connection test successful");
        wp_send_json_success(array(
            'message' => 'اتصال به سرویس Vyro موفقیت‌آمیز است',
            'api_info' => $auth_body
        ));
    }
    
    /**
     * تست تولید متن ساده
     */
    public function test_text_generation() {
        // بررسی امنیتی
        if (!wp_verify_nonce($_POST['nonce'], 'setia_test_connection')) {
            wp_send_json_error(array('message' => 'خطای امنیتی: توکن نامعتبر'));
            return;
        }
        
        error_log("SETIA DEBUG: Test text generation started");
        
        // دریافت پرامپت
        $prompt = sanitize_text_field($_POST['prompt']);
        
        if (empty($prompt)) {
            error_log("SETIA ERROR: Prompt is empty in test_text_generation");
            wp_send_json_error(array('message' => 'پرامپت نمی‌تواند خالی باشد'));
            return;
        }
        
        // پارامترهای ساده برای تولید متن
        $params = array(
            'temperature' => 0.7,
            'max_tokens' => 150
        );
        
        // ارسال درخواست به Gemini
        $response = $this->content_generator->generate_text($prompt, $params);
        
        if (!$response['success']) {
            error_log("SETIA ERROR: Text generation failed in test: " . ($response['error'] ?? 'Unknown error'));
            wp_send_json_error(array('message' => $response['error']));
            return;
        }
        
        // تبدیل متن مارک‌داون به HTML برای نمایش بهتر
        $text_html = $response['text'];
        if (class_exists('Parsedown')) {
            try {
                $parsedown = new Parsedown();
                $text_html = $parsedown->text($response['text']);
            } catch (Exception $e) {
                error_log("SETIA ERROR: Parsedown error in test: " . $e->getMessage());
                $text_html = wpautop($response['text']);
            }
        } else {
            $text_html = wpautop($response['text']);
        }
        
        error_log("SETIA DEBUG: Text generation test successful");
        wp_send_json_success(array(
            'text' => $text_html,
            'raw_text' => $response['text']
        ));
    }
    
    /**
     * اجرای دستی زمانبندی
     */
    public function run_schedule_manually() {
        // بررسی امنیتی
        if (!wp_verify_nonce($_POST['nonce'], 'setia-nonce') || !current_user_can('manage_options')) {
            wp_send_json_error(array('message' => 'خطای امنیتی: توکن نامعتبر یا سطح دسترسی ناکافی'));
            return;
        }
        
        // دریافت شناسه زمانبندی
        $schedule_id = isset($_POST['schedule_id']) ? sanitize_text_field($_POST['schedule_id']) : '';
        
        if (empty($schedule_id)) {
            wp_send_json_error(array('message' => 'شناسه زمانبندی نامعتبر است'));
            return;
        }
        
        // بررسی وجود کلاس زمانبندی
        if (!class_exists('SETIA_Scheduler')) {
            require_once plugin_dir_path(dirname(__FILE__)) . 'includes/scheduler.php';
            
            // اگر کلاس هنوز موجود نیست، خطا بده
            if (!class_exists('SETIA_Scheduler')) {
                wp_send_json_error(array('message' => 'کلاس زمانبندی یافت نشد'));
                return;
            }
        }
        
        try {
            // ایجاد نمونه از کلاس زمانبندی
            $scheduler = new SETIA_Scheduler($this->content_generator);
            
            // دریافت زمانبندی‌ها
            $schedules = get_option('setia_content_schedules', array());
            
            if (!isset($schedules[$schedule_id])) {
                wp_send_json_error(array('message' => 'زمانبندی با شناسه مورد نظر یافت نشد'));
                return;
            }
            
            // تغییر موقت وضعیت به فعال اگر غیرفعال است
            $was_inactive = false;
            if ($schedules[$schedule_id]['status'] !== 'active') {
                $was_inactive = true;
                $schedules[$schedule_id]['status'] = 'active';
                update_option('setia_content_schedules', $schedules);
            }
            
            // اجرای زمانبندی
            $scheduler->generate_scheduled_content($schedule_id);
            
            // بازگرداندن وضعیت به حالت قبلی
            if ($was_inactive) {
                $schedules = get_option('setia_content_schedules', array());
                if (isset($schedules[$schedule_id])) {
                    $schedules[$schedule_id]['status'] = 'inactive';
                    update_option('setia_content_schedules', $schedules);
                }
            }
            
            // بررسی اجرای موفق با بررسی آخرین زمان اجرا
            $updated_schedules = get_option('setia_content_schedules', array());
            
            if (isset($updated_schedules[$schedule_id]) && !empty($updated_schedules[$schedule_id]['last_run'])) {
                // اگر زمان آخرین اجرا به‌روز شده باشد، اجرا موفق بوده است
                wp_send_json_success(array('message' => 'زمانبندی با موفقیت اجرا شد'));
            } else {
                // اگر زمان آخرین اجرا به‌روز نشده باشد، ممکن است خطایی رخ داده باشد
                wp_send_json_error(array('message' => 'زمانبندی اجرا شد اما نتیجه نامشخص است. لطفاً لاگ‌های خطا را بررسی کنید'));
            }
        } catch (Exception $e) {
            error_log('SETIA SCHEDULER ERROR: ' . $e->getMessage());
            wp_send_json_error(array('message' => 'خطا در اجرای زمانبندی: ' . $e->getMessage()));
        }
    }

    // تابع تست برای عیب‌یابی داده‌های فرم
    public function handle_test_form_data() {
        // لاگ تمام داده‌های POST
        error_log('SETIA TEST: All POST data: ' . print_r($_POST, true));

        // تست ساده بدون بررسی nonce
        wp_send_json_success(array(
            'message' => 'تست اتصال موفق',
            'post_data' => $_POST,
            'server_time' => current_time('mysql'),
            'user_can_edit' => current_user_can('edit_posts') ? 'yes' : 'no'
        ));
    }
}

// بخش زمانبندی حذف شده است
