<?php
/**
 * کلاس تولید محتوا
 * 
 * @package SETIA
 */

if (!defined('ABSPATH')) {
    exit;
}

class SETIA_AI_Generator {
    private $logger;
    
    /**
     * مقداردهی اولیه کلاس
     */
    public function __construct() {
        try {
            // راه‌اندازی لاگر
            if (class_exists('SETIA_Logger')) {
                $this->logger = SETIA_Logger::get_instance();
                $this->logger->log('Content Generator initialized', 'info');
            } else {
                error_log('SETIA Error: Logger class not found in content generator!');
            }
            
            // تنظیم اکشن‌های AJAX
            add_action('wp_ajax_setia_generate_content', array($this, 'ajax_generate_content'));
        } catch (Exception $e) {
            error_log('SETIA Error initializing content generator: ' . $e->getMessage());
        }
    }
    
    /**
     * تولید محتوا
     * 
     * @param array $params پارامترهای تولید محتوا
     * @return array|bool آرایه‌ای از محتوا یا false در صورت بروز خطا
     */
    public function generate_content($params) {
        try {
            $this->log('Starting content generation with parameters', 'info', $params);
            
            // بررسی پارامترهای ورودی
            if (!isset($params['title']) || empty($params['title'])) {
                throw new Exception('عنوان محتوا مشخص نشده است');
            }
            
            // در حالت آزمایشی، یک محتوای نمونه ایجاد می‌کنیم
            $title = $params['title'];
            $instructions = isset($params['instructions']) ? $params['instructions'] : 'محتوای پیش‌فرض';
            
            // ایجاد محتوای نمونه
            $content = array(
                'title' => $title,
                'content' => '<h2>محتوای نمونه</h2><p>این یک محتوای نمونه است که به صورت خودکار تولید شده است.</p><p>' . $instructions . '</p>',
                'meta' => array(
                    'setia_generated' => true,
                    'setia_params' => json_encode($params)
                )
            );
            
            $this->log('Content generated successfully', 'info');
            return $content;
            
        } catch (Exception $e) {
            $this->log('Error in content generation', 'error', $e->getMessage());
            return false;
        }
    }
    
    /**
     * پاسخ به درخواست AJAX برای تولید محتوا
     */
    public function ajax_generate_content() {
        // بررسی امنیت
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'setia_nonce')) {
            wp_send_json_error(array('message' => 'خطای امنیتی: نانس نامعتبر است'));
            return;
        }
        
        // بررسی دسترسی
        if (!current_user_can('manage_options')) {
            wp_send_json_error(array('message' => 'شما دسترسی لازم برای این عملیات را ندارید'));
            return;
        }
        
        // پاکسازی و دریافت پارامترها
        $title = isset($_POST['title']) ? sanitize_text_field($_POST['title']) : '';
        $instructions = isset($_POST['instructions']) ? sanitize_textarea_field($_POST['instructions']) : '';
        
        if (empty($title)) {
            wp_send_json_error(array('message' => 'عنوان نمی‌تواند خالی باشد'));
            return;
        }
        
        // تولید محتوا
        $params = array(
            'title' => $title,
            'instructions' => $instructions
        );
        
        $content = $this->generate_content($params);
        
        if ($content === false) {
            wp_send_json_error(array('message' => 'خطا در تولید محتوا'));
            return;
        }
        
        wp_send_json_success(array(
            'title' => $content['title'],
            'content' => $content['content'],
            'message' => 'محتوا با موفقیت تولید شد'
        ));
    }
    
    /**
     * ثبت پیام در لاگ
     */
    private function log($message, $type = 'info', $data = null) {
        if (isset($this->logger)) {
            try {
                $this->logger->log($message, $type, $data);
            } catch (Exception $e) {
                error_log("SETIA: {$message}");
                if ($data) {
                    error_log("SETIA Data: " . print_r($data, true));
                }
            }
        } else {
            error_log("SETIA: {$message}");
            if ($data) {
                error_log("SETIA Data: " . print_r($data, true));
            }
        }
    }
} 