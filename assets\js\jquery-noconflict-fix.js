/**
 * اسکریپت رفع تداخل jQuery
 * این اسکریپت از تداخل بین نسخه‌های مختلف jQuery جلوگیری می‌کند
 * 
 * @package SETIA
 */

(function() {
    // ذخیره نسخه اصلی jQuery
    if (typeof jQuery !== 'undefined') {
        // ذخیره jQuery اصلی در متغیر جهانی
        window.setiaJQuery = jQuery.noConflict(true);
        
        // ثبت لاگ برای اطلاع از نسخه jQuery
        console.log("jQuery در حالت noConflict تنظیم شد - نسخه: " + window.setiaJQuery.fn.jquery);
        
        // بازگرداندن jQuery به window برای سازگاری با اسکریپت‌های قدیمی
        window.jQuery = window.setiaJQuery;
        window.$ = window.setiaJQuery;
        
        console.log("jQuery با موفقیت بازیابی شد و در دسترس است.");
    } else {
        console.error("خطا: jQuery در دسترس نیست!");
    }
})(); 