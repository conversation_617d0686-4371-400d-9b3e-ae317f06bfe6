<?php
/**
 * صفحه زمانبندی تولید محتوا
 * SETIA Content Generator Plugin
 */

// جلوگیری از دسترسی مستقیم
if (!defined('ABSPATH')) {
    exit;
}
?>

<div class="wrap setia-scheduler-wrap">
    <div class="setia-header">
        <h1 class="setia-title">
            <span class="setia-icon">⏰</span>
            زمانبندی تولید محتوا
        </h1>
        <p class="setia-subtitle">برنامه‌ریزی و زمانبندی خودکار تولید محتوا با هوش مصنوعی</p>
    </div>

    <div class="setia-main-container">
        <!-- تنظیمات کلی زمانبندی -->
        <div class="setia-card">
            <div class="setia-card-header">
                <h3>تنظیمات کلی زمانبندی</h3>
                <div class="setia-toggle-switch">
                    <input type="checkbox" id="auto-schedule" class="setia-toggle-input" <?php checked(get_option('setia_auto_schedule', 0), 1); ?>>
                    <label for="auto-schedule" class="setia-toggle-label">
                        <span class="setia-toggle-slider"></span>
                    </label>
                    <span class="setia-toggle-text">فعال‌سازی زمانبندی خودکار</span>
                </div>
            </div>
            
            <div class="setia-card-body">
                <div class="setia-form-grid">
                    <div class="setia-form-group">
                        <label for="schedule-frequency">تناوب تولید محتوا:</label>
                        <select id="schedule-frequency" class="setia-select">
                            <option value="hourly" <?php selected(get_option('setia_schedule_frequency', 'daily'), 'hourly'); ?>>هر ساعت</option>
                            <option value="daily" <?php selected(get_option('setia_schedule_frequency', 'daily'), 'daily'); ?>>روزانه</option>
                            <option value="weekly" <?php selected(get_option('setia_schedule_frequency', 'daily'), 'weekly'); ?>>هفتگی</option>
                            <option value="monthly" <?php selected(get_option('setia_schedule_frequency', 'daily'), 'monthly'); ?>>ماهانه</option>
                        </select>
                    </div>

                    <div class="setia-form-group">
                        <label for="content-count">تعداد محتوا در هر بار:</label>
                        <input type="number" id="content-count" class="setia-input" value="<?php echo esc_attr(get_option('setia_content_count', 1)); ?>" min="1" max="10">
                    </div>

                    <div class="setia-form-group">
                        <label for="publish-time">زمان انتشار:</label>
                        <input type="time" id="publish-time" class="setia-input" value="<?php echo esc_attr(get_option('setia_publish_time', '09:00')); ?>">
                    </div>
                    
                    <div class="setia-form-group">
                        <label for="content-type">نوع محتوا:</label>
                        <select id="content-type" class="setia-select">
                            <option value="article">مقاله</option>
                            <option value="product">محصول WooCommerce</option>
                            <option value="mixed">ترکیبی</option>
                        </select>
                    </div>

                    <div class="setia-form-group">
                        <div class="setia-toggle-switch">
                            <input type="checkbox" id="auto-publish" class="setia-toggle-input" <?php checked(get_option('setia_auto_publish', 0), 1); ?>>
                            <label for="auto-publish" class="setia-toggle-label">
                                <span class="setia-toggle-slider"></span>
                            </label>
                            <span class="setia-toggle-text">انتشار خودکار محتوا</span>
                        </div>
                    </div>

                    <div class="setia-form-group">
                        <div class="setia-toggle-switch">
                            <input type="checkbox" id="generate-images" class="setia-toggle-input" <?php checked(get_option('setia_generate_images', 1), 1); ?>>
                            <label for="generate-images" class="setia-toggle-label">
                                <span class="setia-toggle-slider"></span>
                            </label>
                            <span class="setia-toggle-text">تولید تصاویر خودکار</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- برنامه‌های زمانبندی شده -->
        <div class="setia-card">
            <div class="setia-card-header">
                <h3>برنامه‌های زمانبندی شده</h3>
                <button id="add-schedule" class="setia-button setia-button-primary">
                    <span class="dashicons dashicons-plus-alt"></span>
                    افزودن برنامه جدید
                </button>
            </div>
            
            <div class="setia-schedules-list" id="schedules-list">
                <!-- لیست برنامه‌ها از طریق JavaScript بارگذاری می‌شود -->
                <div style="text-align: center; padding: 40px; color: #666;">
                    <span class="dashicons dashicons-update-alt" style="font-size: 24px; animation: spin 1s linear infinite;"></span>
                    <p style="margin-top: 10px;">در حال بارگذاری برنامه‌ها...</p>
                </div>
            </div>
        </div>

        <!-- آمار زمانبندی -->
        <div class="setia-stats-grid">
            <div class="setia-stat-card">
                <div class="setia-stat-icon">📅</div>
                <div class="setia-stat-content">
                    <h4>برنامه‌های فعال</h4>
                    <span class="setia-stat-number">2</span>
                </div>
            </div>
            
            <div class="setia-stat-card">
                <div class="setia-stat-icon">✅</div>
                <div class="setia-stat-content">
                    <h4>اجرای موفق امروز</h4>
                    <span class="setia-stat-number">1</span>
                </div>
            </div>
            
            <div class="setia-stat-card">
                <div class="setia-stat-icon">⏳</div>
                <div class="setia-stat-content">
                    <h4>برنامه بعدی</h4>
                    <span class="setia-stat-number">2 ساعت</span>
                </div>
            </div>
            
            <div class="setia-stat-card">
                <div class="setia-stat-icon">📊</div>
                <div class="setia-stat-content">
                    <h4>کل محتوای تولید شده</h4>
                    <span class="setia-stat-number">45</span>
                </div>
            </div>
        </div>

        <!-- لاگ اجرا -->
        <div class="setia-card">
            <div class="setia-card-header">
                <h3>لاگ اجرای زمانبندی</h3>
                <button id="clear-logs" class="setia-button setia-button-outline">پاک کردن لاگ‌ها</button>
            </div>
            
            <div class="setia-logs-container">
                <!-- لاگ‌ها از طریق JavaScript بارگذاری می‌شوند -->
                <div style="text-align: center; padding: 40px; color: #666;">
                    <span class="dashicons dashicons-update-alt" style="font-size: 24px; animation: spin 1s linear infinite;"></span>
                    <p style="margin-top: 10px;">در حال بارگذاری لاگ‌ها...</p>
                </div>
            </div>
        </div>

        <!-- دکمه‌های عملیات -->
        <div class="setia-actions-bar">
            <button id="save-scheduler-settings" class="setia-button setia-button-primary setia-button-large">
                <span class="dashicons dashicons-yes"></span>
                ذخیره تنظیمات
            </button>
            
            <button id="test-scheduler" class="setia-button setia-button-secondary setia-button-large">
                <span class="dashicons dashicons-admin-tools"></span>
                تست زمانبندی
            </button>
            
            <button id="run-now" class="setia-button setia-button-outline setia-button-large">
                <span class="dashicons dashicons-controls-play"></span>
                اجرای فوری
            </button>
        </div>
    </div>
</div>

<!-- مودال افزودن برنامه جدید -->
<div id="add-schedule-modal" class="setia-modal" style="display: none;">
    <div class="setia-modal-content">
        <div class="setia-modal-header">
            <h3>افزودن برنامه زمانبندی جدید</h3>
            <span class="setia-modal-close">&times;</span>
        </div>
        <div class="setia-modal-body">
            <form id="schedule-form">
                <div class="setia-form-group">
                    <label for="schedule-title">عنوان برنامه:</label>
                    <input type="text" id="schedule-title" class="setia-input" placeholder="مثال: تولید مقاله روزانه" required>
                </div>

                <div class="setia-form-group">
                    <label for="schedule-topic">موضوع محتوا:</label>
                    <input type="text" id="schedule-topic" class="setia-input" placeholder="مثال: تکنولوژی، سلامت، ورزش" required>
                </div>

                <div class="setia-form-group">
                    <label for="schedule-keywords">کلمات کلیدی:</label>
                    <input type="text" id="schedule-keywords" class="setia-input" placeholder="کلمات کلیدی را با کاما جدا کنید">
                </div>

                <div class="setia-form-group">
                    <label for="schedule-frequency-modal">تناوب:</label>
                    <select id="schedule-frequency-modal" class="setia-select">
                        <option value="hourly">هر ساعت</option>
                        <option value="daily">روزانه</option>
                        <option value="weekly">هفتگی</option>
                        <option value="monthly">ماهانه</option>
                    </select>
                </div>

                <div class="setia-form-group">
                    <label for="schedule-time">زمان اجرا:</label>
                    <input type="time" id="schedule-time" class="setia-input" value="09:00">
                </div>
                
                <div class="setia-modal-actions">
                    <button type="submit" class="setia-button setia-button-primary">ایجاد برنامه</button>
                    <button type="button" class="setia-button setia-button-secondary setia-modal-close">انصراف</button>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
.setia-scheduler-wrap {
    direction: rtl;
    font-family: 'IRANSans', Tahoma, sans-serif;
}

.setia-form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.setia-toggle-switch {
    display: flex;
    align-items: center;
    gap: 10px;
}

.setia-toggle-input {
    display: none;
}

.setia-toggle-label {
    position: relative;
    width: 50px;
    height: 24px;
    background: #ccc;
    border-radius: 12px;
    cursor: pointer;
    transition: background 0.3s;
}

.setia-toggle-slider {
    position: absolute;
    top: 2px;
    right: 2px;
    width: 20px;
    height: 20px;
    background: white;
    border-radius: 50%;
    transition: transform 0.3s;
}

.setia-toggle-input:checked + .setia-toggle-label {
    background: #667eea;
}

.setia-toggle-input:checked + .setia-toggle-label .setia-toggle-slider {
    transform: translateX(-26px);
}

.setia-schedules-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.setia-schedule-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
    border-right: 4px solid #667eea;
}

.setia-schedule-info h4 {
    margin: 0 0 5px 0;
    color: #2c3e50;
}

.setia-schedule-info p {
    margin: 0 0 10px 0;
    color: #666;
}

.setia-schedule-status {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
}

.setia-schedule-status.active {
    background: #d4edda;
    color: #155724;
}

.setia-schedule-status.inactive {
    background: #f8d7da;
    color: #721c24;
}

.setia-schedule-actions {
    display: flex;
    gap: 10px;
}

.setia-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin: 20px 0;
}

.setia-stat-card {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.setia-stat-icon {
    font-size: 2em;
}

.setia-stat-content h4 {
    margin: 0 0 5px 0;
    font-size: 14px;
    opacity: 0.9;
}

.setia-stat-number {
    font-size: 24px;
    font-weight: bold;
}

.setia-logs-container {
    max-height: 300px;
    overflow-y: auto;
}

.setia-log-item {
    display: grid;
    grid-template-columns: 150px 1fr 80px;
    gap: 15px;
    padding: 15px;
    border-bottom: 1px solid #e1e8ed;
    align-items: center;
}

.setia-log-item.success {
    border-right: 4px solid #28a745;
}

.setia-log-item.error {
    border-right: 4px solid #dc3545;
}

.setia-log-time {
    font-size: 12px;
    color: #666;
    font-weight: 600;
}

.setia-log-message {
    color: #2c3e50;
}

.setia-log-status {
    text-align: center;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 600;
}

.setia-log-item.success .setia-log-status {
    background: #d4edda;
    color: #155724;
}

.setia-log-item.error .setia-log-status {
    background: #f8d7da;
    color: #721c24;
}

.setia-actions-bar {
    display: flex;
    gap: 15px;
    justify-content: center;
    margin-top: 30px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
}

.setia-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    z-index: 10000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.setia-modal-content {
    background: white;
    border-radius: 12px;
    max-width: 500px;
    width: 90%;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
}

.setia-modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 12px 12px 0 0;
}

.setia-modal-header h3 {
    margin: 0;
}

.setia-modal-close {
    font-size: 24px;
    cursor: pointer;
    opacity: 0.8;
    transition: opacity 0.3s;
}

.setia-modal-close:hover {
    opacity: 1;
}

.setia-modal-body {
    padding: 20px;
}

.setia-modal-actions {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
    margin-top: 20px;
}
</style>

<script>
jQuery(document).ready(function($) {
    // متغیرهای سراسری
    var ajaxUrl = '<?php echo admin_url('admin-ajax.php'); ?>';
    var nonce = '<?php echo wp_create_nonce('setia-nonce'); ?>';

    // تابع نمایش پیام
    function showMessage(message, type = 'success') {
        var messageClass = type === 'success' ? 'notice-success' : 'notice-error';
        var messageHtml = '<div class="notice ' + messageClass + ' is-dismissible"><p>' + message + '</p></div>';

        // حذف پیام‌های قبلی
        $('.notice').remove();

        // اضافه کردن پیام جدید
        $('.setia-scheduler-wrap').prepend(messageHtml);

        // حذف خودکار پیام بعد از 5 ثانیه
        setTimeout(function() {
            $('.notice').fadeOut();
        }, 5000);
    }

    // تابع نمایش لودینگ
    function showLoading(button) {
        button.prop('disabled', true);
        button.find('span.dashicons').removeClass().addClass('dashicons dashicons-update-alt');
        button.find('span.dashicons').css('animation', 'spin 1s linear infinite');
    }

    // تابع مخفی کردن لودینگ
    function hideLoading(button, originalIcon) {
        button.prop('disabled', false);
        button.find('span.dashicons').removeClass().addClass('dashicons ' + originalIcon);
        button.find('span.dashicons').css('animation', 'none');
    }

    // رویداد باز کردن مودال
    $('#add-schedule').on('click', function() {
        $('#add-schedule-modal').show();
    });

    // رویداد بستن مودال
    $('.setia-modal-close').on('click', function() {
        $('.setia-modal').hide();
    });

    // رویداد ذخیره تنظیمات زمانبندی
    $('#save-scheduler-settings').on('click', function() {
        var button = $(this);
        var originalIcon = 'dashicons-yes';

        showLoading(button);

        // جمع‌آوری داده‌های فرم
        var formData = {
            action: 'setia_save_scheduler_settings',
            nonce: nonce,
            auto_schedule: $('#auto-schedule').is(':checked') ? 1 : 0,
            schedule_frequency: $('#schedule-frequency').val(),
            content_count: $('#content-count').val(),
            publish_time: $('#publish-time').val(),
            auto_publish: $('#auto-publish').is(':checked') ? 1 : 0,
            generate_images: $('#generate-images').is(':checked') ? 1 : 0
        };

        $.ajax({
            url: ajaxUrl,
            type: 'POST',
            data: formData,
            success: function(response) {
                hideLoading(button, originalIcon);
                if (response.success) {
                    showMessage('تنظیمات زمانبندی با موفقیت ذخیره شد.', 'success');
                } else {
                    showMessage('خطا در ذخیره تنظیمات: ' + (response.data.message || 'خطای نامشخص'), 'error');
                }
            },
            error: function() {
                hideLoading(button, originalIcon);
                showMessage('خطا در ارتباط با سرور. لطفاً دوباره تلاش کنید.', 'error');
            }
        });
    });

    // رویداد تست زمانبندی
    $('#test-scheduler').on('click', function() {
        var button = $(this);
        var originalIcon = 'dashicons-admin-tools';

        showLoading(button);

        $.ajax({
            url: ajaxUrl,
            type: 'POST',
            data: {
                action: 'setia_test_scheduler',
                nonce: nonce
            },
            success: function(response) {
                hideLoading(button, originalIcon);
                if (response.success) {
                    showMessage('تست زمانبندی با موفقیت انجام شد. ' + (response.data.message || ''), 'success');
                } else {
                    showMessage('خطا در تست زمانبندی: ' + (response.data.message || 'خطای نامشخص'), 'error');
                }
            },
            error: function() {
                hideLoading(button, originalIcon);
                showMessage('خطا در ارتباط با سرور. لطفاً دوباره تلاش کنید.', 'error');
            }
        });
    });

    // رویداد اجرای فوری
    $('#run-now').on('click', function() {
        if (!confirm('آیا مطمئن هستید که می‌خواهید زمانبندی را الان اجرا کنید؟')) {
            return;
        }

        var button = $(this);
        var originalIcon = 'dashicons-controls-play';

        showLoading(button);

        $.ajax({
            url: ajaxUrl,
            type: 'POST',
            data: {
                action: 'setia_run_scheduler_now',
                nonce: nonce
            },
            success: function(response) {
                hideLoading(button, originalIcon);
                if (response.success) {
                    showMessage('زمانبندی با موفقیت اجرا شد. ' + (response.data.message || ''), 'success');
                    // بارگذاری مجدد لاگ‌ها
                    loadSchedulerLogs();
                } else {
                    showMessage('خطا در اجرای زمانبندی: ' + (response.data.message || 'خطای نامشخص'), 'error');
                }
            },
            error: function() {
                hideLoading(button, originalIcon);
                showMessage('خطا در ارتباط با سرور. لطفاً دوباره تلاش کنید.', 'error');
            }
        });
    });

    // رویداد ارسال فرم ایجاد برنامه جدید
    $('#schedule-form').on('submit', function(e) {
        e.preventDefault();

        var form = $(this);
        var submitButton = form.find('button[type="submit"]');

        // غیرفعال کردن دکمه ارسال
        submitButton.prop('disabled', true).text('در حال ایجاد...');

        // جمع‌آوری داده‌های فرم
        var formData = {
            action: 'setia_save_schedule',
            nonce: nonce,
            title: $('#schedule-title').val(),
            topic: $('#schedule-topic').val(),
            keywords: $('#schedule-keywords').val(),
            frequency: $('#schedule-frequency-modal').val(),
            time: $('#schedule-time').val(),
            status: 'active',
            schedule_id: 0 // برای ایجاد برنامه جدید
        };

        $.ajax({
            url: ajaxUrl,
            type: 'POST',
            data: formData,
            success: function(response) {
                submitButton.prop('disabled', false).text('ایجاد برنامه');

                if (response.success) {
                    showMessage('برنامه جدید با موفقیت ایجاد شد.', 'success');
                    $('#add-schedule-modal').hide();
                    form[0].reset();
                    // بارگذاری مجدد لیست برنامه‌ها
                    loadSchedulesList();
                } else {
                    showMessage('خطا در ایجاد برنامه: ' + (response.data.message || 'خطای نامشخص'), 'error');
                }
            },
            error: function() {
                submitButton.prop('disabled', false).text('ایجاد برنامه');
                showMessage('خطا در ارتباط با سرور. لطفاً دوباره تلاش کنید.', 'error');
            }
        });
    });

    // رویداد پاک کردن لاگ‌ها
    $('#clear-logs').on('click', function() {
        if (!confirm('آیا مطمئن هستید که می‌خواهید لاگ‌ها را پاک کنید؟')) {
            return;
        }

        var button = $(this);
        button.prop('disabled', true).text('در حال پاک کردن...');

        $.ajax({
            url: ajaxUrl,
            type: 'POST',
            data: {
                action: 'setia_clear_scheduler_logs',
                nonce: nonce
            },
            success: function(response) {
                button.prop('disabled', false).text('پاک کردن لاگ‌ها');

                if (response.success) {
                    $('.setia-logs-container').html('<p style="text-align: center; padding: 20px; color: #666;">لاگ‌ها پاک شدند.</p>');
                    showMessage('لاگ‌ها با موفقیت پاک شدند.', 'success');
                } else {
                    showMessage('خطا در پاک کردن لاگ‌ها: ' + (response.data.message || 'خطای نامشخص'), 'error');
                }
            },
            error: function() {
                button.prop('disabled', false).text('پاک کردن لاگ‌ها');
                showMessage('خطا در ارتباط با سرور. لطفاً دوباره تلاش کنید.', 'error');
            }
        });
    });

    // تابع بارگذاری لیست برنامه‌ها
    function loadSchedulesList() {
        $.ajax({
            url: ajaxUrl,
            type: 'POST',
            data: {
                action: 'setia_get_schedules',
                nonce: nonce
            },
            success: function(response) {
                if (response.success && response.data.schedules) {
                    updateSchedulesList(response.data.schedules);
                }
            }
        });
    }

    // تابع به‌روزرسانی لیست برنامه‌ها
    function updateSchedulesList(schedules) {
        var container = $('#schedules-list');
        container.empty();

        if (schedules.length === 0) {
            container.html('<p style="text-align: center; padding: 20px; color: #666;">هیچ برنامه‌ای تعریف نشده است.</p>');
            return;
        }

        schedules.forEach(function(schedule) {
            var statusClass = schedule.status === 'active' ? 'active' : 'inactive';
            var statusText = schedule.status === 'active' ? 'فعال' : 'غیرفعال';

            var scheduleHtml = '<div class="setia-schedule-item">' +
                '<div class="setia-schedule-info">' +
                '<h4>' + schedule.title + '</h4>' +
                '<p>' + schedule.topic + ' - ' + schedule.frequency + '</p>' +
                '<span class="setia-schedule-status ' + statusClass + '">' + statusText + '</span>' +
                '</div>' +
                '<div class="setia-schedule-actions">' +
                '<button class="setia-button setia-button-small setia-button-outline edit-schedule" data-id="' + schedule.id + '">ویرایش</button>' +
                '<button class="setia-button setia-button-small setia-button-danger delete-schedule" data-id="' + schedule.id + '">حذف</button>' +
                '</div>' +
                '</div>';

            container.append(scheduleHtml);
        });
    }

    // تابع بارگذاری لاگ‌های زمانبندی
    function loadSchedulerLogs() {
        $.ajax({
            url: ajaxUrl,
            type: 'POST',
            data: {
                action: 'setia_get_scheduler_logs',
                nonce: nonce
            },
            success: function(response) {
                if (response.success && response.data.logs) {
                    updateSchedulerLogs(response.data.logs);
                }
            }
        });
    }

    // تابع به‌روزرسانی لاگ‌ها
    function updateSchedulerLogs(logs) {
        var container = $('.setia-logs-container');
        container.empty();

        if (logs.length === 0) {
            container.html('<p style="text-align: center; padding: 20px; color: #666;">هیچ لاگی موجود نیست.</p>');
            return;
        }

        logs.forEach(function(log) {
            var logClass = log.type === 'success' ? 'success' : 'error';
            var logHtml = '<div class="setia-log-item ' + logClass + '">' +
                '<span class="setia-log-time">' + log.time + '</span>' +
                '<span class="setia-log-message">' + log.message + '</span>' +
                '</div>';

            container.append(logHtml);
        });
    }

    // رویدادهای حذف و ویرایش برنامه (با event delegation)
    $(document).on('click', '.delete-schedule', function() {
        var scheduleId = $(this).data('id');

        if (!confirm('آیا مطمئن هستید که می‌خواهید این برنامه را حذف کنید؟')) {
            return;
        }

        $.ajax({
            url: ajaxUrl,
            type: 'POST',
            data: {
                action: 'setia_delete_schedule',
                nonce: nonce,
                schedule_id: scheduleId
            },
            success: function(response) {
                if (response.success) {
                    showMessage('برنامه با موفقیت حذف شد.', 'success');
                    loadSchedulesList();
                } else {
                    showMessage('خطا در حذف برنامه: ' + (response.data.message || 'خطای نامشخص'), 'error');
                }
            },
            error: function() {
                showMessage('خطا در ارتباط با سرور. لطفاً دوباره تلاش کنید.', 'error');
            }
        });
    });

    // بارگذاری اولیه داده‌ها
    loadSchedulesList();
    loadSchedulerLogs();

    // CSS برای انیمیشن چرخش
    $('<style>').text('@keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }').appendTo('head');
});
</script>
