<?php
/**
 * صفحه زمانبندی تولید محتوا
 * SETIA Content Generator Plugin
 */

// جلوگیری از دسترسی مستقیم
if (!defined('ABSPATH')) {
    exit;
}
?>

<div class="wrap setia-scheduler-wrap">
    <div class="setia-header">
        <h1 class="setia-title">
            <span class="setia-icon">⏰</span>
            زمانبندی تولید محتوا
        </h1>
        <p class="setia-subtitle">برنامه‌ریزی و زمانبندی خودکار تولید محتوا با هوش مصنوعی</p>
    </div>

    <div class="setia-main-container">
        <!-- تنظیمات کلی زمانبندی -->
        <div class="setia-card">
            <div class="setia-card-header">
                <h3>تنظیمات کلی زمانبندی</h3>
                <div class="setia-toggle-switch">
                    <input type="checkbox" id="scheduler-enabled" class="setia-toggle-input">
                    <label for="scheduler-enabled" class="setia-toggle-label">
                        <span class="setia-toggle-slider"></span>
                    </label>
                    <span class="setia-toggle-text">فعال‌سازی زمانبندی خودکار</span>
                </div>
            </div>
            
            <div class="setia-card-body">
                <div class="setia-form-grid">
                    <div class="setia-form-group">
                        <label for="schedule-frequency">تناوب تولید محتوا:</label>
                        <select id="schedule-frequency" class="setia-select">
                            <option value="hourly">هر ساعت</option>
                            <option value="daily" selected>روزانه</option>
                            <option value="weekly">هفتگی</option>
                            <option value="monthly">ماهانه</option>
                        </select>
                    </div>
                    
                    <div class="setia-form-group">
                        <label for="content-count">تعداد محتوا در هر بار:</label>
                        <input type="number" id="content-count" class="setia-input" value="1" min="1" max="10">
                    </div>
                    
                    <div class="setia-form-group">
                        <label for="publish-time">زمان انتشار:</label>
                        <input type="time" id="publish-time" class="setia-input" value="09:00">
                    </div>
                    
                    <div class="setia-form-group">
                        <label for="content-type">نوع محتوا:</label>
                        <select id="content-type" class="setia-select">
                            <option value="article">مقاله</option>
                            <option value="product">محصول WooCommerce</option>
                            <option value="mixed">ترکیبی</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>

        <!-- برنامه‌های زمانبندی شده -->
        <div class="setia-card">
            <div class="setia-card-header">
                <h3>برنامه‌های زمانبندی شده</h3>
                <button id="add-schedule" class="setia-button setia-button-primary">
                    <span class="dashicons dashicons-plus-alt"></span>
                    افزودن برنامه جدید
                </button>
            </div>
            
            <div class="setia-schedules-list" id="schedules-list">
                <div class="setia-schedule-item">
                    <div class="setia-schedule-info">
                        <h4>تولید مقاله روزانه</h4>
                        <p>هر روز ساعت 09:00 - 1 مقاله</p>
                        <span class="setia-schedule-status active">فعال</span>
                    </div>
                    <div class="setia-schedule-actions">
                        <button class="setia-button setia-button-small setia-button-outline">ویرایش</button>
                        <button class="setia-button setia-button-small setia-button-danger">حذف</button>
                    </div>
                </div>
                
                <div class="setia-schedule-item">
                    <div class="setia-schedule-info">
                        <h4>تولید محصول هفتگی</h4>
                        <p>هر یکشنبه ساعت 10:00 - 2 محصول</p>
                        <span class="setia-schedule-status inactive">غیرفعال</span>
                    </div>
                    <div class="setia-schedule-actions">
                        <button class="setia-button setia-button-small setia-button-outline">ویرایش</button>
                        <button class="setia-button setia-button-small setia-button-danger">حذف</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- آمار زمانبندی -->
        <div class="setia-stats-grid">
            <div class="setia-stat-card">
                <div class="setia-stat-icon">📅</div>
                <div class="setia-stat-content">
                    <h4>برنامه‌های فعال</h4>
                    <span class="setia-stat-number">2</span>
                </div>
            </div>
            
            <div class="setia-stat-card">
                <div class="setia-stat-icon">✅</div>
                <div class="setia-stat-content">
                    <h4>اجرای موفق امروز</h4>
                    <span class="setia-stat-number">1</span>
                </div>
            </div>
            
            <div class="setia-stat-card">
                <div class="setia-stat-icon">⏳</div>
                <div class="setia-stat-content">
                    <h4>برنامه بعدی</h4>
                    <span class="setia-stat-number">2 ساعت</span>
                </div>
            </div>
            
            <div class="setia-stat-card">
                <div class="setia-stat-icon">📊</div>
                <div class="setia-stat-content">
                    <h4>کل محتوای تولید شده</h4>
                    <span class="setia-stat-number">45</span>
                </div>
            </div>
        </div>

        <!-- لاگ اجرا -->
        <div class="setia-card">
            <div class="setia-card-header">
                <h3>لاگ اجرای زمانبندی</h3>
                <button id="clear-logs" class="setia-button setia-button-outline">پاک کردن لاگ‌ها</button>
            </div>
            
            <div class="setia-logs-container">
                <div class="setia-log-item success">
                    <div class="setia-log-time">1403/04/12 - 09:00</div>
                    <div class="setia-log-message">تولید مقاله "راهنمای خرید گوشی هوشمند" با موفقیت انجام شد</div>
                    <div class="setia-log-status">موفق</div>
                </div>
                
                <div class="setia-log-item error">
                    <div class="setia-log-time">1403/04/11 - 09:00</div>
                    <div class="setia-log-message">خطا در تولید محتوا: کلید API نامعتبر</div>
                    <div class="setia-log-status">خطا</div>
                </div>
                
                <div class="setia-log-item success">
                    <div class="setia-log-time">1403/04/10 - 09:00</div>
                    <div class="setia-log-message">تولید محصول "کیف چرمی مردانه" با موفقیت انجام شد</div>
                    <div class="setia-log-status">موفق</div>
                </div>
            </div>
        </div>

        <!-- دکمه‌های عملیات -->
        <div class="setia-actions-bar">
            <button id="save-scheduler-settings" class="setia-button setia-button-primary setia-button-large">
                <span class="dashicons dashicons-yes"></span>
                ذخیره تنظیمات
            </button>
            
            <button id="test-scheduler" class="setia-button setia-button-secondary setia-button-large">
                <span class="dashicons dashicons-admin-tools"></span>
                تست زمانبندی
            </button>
            
            <button id="run-now" class="setia-button setia-button-outline setia-button-large">
                <span class="dashicons dashicons-controls-play"></span>
                اجرای فوری
            </button>
        </div>
    </div>
</div>

<!-- مودال افزودن برنامه جدید -->
<div id="add-schedule-modal" class="setia-modal" style="display: none;">
    <div class="setia-modal-content">
        <div class="setia-modal-header">
            <h3>افزودن برنامه زمانبندی جدید</h3>
            <span class="setia-modal-close">&times;</span>
        </div>
        <div class="setia-modal-body">
            <form id="schedule-form">
                <div class="setia-form-group">
                    <label for="schedule-name">نام برنامه:</label>
                    <input type="text" id="schedule-name" class="setia-input" placeholder="مثال: تولید مقاله روزانه">
                </div>
                
                <div class="setia-form-group">
                    <label for="schedule-type">نوع محتوا:</label>
                    <select id="schedule-type" class="setia-select">
                        <option value="article">مقاله</option>
                        <option value="product">محصول</option>
                    </select>
                </div>
                
                <div class="setia-form-group">
                    <label for="schedule-frequency-modal">تناوب:</label>
                    <select id="schedule-frequency-modal" class="setia-select">
                        <option value="daily">روزانه</option>
                        <option value="weekly">هفتگی</option>
                        <option value="monthly">ماهانه</option>
                    </select>
                </div>
                
                <div class="setia-form-group">
                    <label for="schedule-time">زمان اجرا:</label>
                    <input type="time" id="schedule-time" class="setia-input">
                </div>
                
                <div class="setia-modal-actions">
                    <button type="submit" class="setia-button setia-button-primary">ایجاد برنامه</button>
                    <button type="button" class="setia-button setia-button-secondary setia-modal-close">انصراف</button>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
.setia-scheduler-wrap {
    direction: rtl;
    font-family: 'IRANSans', Tahoma, sans-serif;
}

.setia-form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.setia-toggle-switch {
    display: flex;
    align-items: center;
    gap: 10px;
}

.setia-toggle-input {
    display: none;
}

.setia-toggle-label {
    position: relative;
    width: 50px;
    height: 24px;
    background: #ccc;
    border-radius: 12px;
    cursor: pointer;
    transition: background 0.3s;
}

.setia-toggle-slider {
    position: absolute;
    top: 2px;
    right: 2px;
    width: 20px;
    height: 20px;
    background: white;
    border-radius: 50%;
    transition: transform 0.3s;
}

.setia-toggle-input:checked + .setia-toggle-label {
    background: #667eea;
}

.setia-toggle-input:checked + .setia-toggle-label .setia-toggle-slider {
    transform: translateX(-26px);
}

.setia-schedules-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.setia-schedule-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
    border-right: 4px solid #667eea;
}

.setia-schedule-info h4 {
    margin: 0 0 5px 0;
    color: #2c3e50;
}

.setia-schedule-info p {
    margin: 0 0 10px 0;
    color: #666;
}

.setia-schedule-status {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
}

.setia-schedule-status.active {
    background: #d4edda;
    color: #155724;
}

.setia-schedule-status.inactive {
    background: #f8d7da;
    color: #721c24;
}

.setia-schedule-actions {
    display: flex;
    gap: 10px;
}

.setia-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin: 20px 0;
}

.setia-stat-card {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.setia-stat-icon {
    font-size: 2em;
}

.setia-stat-content h4 {
    margin: 0 0 5px 0;
    font-size: 14px;
    opacity: 0.9;
}

.setia-stat-number {
    font-size: 24px;
    font-weight: bold;
}

.setia-logs-container {
    max-height: 300px;
    overflow-y: auto;
}

.setia-log-item {
    display: grid;
    grid-template-columns: 150px 1fr 80px;
    gap: 15px;
    padding: 15px;
    border-bottom: 1px solid #e1e8ed;
    align-items: center;
}

.setia-log-item.success {
    border-right: 4px solid #28a745;
}

.setia-log-item.error {
    border-right: 4px solid #dc3545;
}

.setia-log-time {
    font-size: 12px;
    color: #666;
    font-weight: 600;
}

.setia-log-message {
    color: #2c3e50;
}

.setia-log-status {
    text-align: center;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 600;
}

.setia-log-item.success .setia-log-status {
    background: #d4edda;
    color: #155724;
}

.setia-log-item.error .setia-log-status {
    background: #f8d7da;
    color: #721c24;
}

.setia-actions-bar {
    display: flex;
    gap: 15px;
    justify-content: center;
    margin-top: 30px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
}

.setia-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    z-index: 10000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.setia-modal-content {
    background: white;
    border-radius: 12px;
    max-width: 500px;
    width: 90%;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
}

.setia-modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 12px 12px 0 0;
}

.setia-modal-header h3 {
    margin: 0;
}

.setia-modal-close {
    font-size: 24px;
    cursor: pointer;
    opacity: 0.8;
    transition: opacity 0.3s;
}

.setia-modal-close:hover {
    opacity: 1;
}

.setia-modal-body {
    padding: 20px;
}

.setia-modal-actions {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
    margin-top: 20px;
}
</style>

<script>
jQuery(document).ready(function($) {
    // رویدادها
    $('#add-schedule').on('click', function() {
        $('#add-schedule-modal').show();
    });

    $('.setia-modal-close').on('click', function() {
        $('.setia-modal').hide();
    });

    $('#save-scheduler-settings').on('click', function() {
        alert('تنظیمات زمانبندی ذخیره شد.');
    });

    $('#test-scheduler').on('click', function() {
        alert('تست زمانبندی با موفقیت انجام شد.');
    });

    $('#run-now').on('click', function() {
        if (confirm('آیا مطمئن هستید که می‌خواهید زمانبندی را الان اجرا کنید؟')) {
            alert('زمانبندی در حال اجرا است...');
        }
    });

    $('#schedule-form').on('submit', function(e) {
        e.preventDefault();
        alert('برنامه جدید ایجاد شد.');
        $('#add-schedule-modal').hide();
    });

    $('#clear-logs').on('click', function() {
        if (confirm('آیا مطمئن هستید که می‌خواهید لاگ‌ها را پاک کنید؟')) {
            $('.setia-logs-container').html('<p style="text-align: center; padding: 20px; color: #666;">لاگ‌ها پاک شدند.</p>');
        }
    });
});
</script>
