<?php // Scheduler template file

/**
 * قالب صفحه زمانبندی تولید محتوا
 */

// امنیت: جلوگیری از دسترسی مستقیم
if (!defined('ABSPATH')) {
    exit;
}
?>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<div class="wrap setia-history-page setia-scheduler-page setia-fade-in">
    <div class="setia-history-header">
        <h1>
            <span class="setia-icon">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><path d="M464 256A208 208 0 1 1 48 256a208 208 0 1 1 416 0zM0 256a256 256 0 1 0 512 0A256 256 0 1 0 0 256zM232 120V256c0 8 4 15.5 10.7 20l96 64c11 7.4 25.9 4.4 33.3-6.7s4.4-25.9-6.7-33.3L280 243.2V120c0-13.3-10.7-24-24-24s-24 10.7-24 24z"/></svg>
            </span> 
            زمانبندی تولید خودکار محتوا
        </h1>
    </div>
    
    <div class="setia-notice setia-info-notice">
        <p>
            <strong>راهنمای اجرای دستی:</strong> 
            برای اجرای دستی زمانبندی، می‌توانید از دکمه "اجرای دستی" در لیست زمانبندی‌ها استفاده کنید یا با استفاده از آدرس زیر، زمانبندی را مستقیماً اجرا کنید:
            <code><?php echo site_url(); ?>/wp-content/plugins/SETIA/manual-scheduler.php?id=SCHEDULE_ID</code>
        </p>
    </div>
    
    <div class="setia-tabs">
        <div class="setia-tab active" data-tab="schedules">لیست زمانبندی‌ها</div>
        <div class="setia-tab" data-tab="new">افزودن زمانبندی جدید</div>
    </div>
    
    <div class="setia-tab-content active" id="schedules-tab">
            <div class="setia-card">
            <div class="setia-card-header">
                <h3 class="setia-card-title">زمانبندی‌های موجود</h3>
            </div>
            <div class="setia-card-body">
                <p>در این بخش می‌توانید لیست زمانبندی‌های تولید خودکار محتوا را مشاهده و مدیریت کنید.</p>
                
                <div class="setia-schedules-list">
                    <table class="setia-history-table">
                        <thead>
                            <tr>
                                <th>عنوان</th>
                                <th>موضوع</th>
                                <th>دسته‌بندی</th>
                                <th>تناوب</th>
                                <th>وضعیت</th>
                                <th>اجرای بعدی</th>
                                <th>عملیات</th>
                            </tr>
                        </thead>
                        <tbody id="schedules-list">
                            <tr>
                                <td colspan="7">در حال بارگذاری...</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    
    <div class="setia-tab-content" id="new-tab">
            <div class="setia-card">
            <div class="setia-card-header">
                <h3 class="setia-card-title">افزودن زمانبندی جدید</h3>
            </div>
            <div class="setia-card-body">
                <p>با استفاده از فرم زیر می‌توانید یک زمانبندی جدید برای تولید خودکار محتوا ایجاد کنید.</p>
                
                <form id="schedule-form" class="setia-form" style="width:100%; max-width:100%;">
                    <input type="hidden" id="schedule_id" name="schedule_id" value="0">
                    
                    <div class="setia-form-group setia-full-width" style="width:100% !important; display:block !important;">
                        <label for="title">عنوان زمانبندی:</label>
                        <div class="setia-input-container">
                            <span class="setia-input-icon">
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" width="16" height="16" fill="var(--setia-text-medium)"><path d="M471.6 21.7c-21.9-21.9-57.3-21.9-79.2 0L362.3 51.7l97.9 97.9 30.1-30.1c21.9-21.9 21.9-57.3 0-79.2L471.6 21.7zm-299.2 220c-6.1 6.1-10.8 13.6-13.5 21.9l-29.6 88.8c-2.9 8.6-.6 18.1 5.8 24.6s15.9 8.7 24.6 5.8l88.8-29.6c8.2-2.8 15.7-7.4 21.9-13.5L437.7 172.3 339.7 74.3 172.4 241.7zM96 64C43 64 0 107 0 160V416c0 53 43 96 96 96H352c53 0 96-43 96-96V320c0-17.7-14.3-32-32-32s-32 14.3-32 32v96c0 17.7-14.3 32-32 32H96c-17.7 0-32-14.3-32-32V160c0-17.7 14.3-32 32-32h96c17.7 0 32-14.3 32-32s-14.3-32-32-32H96z"/></svg>
                            </span>
                            <input type="text" id="title" name="title" class="setia-input" style="width:100% !important;" placeholder="مثال: تولید مقالات سئو هفتگی" required>
                        </div>
                    </div>
                    
                    <div class="setia-form-group setia-full-width" style="width:100% !important; display:block !important;">
                        <label for="topic">موضوع محتوا:</label>
                        <div class="setia-input-container">
                            <span class="setia-input-icon">
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512" width="16" height="16" fill="var(--setia-text-medium)"><path d="M96 0C43 0 0 43 0 96V416c0 53 43 96 96 96H384h32c17.7 0 32-14.3 32-32s-14.3-32-32-32V384c17.7 0 32-14.3 32-32V32c0-17.7-14.3-32-32-32H384 96zm0 384H352v64H96c-17.7 0-32-14.3-32-32s14.3-32 32-32zm32-240c0-8.8 7.2-16 16-16H336c8.8 0 16 7.2 16 16s-7.2 16-16 16H144c-8.8 0-16-7.2-16-16zm16 48H336c8.8 0 16 7.2 16 16s-7.2 16-16 16H144c-8.8 0-16-7.2-16-16s7.2-16 16-16z"/></svg>
                            </span>
                            <input type="text" id="topic" name="topic" class="setia-input" style="width:100% !important;" placeholder="موضوع اصلی محتوا را وارد کنید" required>
                        </div>
                    </div>
                    
                    <div class="setia-form-group setia-full-width" style="width:100% !important; display:block !important;">
                        <label for="keywords">کلمات کلیدی:</label>
                        <div class="setia-input-container">
                            <span class="setia-input-icon">
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 640 512" width="16" height="16" fill="var(--setia-text-medium)"><path d="M392.8 1.2c-17-4.9-34.7 5-39.6 22l-128 448c-4.9 17 5 34.7 22 39.6s34.7-5 39.6-22l128-448c4.9-17-5-34.7-22-39.6zm80.6 120.1c-12.5 12.5-12.5 32.8 0 45.3L562.7 256l-89.4 89.4c-12.5 12.5-12.5 32.8 0 45.3s32.8 12.5 45.3 0l112-112c12.5-12.5 12.5-32.8 0-45.3l-112-112c-12.5-12.5-32.8-12.5-45.3 0zm-306.7 0c-12.5-12.5-32.8-12.5-45.3 0l-112 112c-12.5 12.5-12.5 32.8 0 45.3l112 112c12.5 12.5 32.8 12.5 45.3 0s12.5-32.8 0-45.3L77.3 256l89.4-89.4c12.5-12.5 12.5-32.8 0-45.3z"/></svg>
                            </span>
                            <input type="text" id="keywords" name="keywords" class="setia-input" style="width:100% !important;" placeholder="کلمات کلیدی را با کاما جدا کنید" required>
                        </div>
                    </div>
                    
                    <div class="setia-form-group">
                        <label for="category">دسته‌بندی:</label>
                        <div class="setia-input-container">
                            <span class="setia-input-icon">
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" width="16" height="16" fill="var(--setia-text-medium)"><path d="M40 48C26.7 48 16 58.7 16 72v48c0 13.3 10.7 24 24 24H88c13.3 0 24-10.7 24-24V72c0-13.3-10.7-24-24-24H40zM192 64c-17.7 0-32 14.3-32 32s14.3 32 32 32H480c17.7 0 32-14.3 32-32s-14.3-32-32-32H192zm0 160c-17.7 0-32 14.3-32 32s14.3 32 32 32H480c17.7 0 32-14.3 32-32s-14.3-32-32-32H192zm0 160c-17.7 0-32 14.3-32 32s14.3 32 32 32H480c17.7 0 32-14.3 32-32s-14.3-32-32-32H192zM16 232v48c0 13.3 10.7 24 24 24H88c13.3 0 24-10.7 24-24V232c0-13.3-10.7-24-24-24H40c-13.3 0-24 10.7-24 24zM40 368c-13.3 0-24 10.7-24 24v48c0 13.3 10.7 24 24 24H88c13.3 0 24-10.7 24-24V392c0-13.3-10.7-24-24-24H40z"/></svg>
                            </span>
                        <?php wp_dropdown_categories(array(
                            'name' => 'category',
                            'id' => 'category',
                            'class' => 'setia-select',
                            'show_option_none' => 'انتخاب دسته‌بندی',
                                'option_none_value' => '',
                            'hierarchical' => true,
                                'required' => true
                        )); ?>
                        </div>
                    </div>
                    
                    <div class="setia-form-row">
                        <div class="setia-form-group">
                            <label for="tone">لحن محتوا:</label>
                            <div class="setia-input-container">
                                <span class="setia-input-icon">
                                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" width="16" height="16" fill="var(--setia-text-medium)"><path d="M256 0C114.6 0 0 114.6 0 256s114.6 256 256 256s256-114.6 256-256S397.4 0 256 0zM256 464c-114.7 0-208-93.31-208-208S141.3 48 256 48s208 93.31 208 208S370.7 464 256 464zM256 288c17.67 0 32-14.33 32-32c0-17.67-14.33-32-32-32c-17.67 0-32 14.33-32 32C224 273.7 238.3 288 256 288zM296 384h-80C202.8 384 192 394.8 192 408C192 421.2 202.8 432 216 432h80c13.2 0 24-10.8 24-24C320 394.8 309.2 384 296 384z"/></svg>
                                </span>
                            <select id="tone" name="tone" class="setia-select">
                                <option value="عادی">عادی</option>
                                <option value="رسمی">رسمی</option>
                                <option value="دوستانه">دوستانه</option>
                                    <option value="تخصصی">تخصصی</option>
                                <option value="آموزشی">آموزشی</option>
                            </select>
                            </div>
                        </div>
                        
                        <div class="setia-form-group">
                            <label for="length">طول محتوا:</label>
                            <div class="setia-input-container">
                                <span class="setia-input-icon">
                                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512" width="16" height="16" fill="var(--setia-text-medium)"><path d="M254 52.8C249.3 40.3 237.3 32 224 32s-25.3 8.3-30 20.8L57.8 416H32c-17.7 0-32 14.3-32 32s14.3 32 32 32h96c17.7 0 32-14.3 32-32s-14.3-32-32-32h-1.8l18-48H303.8l18 48H320c-17.7 0-32 14.3-32 32s14.3 32 32 32h96c17.7 0 32-14.3 32-32s-14.3-32-32-32H390.2L254 52.8zM279.8 304H168.2L224 155.1 279.8 304z"/></svg>
                                </span>
                            <select id="length" name="length" class="setia-select">
                                <option value="کوتاه">کوتاه (300-500 کلمه)</option>
                                <option value="متوسط" selected>متوسط (500-800 کلمه)</option>
                                    <option value="بلند">بلند (800-1500 کلمه)</option>
                                    <option value="خیلی بلند">خیلی بلند (1500+ کلمه)</option>
                            </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="setia-form-row">
                        <div class="setia-form-group">
                            <label for="frequency">تناوب زمانی:</label>
                            <div class="setia-input-container">
                                <span class="setia-input-icon">
                                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" width="16" height="16" fill="var(--setia-text-medium)"><path d="M464 256A208 208 0 1 1 48 256a208 208 0 1 1 416 0zM0 256a256 256 0 1 0 512 0A256 256 0 1 0 0 256zM232 120V256c0 8 4 15.5 10.7 20l96 64c11 7.4 25.9 4.4 33.3-6.7s4.4-25.9-6.7-33.3L280 243.2V120c0-13.3-10.7-24-24-24s-24 10.7-24 24z"/></svg>
                                </span>
                            <select id="frequency" name="frequency" class="setia-select">
                                <option value="minutely">هر دقیقه</option>
                                <option value="hourly">هر ساعت</option>
                                    <option value="daily" selected>روزانه</option>
                                    <option value="weekly">هفتگی</option>
                                <option value="monthly">ماهانه</option>
                            </select>
                            </div>
                        </div>
                        
                        <div class="setia-form-group">
                            <label for="status">وضعیت:</label>
                            <div class="setia-input-container">
                                <span class="setia-input-icon">
                                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 576 512" width="16" height="16" fill="var(--setia-text-medium)"><path d="M96 0C78.3 0 64 14.3 64 32v96h64V32c0-17.7-14.3-32-32-32zM288 0c-17.7 0-32 14.3-32 32v96h64V32c0-17.7-14.3-32-32-32zM32 160c-17.7 0-32 14.3-32 32s14.3 32 32 32h576c17.7 0 32-14.3 32-32s-14.3-32-32-32H32zm448 96c0-17.7-14.3-32-32-32s-32 14.3-32 32v224c0 17.7 14.3 32 32 32s32-14.3 32-32V256zM320 416c0 35.3 28.7 64 64 64s64-28.7 64-64V256c0-35.3-28.7-64-64-64s-64 28.7-64 64v160zM256 256c0-17.7-14.3-32-32-32s-32 14.3-32 32v224c0 17.7 14.3 32 32 32s32-14.3 32-32V256zM64 256c0-17.7-14.3-32-32-32s-32 14.3-32 32v224c0 17.7 14.3 32 32 32s32-14.3 32-32V256zM192 448a64 64 0 1 0 0-128 64 64 0 1 0 0 128z"/></svg>
                                </span>
                            <select id="status" name="status" class="setia-select">
                                <option value="active">فعال</option>
                                    <option value="paused">متوقف شده</option>
                                    <option value="draft">پیش‌نویس</option>
                            </select>
                            </div>
                        </div>

                        <div class="setia-form-group">
                            <label for="status">حداکثر تعداد تولید (اختیاری):</label>
                            <div class="setia-input-container">
                                <span class="setia-input-icon">
                                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 576 512" width="16" height="16" fill="var(--setia-text-medium)"><path d="M96 0C78.3 0 64 14.3 64 32v96h64V32c0-17.7-14.3-32-32-32zM288 0c-17.7 0-32 14.3-32 32v96h64V32c0-17.7-14.3-32-32-32zM32 160c-17.7 0-32 14.3-32 32s14.3 32 32 32h576c17.7 0 32-14.3 32-32s-14.3-32-32-32H32zm448 96c0-17.7-14.3-32-32-32s-32 14.3-32 32v224c0 17.7 14.3 32 32 32s32-14.3 32-32V256zM320 416c0 35.3 28.7 64 64 64s64-28.7 64-64V256c0-35.3-28.7-64-64-64s-64 28.7-64 64v160zM256 256c0-17.7-14.3-32-32-32s-32 14.3-32 32v224c0 17.7 14.3 32 32 32s32-14.3 32-32V256zM64 256c0-17.7-14.3-32-32-32s-32 14.3-32 32v224c0 17.7 14.3 32 32 32s32-14.3 32-32V256zM192 448a64 64 0 1 0 0-128 64 64 0 1 0 0 128z"/></svg>
                                </span>
                                <input type="number" id="daily_limit" name="daily_limit"  class="setia-input" style="width:100% !important;" min="0" placeholder="مثلاً 3 برای حداکثر 3 مطلب در روز">

                            </div>
                        </div>

                    </div>
                    
                    
                    <div class="setia-checkbox-wrapper">
                        <label class="setia-checkbox-label">
                            <input type="checkbox" id="ai_generated" name="ai_generated" class="setia-checkbox" value="1" checked>
                            <span class="setia-checkbox-text">تولید خودکار تصویر شاخص</span>
                        </label>
                    </div>
                    
                    <div class="setia-form-actions">
                        <button type="submit" id="submit-btn" class="button button-primary setia-button">
                            <span class="setia-button-icon">
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512" width="16" height="16" fill="currentColor">
                                    <path d="M64 32C28.7 32 0 60.7 0 96V416c0 35.3 28.7 64 64 64H384c35.3 0 64-28.7 64-64V173.3c0-17-6.7-33.3-18.7-45.3L352 50.7C340 38.7 323.7 32 306.7 32H64zm0 96c0-17.7 14.3-32 32-32H288c17.7 0 32 14.3 32 32v64c0 17.7-14.3 32-32 32H96c-17.7 0-32-14.3-32-32V128zM224 416c-35.3 0-64-28.7-64-64s28.7-64 64-64s64 28.7 64 64s-28.7 64-64 64z"/>
                                </svg>
                            </span>
                            ذخیره زمانبندی
                        </button>
                        <button type="button" id="cancel-edit" class="setia-button" style="display: none;">انصراف</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
jQuery(document).ready(function($) {
    // متغیرهای سراسری
    const nonce = '<?php echo wp_create_nonce('setia-nonce'); ?>';
    let isEditing = false;
    
    // تنظیمات سفارشی SweetAlert2 برای هماهنگی با استایل صفحه
    const SetiaSweet = Swal.mixin({
        customClass: {
            confirmButton: 'setia-swal-confirm-button',
            cancelButton: 'setia-swal-cancel-button',
            title: 'setia-swal-title',
            popup: 'setia-swal-popup',
            container: 'setia-swal-container'
        },
        buttonsStyling: false,
        confirmButtonText: 'تایید',
        cancelButtonText: 'انصراف',
        showClass: {
            popup: 'setia-swal-show'
        },
        hideClass: {
            popup: 'setia-swal-hide'
        }
    });
    
    // تغییر تب‌ها
    $('.setia-tab').on('click', function() {
        $('.setia-tab').removeClass('active');
        $(this).addClass('active');
        
        const tabId = $(this).data('tab') + '-tab';
        $('.setia-tab-content').removeClass('active');
        $('#' + tabId).addClass('active');
    });
    
    // بارگذاری زمانبندی‌ها
    function loadSchedules() {
        $.ajax({
            url: ajaxurl,
            type: 'GET',
            data: {
                action: 'setia_get_schedules',
                nonce: nonce
            },
            success: function(response) {
                if (response.success) {
                    renderSchedules(response.data.schedules);
                } else {
                    // استفاده از SweetAlert2 سفارشی به جای alert استاندارد
                    SetiaSweet.fire({
                        title: 'خطا',
                        text: 'خطا در بارگذاری زمانبندی‌ها: ' + response.data.message,
                        icon: 'error',
                        confirmButtonText: 'متوجه شدم'
                    });
                }
            },
            error: function() {
                // استفاده از SweetAlert2 سفارشی به جای alert استاندارد
                SetiaSweet.fire({
                    title: 'خطا',
                    text: 'خطا در ارتباط با سرور',
                    icon: 'error',
                    confirmButtonText: 'متوجه شدم'
                });
            }
        });
    }
    
    // نمایش زمانبندی‌ها
    function renderSchedules(schedules) {
        const $list = $('#schedules-list');
        $list.empty();
        
        if (Object.keys(schedules).length === 0) {
            $list.html('<tr><td colspan="7" class="setia-no-history"><span class="setia-icon" style="width: 48px; height: 48px;"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 384 512"><path d="M0 64C0 28.7 28.7 0 64 0H224V128c0 17.7 14.3 32 32 32H384V448c0 35.3-28.7 64-64 64H64c-35.3 0-64-28.7-64-64V64zm384 64H256V0L384 128z"/></svg></span><p>هیچ زمانبندی یافت نشد.</p></td></tr>');
            return;
        }
        
        Object.keys(schedules).forEach(function(id) {
            const schedule = schedules[id];
            const statusClass = schedule.status === 'active' ? 'setia-tag-tone' : '';
            const statusText = schedule.status === 'active' ? 'فعال' : 'غیرفعال';
            
            const $row = $('<tr></tr>');
            $row.append(`<td>${schedule.title}</td>`);
            $row.append(`<td>${schedule.topic}</td>`);
            $row.append(`<td>${schedule.category_name}</td>`);
            $row.append(`<td>${getFrequencyText(schedule.frequency)}</td>`);
            $row.append(`<td><span class="setia-tag ${statusClass}">${statusText}</span></td>`);
            $row.append(`<td>${schedule.next_run}</td>`);
            
            const $actions = $('<td class="setia-actions"></td>');
            $actions.append(`<button class="setia-button edit-schedule" data-id="${id}">ویرایش</button>`);
            $actions.append(`<button class="setia-button setia-danger-button delete-schedule" data-id="${id}">حذف</button>`);
            $actions.append(`<button class="setia-button setia-primary-button run-schedule" data-id="${id}">اجرای دستی</button>`);
            
            $row.append($actions);
            $list.append($row);
        });
        
        // رویدادهای دکمه‌ها
        $('.edit-schedule').on('click', function() {
            const id = $(this).data('id');
            editSchedule(id, schedules[id]);
        });
        
        $('.delete-schedule').on('click', function() {
            const id = $(this).data('id');
            
            // استفاده از SweetAlert2 سفارشی به جای alert استاندارد
            SetiaSweet.fire({
                title: 'آیا مطمئن هستید؟',
                text: 'آیا از حذف این زمانبندی اطمینان دارید؟',
                icon: 'warning',
                showCancelButton: true,
                confirmButtonText: 'بله، حذف کن',
                cancelButtonText: 'انصراف'
            }).then((result) => {
                if (result.isConfirmed) {
                deleteSchedule(id);
            }
            });
        });
        
        $('.run-schedule').on('click', function() {
            const id = $(this).data('id');
            
            // استفاده از SweetAlert2 سفارشی به جای alert استاندارد
            SetiaSweet.fire({
                title: 'اجرای دستی زمانبندی',
                text: 'آیا می‌خواهید این زمانبندی را به صورت دستی اجرا کنید؟',
                icon: 'question',
                showCancelButton: true,
                confirmButtonText: 'بله، اجرا کن',
                cancelButtonText: 'انصراف'
            }).then((result) => {
                if (result.isConfirmed) {
                runScheduleManually(id);
            }
            });
        });
    }
    
    // تبدیل کد تناوب به متن فارسی
    function getFrequencyText(frequency) {
        const frequencies = {
            'minutely': 'هر دقیقه',
            'every5minutes': 'هر 5 دقیقه',
            'every15minutes': 'هر 15 دقیقه',
            'every30minutes': 'هر 30 دقیقه',
            'hourly': 'هر ساعت',
            'twicedaily': 'روزانه دو بار',
            'daily': 'روزانه',
            'weekly': 'هفتگی',
            'monthly': 'ماهانه'
        };
        
        return frequencies[frequency] || frequency;
    }
    
    // ویرایش زمانبندی
    function editSchedule(id, schedule) {
        isEditing = true;
        
        // تغییر به تب افزودن/ویرایش
        $('.setia-tab[data-tab="new"]').click();
        
        // پر کردن فرم
        $('#schedule_id').val(id);
        $('#title').val(schedule.title);
        $('#topic').val(schedule.topic);
        $('#keywords').val(schedule.keywords);
        $('#category').val(schedule.category);
        $('#tone').val(schedule.tone);
        $('#length').val(schedule.length);
        $('#frequency').val(schedule.frequency);
        $('#status').val(schedule.status);
        $('#ai_generated').prop('checked', schedule.ai_generated === '1');
        $('#daily_limit').val(schedule.daily_limit || '');
        
        // تغییر عنوان فرم و نمایش دکمه انصراف
        $('.setia-card-header .setia-card-title').text('ویرایش زمانبندی');
        $('#cancel-edit').show();
    }
    
    // حذف زمانبندی
    function deleteSchedule(id) {
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'setia_delete_schedule',
                nonce: nonce,
                schedule_id: id
            },
            success: function(response) {
                if (response.success) {
                    // استفاده از SweetAlert2 سفارشی به جای alert استاندارد
                    SetiaSweet.fire({
                        title: 'موفقیت‌آمیز',
                        text: 'زمانبندی با موفقیت حذف شد',
                        icon: 'success',
                        confirmButtonText: 'متوجه شدم'
                    });
                    loadSchedules();
                } else {
                    // استفاده از SweetAlert2 سفارشی به جای alert استاندارد
                    SetiaSweet.fire({
                        title: 'خطا',
                        text: 'خطا در حذف زمانبندی: ' + response.data.message,
                        icon: 'error',
                        confirmButtonText: 'متوجه شدم'
                    });
                }
            },
            error: function() {
                // استفاده از SweetAlert2 سفارشی به جای alert استاندارد
                SetiaSweet.fire({
                    title: 'خطا',
                    text: 'خطا در ارتباط با سرور',
                    icon: 'error',
                    confirmButtonText: 'متوجه شدم'
                });
            }
        });
    }
    
    // اجرای دستی زمانبندی
    function runScheduleManually(id) {
        // نمایش پیام در حال اجرا
        SetiaSweet.fire({
            title: 'در حال اجرا',
            text: 'در حال اجرای زمانبندی، لطفاً منتظر بمانید...',
            icon: 'info',
            allowOutsideClick: false,
            showConfirmButton: false,
            willOpen: () => {
                Swal.showLoading();
            }
        });
        
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'setia_run_schedule_manually',
                nonce: nonce,
                schedule_id: id
            },
            success: function(response) {
                if (response.success) {
                    SetiaSweet.fire({
                        title: 'موفقیت‌آمیز',
                        text: 'زمانبندی با موفقیت اجرا شد. محتوا در بخش نوشته‌ها قابل مشاهده است.',
                        icon: 'success',
                        confirmButtonText: 'متوجه شدم'
                    });
                    loadSchedules();
                } else {
                    SetiaSweet.fire({
                        title: 'خطا',
                        text: 'خطا در اجرای زمانبندی: ' + (response.data.message || 'خطای نامشخص'),
                        icon: 'error',
                        confirmButtonText: 'متوجه شدم'
                    });
                }
            },
            error: function() {
                SetiaSweet.fire({
                    title: 'خطا',
                    text: 'خطا در ارتباط با سرور',
                    icon: 'error',
                    confirmButtonText: 'متوجه شدم'
                });
            }
        });
    }
    
    // ارسال فرم
    $('#schedule-form').on('submit', function(e) {
        e.preventDefault();
        
        const formData = $(this).serialize();
        
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'setia_save_schedule',
                nonce: nonce,
                title: $('#title').val(),
                topic: $('#topic').val(),
                keywords: $('#keywords').val(),
                category: $('#category').val(),
                tone: $('#tone').val(),
                length: $('#length').val(),
                frequency: $('#frequency').val(),
                status: $('#status').val(),
                daily_limit: $('#daily_limit').val(),
                ai_generated: $('#ai_generated').is(':checked') ? '1' : '0',
                schedule_id: $('#schedule_id').val()
            },
            success: function(response) {
                if (response.success) {
                    // استفاده از SweetAlert2 سفارشی به جای alert استاندارد
                    SetiaSweet.fire({
                        title: 'موفقیت‌آمیز',
                        text: 'زمانبندی با موفقیت ذخیره شد',
                        icon: 'success',
                        confirmButtonText: 'متوجه شدم'
                    }).then(() => {
                    resetForm();
                    loadSchedules();
                    $('.setia-tab[data-tab="schedules"]').click();
                    });
                } else {
                    // استفاده از SweetAlert2 سفارشی به جای alert استاندارد
                    SetiaSweet.fire({
                        title: 'خطا',
                        text: 'خطا در ذخیره زمانبندی: ' + response.data.message,
                        icon: 'error',
                        confirmButtonText: 'متوجه شدم'
                    });
                }
            },
            error: function(xhr, status, error) {
                console.error('خطای AJAX:', error);
                // استفاده از SweetAlert2 سفارشی به جای alert استاندارد
                SetiaSweet.fire({
                    title: 'خطا',
                    text: 'خطا در ارتباط با سرور: ' + error,
                    icon: 'error',
                    confirmButtonText: 'متوجه شدم'
                });
            }
        });
    });
    
    // دکمه انصراف
    $('#cancel-edit').on('click', function() {
        resetForm();
        $('.setia-tab[data-tab="schedules"]').click();
    });
    
    // بازنشانی فرم
    function resetForm() {
        isEditing = false;
        $('#schedule-form')[0].reset();
        $('#schedule_id').val('0');
        $('#daily_limit').val('');
        $('.setia-card-header .setia-card-title').text('افزودن زمانبندی جدید');
        $('#cancel-edit').hide();
    }
    
    // بارگذاری اولیه زمانبندی‌ها
    loadSchedules();
});
</script>

<style>
:root {
    --setia-primary: #4a6bef;
    --setia-primary-dark: #3854c8;
    --setia-secondary: #6c49b8;
    --setia-accent: #ff7043;
    --setia-success: #43a047;
    --setia-warning: #ff9800;
    --setia-error: #f44336;
    --setia-gray-100: #f5f5f5;
    --setia-gray-200: #eeeeee;
    --setia-gray-300: #e0e0e0;
    --setia-gray-400: #bdbdbd;
    --setia-text-dark: #202124;
    --setia-text-medium: #5f6368;
    --setia-text-light: #80868b;
    --setia-gradient-primary: linear-gradient(135deg, var(--setia-primary) 0%, var(--setia-secondary) 100%);
    --setia-box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    --setia-border-radius: 8px;
    --input-icon-width: 40px;
}

/* اطمینان از نمایش صحیح آیکون‌ها */
.dashicons, .dashicons-before:before {
    font-family: dashicons !important;
    display: inline-block;
    line-height: 1;
    font-weight: 400;
    font-style: normal;
    text-decoration: inherit;
    text-transform: none;
    text-rendering: auto;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    width: 20px;
    height: 20px;
    font-size: 20px;
    vertical-align: middle;
    text-align: center;
}

/* استایل سفارشی برای مدال‌های SweetAlert2 */
.setia-swal-popup {
    font-family: 'IRANSans', Tahoma, sans-serif !important;
    border-radius: var(--setia-border-radius) !important;
    box-shadow: var(--setia-box-shadow) !important;
    padding: 20px !important;
    direction: rtl !important;
}

.setia-swal-popup::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--setia-gradient-primary);
    border-radius: var(--setia-border-radius) var(--setia-border-radius) 0 0;
}

.setia-swal-title {
    color: var(--setia-primary) !important;
    font-size: 20px !important;
    margin-top: 10px !important;
    margin-bottom: 15px !important;
    font-weight: 600 !important;
    text-align: right !important;
}

.setia-swal-container .swal2-html-container {
    text-align: right !important;
    font-size: 14px !important;
    color: var(--setia-text-medium) !important;
    margin-bottom: 20px !important;
}

.setia-swal-container .swal2-icon {
    margin: 5px auto 15px !important;
}

.setia-swal-confirm-button {
    background-color: var(--setia-primary) !important;
    color: white !important;
    border: none !important;
    border-radius: var(--setia-border-radius) !important;
    padding: 8px 16px !important;
    font-size: 14px !important;
    cursor: pointer !important;
    transition: background-color 0.2s ease !important;
    margin: 0 5px !important;
}

.setia-swal-confirm-button:hover {
    background-color: var(--setia-primary-dark) !important;
}

.setia-swal-cancel-button {
    background-color: var(--setia-gray-300) !important;
    color: var(--setia-text-medium) !important;
    border: none !important;
    border-radius: var(--setia-border-radius) !important;
    padding: 8px 16px !important;
    font-size: 14px !important;
    cursor: pointer !important;
    transition: background-color 0.2s ease !important;
    margin: 0 5px !important;
}

.setia-swal-cancel-button:hover {
    background-color: var(--setia-gray-400) !important;
}

.setia-swal-container.swal2-backdrop-show {
    backdrop-filter: blur(5px) !important;
}

.setia-swal-show {
    animation: setiaSwalShowAnimation 0.3s ease-out !important;
}

.setia-swal-hide {
    animation: setiaSwalHideAnimation 0.2s ease-in forwards !important;
}

@keyframes setiaSwalShowAnimation {
    0% { transform: scale(0.8); opacity: 0; }
    100% { transform: scale(1); opacity: 1; }
}

@keyframes setiaSwalHideAnimation {
    0% { transform: scale(1); opacity: 1; }
    100% { transform: scale(0.8); opacity: 0; }
}

.wrap.setia-history-page {
    max-width: 1300px;
    margin: 20px auto;
    background-color: white;
    padding: 25px;
    border-radius: var(--setia-border-radius);
    box-shadow: var(--setia-box-shadow);
    position: relative;
    overflow: hidden;
}

.wrap.setia-history-page::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 5px;
    background: var(--setia-gradient-primary);
}

.setia-icon svg {
    width: 24px;
    height: 24px;
    fill: var(--setia-primary);
}

.setia-history-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
    padding-bottom: 15px;
    border-bottom: 1px solid var(--setia-gray-300);
}

.setia-history-header h1 {
    color: var(--setia-primary);
    display: flex;
    align-items: center;
    gap: 10px;
    margin: 0;
    font-size: 24px;
}

.setia-card {
    background-color: white;
    border-radius: var(--setia-border-radius);
    box-shadow: var(--setia-box-shadow);
    margin-bottom: 20px;
    overflow: hidden;
}

.setia-card-header {
    padding: 15px 20px;
    background: var(--setia-gray-100);
    border-bottom: 1px solid var(--setia-gray-300);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.setia-card-body {
    padding: 20px;
}

.setia-card-title {
    margin: 0;
    color: var(--setia-text-dark);
    font-size: 16px;
    font-weight: 600;
}

.setia-history-table {
    width: 100%;
    border-collapse: collapse;
}

.setia-history-table th {
    background-color: var(--setia-gray-100);
    color: var(--setia-text-dark);
    text-align: right;
    padding: 12px 15px;
    font-weight: 600;
    border-bottom: 2px solid var(--setia-gray-300);
}

.setia-history-table td {
    padding: 12px 15px;
    border-bottom: 1px solid var(--setia-gray-200);
    vertical-align: middle;
}

.setia-history-table tr:hover {
    background-color: rgba(74, 107, 239, 0.05);
}

.setia-history-table tr:last-child td {
    border-bottom: none;
}

.setia-checkbox {
    position: relative;
    width: 18px;
    height: 18px;
    cursor: pointer;
    margin: 0 auto;
    display: block;
}

.setia-checkbox:checked {
    accent-color: var(--setia-primary);
}

.setia-tag {
    display: inline-block;
    padding: 3px 8px;
    border-radius: 15px;
    font-size: 12px;
    background-color: var(--setia-gray-200);
    color: var(--setia-text-medium);
    margin-right: 5px;
    margin-bottom: 5px;
}

.setia-tag-tone {
    background-color: rgba(74, 107, 239, 0.1);
    color: var(--setia-primary);
}

.setia-no-history {
    padding: 40px;
    text-align: center;
    background-color: var(--setia-gray-100);
    border-radius: var(--setia-border-radius);
    color: var(--setia-text-medium);
}

.setia-no-history p {
    margin: 0;
    font-size: 16px;
}

.setia-no-history .setia-icon {
    font-size: 48px;
    width: 48px;
    height: 48px;
    margin-bottom: 10px;
    color: var(--setia-gray-400);
}

.setia-tabs {
    display: flex;
    margin-bottom: 20px;
    border-bottom: 1px solid var(--setia-gray-300);
}

.setia-tab {
    padding: 10px 15px;
    cursor: pointer;
    margin-right: 5px;
    background-color: var(--setia-gray-100);
    border-radius: 8px 8px 0 0;
    color: var(--setia-text-medium);
    border: 1px solid var(--setia-gray-300);
    border-bottom: none;
    transition: all 0.2s ease;
}

.setia-tab.active {
    background-color: white;
    color: var(--setia-primary);
    border-bottom: 1px solid white;
    margin-bottom: -1px;
}

.setia-tab-content {
    display: none;
}

.setia-tab-content.active {
    display: block;
}

.setia-form {
    width: 100%;
    margin: 0 auto;
}

.setia-form-group {
    margin-bottom: 20px;
    position: relative;
}

.setia-form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: var(--setia-text-dark);
    font-size: 14px;
}

.setia-form-row {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    margin-bottom: 20px;
}

.setia-form-row .setia-form-group {
    flex: 1;
    margin-bottom: 0;
}

.setia-full-width {
    width: 100% !important;
    max-width: 100% !important;
    flex-basis: 100% !important;
    display: block !important;
}

.setia-input-container {
    position: relative;
    width: 100%;
    display: block;
}

.setia-full-width .setia-input-container {
    display: block;
    width: 100%;
}

.setia-input, 
.setia-select {
    width: 100%;
    padding: 10px;
    padding-right: var(--input-icon-width);
    border: 1px solid var(--setia-gray-300);
    border-radius: var(--setia-border-radius);
    font-size: 14px;
    color: var(--setia-text-dark);
    transition: all 0.2s;
    background-color: white;
    font-family: inherit;
}

.setia-input:focus, 
.setia-select:focus {
    outline: none;
    border-color: var(--setia-primary);
    box-shadow: 0 0 0 3px rgba(74, 107, 239, 0.15);
}

.setia-input::placeholder {
    color: var(--setia-text-light);
    padding-right: 5px;
}

.setia-input-icon {
    position: absolute;
    right: 2px;
    top: 50%;
    transform: translateY(-50%);
    width: var(--input-icon-width);
    height: calc(100% - 2px);
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: transparent;
    pointer-events: none;
}

.setia-input-icon svg {
    width: 16px;
    height: 16px;
    fill: var(--setia-gray-400);
}

#title,
#topic,
#keywords,
#category,
#tone,
#length,
#frequency,
#status {
    padding-right: 40px !important;
    direction: rtl;
    text-align: right;
}

#title::placeholder,
#topic::placeholder,
#keywords::placeholder {
    direction: rtl;
    text-align: right;
}

.setia-select {
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
}

.setia-form-actions {
    margin-top: 30px;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    border-top: 1px solid var(--setia-gray-200);
    padding-top: 20px;
}

.setia-checkbox-wrapper {
    margin-top: 20px;
    margin-bottom: 10px;
}

.setia-checkbox-label {
    display: flex;
    align-items: center;
    gap: 10px;
    cursor: pointer;
    padding: 8px;
    border-radius: var(--setia-border-radius);
    transition: background-color 0.2s;
}

.setia-checkbox-label:hover {
    background-color: var(--setia-gray-100);
}

.setia-checkbox {
    position: relative;
    width: 18px;
    height: 18px;
    cursor: pointer;
    accent-color: var(--setia-primary);
}

.setia-checkbox-text {
    font-size: 14px;
    color: var(--setia-text-dark);
}

.setia-button {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    border-radius: var(--setia-border-radius);
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s;
    background-color: var(--setia-primary) !important;
    color: white !important;
    border: none !important;
    text-decoration: none;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.setia-button:hover {
    background-color: var(--setia-primary-dark) !important;
    transform: translateY(-1px);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.setia-button:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(74, 107, 239, 0.25);
}

.setia-button:active {
    transform: translateY(1px);
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.setia-button-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

/* استایل‌های خاص برای دکمه‌های مختلف */
#cancel-edit {
    background-color: white !important;
    color: var(--setia-text-medium) !important;
    border: 1px solid var(--setia-gray-300) !important;
}

#cancel-edit:hover {
    background-color: var(--setia-gray-100) !important;
    transform: translateY(-1px);
}

.run-schedule {
    background-color: var(--setia-success) !important;
    border-color: var(--setia-success) !important;
    color: white !important;
}

.run-schedule:hover {
    background-color: #388e3c !important;
}

.setia-danger-button {
    background-color: var(--setia-error) !important;
    border-color: var(--setia-error) !important;
    color: white !important;
}

.setia-danger-button:hover {
    background-color: #d32f2f !important;
}

/* اصلاح ریسپانسیو بودن */
@media (max-width: 782px) {
    .setia-form-row {
        flex-direction: column;
        gap: 10px;
    }
}

/* استایل برای SweetAlert */
.setia-swal-popup {
    font-family: iranyekan, Tahoma, sans-serif !important;
    border-radius: var(--setia-border-radius) !important;
}

.setia-swal-title {
    font-family: iranyekan, Tahoma, sans-serif !important;
    font-weight: 600 !important;
    font-size: 18px !important;
}

.setia-swal-button {
    background-color: var(--setia-primary) !important;
    border-radius: var(--setia-border-radius) !important;
    font-family: iranyekan, Tahoma, sans-serif !important;
}

.setia-swal-danger-button {
    background-color: var(--setia-error) !important;
    border-radius: var(--setia-border-radius) !important;
    font-family: iranyekan, Tahoma, sans-serif !important;
}

.setia-swal-success-button {
    background-color: var(--setia-success) !important;
    border-radius: var(--setia-border-radius) !important;
    font-family: iranyekan, Tahoma, sans-serif !important;
}

.setia-swal-cancel-button {
    background-color: white !important;
    color: var(--setia-text-medium) !important;
    border-radius: var(--setia-border-radius) !important;
    font-family: iranyekan, Tahoma, sans-serif !important;
    border: 1px solid var(--setia-gray-300) !important;
}

/* اضافه کردن انیمیشن به المنت‌ها */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.setia-form-group, 
.setia-checkbox-wrapper, 
.setia-form-actions {
    animation: fadeIn 0.3s ease-in-out;
}

.setia-fade-in {
    animation: fadeIn 0.3s ease-in-out;
}
</style>
