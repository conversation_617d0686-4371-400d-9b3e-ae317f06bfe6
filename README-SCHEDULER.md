# راهنمای رفع مشکلات زمانبندی در افزونه SETIA

## مشکل: زمانبندی‌ها فقط زمانی اجرا می‌شوند که صفحه پیشخوان باز باشد

اگر زمانبندی‌های شما فقط زمانی اجرا می‌شوند که صفحه پیشخوان وردپرس باز باشد، احتمالاً یکی از مشکلات زیر وجود دارد:

### راه حل 1: تنظیم کرون سرور واقعی

1. فایل wp-config.php را باز کرده و خط زیر را به آن اضافه کنید:
```php
define('DISABLE_WP_CRON', true);
```

2. در سی‌پنل به بخش "Cron Jobs" بروید و یک کرون جاب جدید با فرمت زیر ایجاد کنید:
```
wget -q -O /dev/null "https://yourdomain.com/wp-content/plugins/SETIA/cron-trigger.php?key=YOUR_SECURITY_KEY" > /dev/null 2>&1
```
یا
```
curl -s "https://yourdomain.com/wp-content/plugins/SETIA/cron-trigger.php?key=YOUR_SECURITY_KEY" > /dev/null 2>&1
```

3. تناوب اجرا را بر اساس نیاز خود تنظیم کنید (مثلاً هر 5 دقیقه).

4. کلید امنیتی را از بخش "تنظیمات کرون سرور" در منوی SETIA کپی کنید و در URL بالا جایگزین کنید.

### راه حل 2: اجرای اجباری زمانبندی‌ها

اگر راه حل 1 کار نکرد، می‌توانید از ابزار اجرای اجباری زمانبندی‌ها استفاده کنید:

1. فایل `fix-scheduler.php` را از طریق مرورگر اجرا کنید:
```
https://yourdomain.com/wp-content/plugins/SETIA/fix-scheduler.php
```

2. این ابزار تمام زمانبندی‌ها را بدون توجه به وضعیت فعال یا غیرفعال، به صورت اجباری اجرا می‌کند.

### راه حل 3: بررسی لاگ‌های خطا

اگر همچنان مشکل دارید، لاگ‌های خطای سرور را بررسی کنید:

1. در سی‌پنل به بخش "Error Log" بروید.
2. یا فایل error_log را در مسیر اصلی هاست خود بررسی کنید.
3. به دنبال خطاهایی با پیشوند "SETIA CRON" بگردید.

### راه حل 4: بررسی مسیر wp-load.php

اگر کرون با خطای "فایل wp-load.php یافت نشد" مواجه می‌شود:

1. فایل `cron-path-finder.php` را از طریق مرورگر اجرا کنید:
```
https://yourdomain.com/wp-content/plugins/SETIA/cron-path-finder.php
```

2. این ابزار مسیر صحیح wp-load.php را پیدا می‌کند.

3. سپس فایل‌های `cron-trigger.php` و `internal-cron.php` را ویرایش کرده و مسیر دقیق را در آنها قرار دهید:

```php
// بارگذاری وردپرس - مسیر دقیق
$wp_load_path = '/home/<USER>/public_html/wp-load.php'; // مسیر را با مسیر واقعی جایگزین کنید
```

## مشکل: زمانبندی‌ها ثبت می‌شوند اما اجرا نمی‌شوند

اگر زمانبندی‌ها در سیستم ثبت می‌شوند اما اجرا نمی‌شوند، موارد زیر را بررسی کنید:

1. **وضعیت زمانبندی**: مطمئن شوید که زمانبندی در وضعیت "فعال" قرار دارد.

2. **تناوب زمانی**: اگر تناوب زمانی بسیار طولانی است (مثلاً هفتگی یا ماهانه)، ممکن است هنوز زمان اجرای آن نرسیده باشد.

3. **آخرین اجرا**: زمان آخرین اجرا را بررسی کنید. اگر به تازگی اجرا شده، باید منتظر بمانید تا زمان اجرای بعدی فرا برسد.

4. **اجرای دستی**: از دکمه "اجرای دستی" در صفحه زمانبندی استفاده کنید تا مطمئن شوید زمانبندی به درستی کار می‌کند.

## مشکل: محتوای تولید شده در تاریخچه نمایش داده نمی‌شود

اگر محتوای تولید شده در بخش تاریخچه نمایش داده نمی‌شود:

1. مطمئن شوید که تابع `save_to_history` در کلاس `SETIA_Scheduler` به درستی فراخوانی می‌شود.

2. جدول تاریخچه در دیتابیس را بررسی کنید تا مطمئن شوید که به درستی ایجاد شده است.

3. اگر مشکل همچنان وجود دارد، از ابزار اجرای اجباری زمانبندی‌ها استفاده کنید و لاگ‌های خطا را بررسی کنید.

## تماس با پشتیبانی

اگر همچنان با مشکل مواجه هستید، لطفاً با تیم پشتیبانی تماس بگیرید و اطلاعات زیر را ارائه دهید:
- خطای دقیق دریافت شده
- نتایج ابزار مسیریاب
- ساختار پوشه‌های سایت وردپرس شما
- نوع هاست و کنترل پنل مورد استفاده
