<?php
/**
 * SETIA Database Activation Script
 * این فایل برای فعال‌سازی مستقیم جدول دیتابیس و تنظیمات افزونه SETIA است
 * 
 * برای اجرا، این فایل را مستقیماً در مرورگر باز کنید:
 * http://your-site.com/wp-content/plugins/SETIA/db-activation.php
 */

// مسیر اصلی وردپرس را پیدا کنید
$wp_load_path = '';
$dir = dirname(__FILE__);
while ($dir != '/') {
    if (file_exists($dir . '/wp-load.php')) {
        $wp_load_path = $dir . '/wp-load.php';
        break;
    }
    $dir = dirname($dir);
}

// اگر فایل wp-load.php پیدا نشد، سعی کنید با مسیر نسبی
if (empty($wp_load_path)) {
    if (file_exists('../../../../wp-load.php')) {
        $wp_load_path = '../../../../wp-load.php';
    } elseif (file_exists('../../../wp-load.php')) {
        $wp_load_path = '../../../wp-load.php';
    } elseif (file_exists('../../wp-load.php')) {
        $wp_load_path = '../../wp-load.php';
    }
}

// بارگذاری هسته وردپرس
if (!empty($wp_load_path)) {
    require_once($wp_load_path);
} else {
    die('فایل wp-load.php پیدا نشد. لطفاً این فایل را در پوشه افزونه SETIA قرار دهید.');
}

// بررسی مجوز دسترسی (فقط مدیران مجاز به اجرا هستند)
if (!current_user_can('manage_options')) {
    die('شما اجازه دسترسی ندارید.');
}

// تابع فعال‌سازی دیتابیس
function activate_setia_database() {
    global $wpdb;
    $charset_collate = $wpdb->get_charset_collate();
    
    // جدول نگهداری تاریخچه محتوای تولید شده
    $table_name = $wpdb->prefix . 'setia_generated_content';
    
    // بررسی وجود جدول
    $table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$table_name}'") === $table_name;
    
    if (!$table_exists) {
        $sql = "CREATE TABLE $table_name (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            post_id mediumint(9) DEFAULT NULL,
            topic varchar(255) NOT NULL,
            keywords text NOT NULL,
            tone varchar(50) NOT NULL,
            category varchar(100) NOT NULL,
            length varchar(50) NOT NULL,
            generated_text longtext NOT NULL,
            generated_image_url varchar(255) DEFAULT NULL,
            seo_meta text DEFAULT NULL,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY  (id)
        ) $charset_collate;";
        
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);
        
        echo "<div style='background-color: #dff0d8; padding: 10px; margin: 10px 0; border: 1px solid #d6e9c6; border-radius: 4px;'>";
        echo "<p style='color: #3c763d;'>جدول دیتابیس {$table_name} با موفقیت ایجاد شد.</p>";
        echo "</div>";
    } else {
        echo "<div style='background-color: #fcf8e3; padding: 10px; margin: 10px 0; border: 1px solid #faebcc; border-radius: 4px;'>";
        echo "<p style='color: #8a6d3b;'>جدول دیتابیس {$table_name} از قبل وجود دارد.</p>";
        echo "</div>";
    }
    
    return $table_exists;
}

// تابع ایجاد پوشه‌های مورد نیاز
function create_setia_directories() {
    $upload_dir = wp_upload_dir();
    $setia_dir = trailingslashit($upload_dir['basedir']) . 'setia';
    
    if (!file_exists($setia_dir)) {
        wp_mkdir_p($setia_dir);
        
        // ایجاد فایل .htaccess برای امنیت بیشتر
        $htaccess_file = $setia_dir . '/.htaccess';
        $htaccess_content = "Options -Indexes\n";
        $htaccess_content .= "<Files *.php>\n";
        $htaccess_content .= "Order Allow,Deny\n";
        $htaccess_content .= "Deny from all\n";
        $htaccess_content .= "</Files>\n";
        
        file_put_contents($htaccess_file, $htaccess_content);
        
        echo "<div style='background-color: #dff0d8; padding: 10px; margin: 10px 0; border: 1px solid #d6e9c6; border-radius: 4px;'>";
        echo "<p style='color: #3c763d;'>پوشه‌های مورد نیاز افزونه با موفقیت ایجاد شدند.</p>";
        echo "</div>";
        
        return true;
    } else {
        echo "<div style='background-color: #fcf8e3; padding: 10px; margin: 10px 0; border: 1px solid #faebcc; border-radius: 4px;'>";
        echo "<p style='color: #8a6d3b;'>پوشه‌های مورد نیاز افزونه از قبل وجود دارند.</p>";
        echo "</div>";
        
        return false;
    }
}

// تابع ذخیره تنظیمات پیش‌فرض
function save_setia_default_settings() {
    $default_settings = [
        'gemini_api_key' => '',
        'gemma_api_key' => '',
        'imagine_art_api_key' => '',
        'default_tone' => 'عادی',
        'default_length' => 'متوسط',
        'enable_seo' => 'yes',
        'enable_image_generation' => 'yes'
    ];
    
    // ایجاد تنظیمات پیش‌فرض اگر وجود ندارد
    if (!get_option('setia_settings')) {
        update_option('setia_settings', $default_settings);
        
        echo "<div style='background-color: #dff0d8; padding: 10px; margin: 10px 0; border: 1px solid #d6e9c6; border-radius: 4px;'>";
        echo "<p style='color: #3c763d;'>تنظیمات پیش‌فرض افزونه با موفقیت ذخیره شدند.</p>";
        echo "</div>";
        
        return true;
    } else {
        echo "<div style='background-color: #fcf8e3; padding: 10px; margin: 10px 0; border: 1px solid #faebcc; border-radius: 4px;'>";
        echo "<p style='color: #8a6d3b;'>تنظیمات پیش‌فرض افزونه از قبل وجود دارند.</p>";
        echo "</div>";
        
        return false;
    }
}

// نمایش خروجی HTML
?>
<!DOCTYPE html>
<html dir="rtl">
<head>
    <meta charset="utf-8">
    <title>فعال‌سازی افزونه SETIA</title>
    <style>
        body {
            font-family: Tahoma, Arial, sans-serif;
            line-height: 1.6;
            padding: 20px;
            max-width: 800px;
            margin: 0 auto;
            background-color: #f9f9f9;
        }
        .container {
            background-color: #fff;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        h1 {
            color: #444;
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
        }
        .alert {
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 4px;
        }
        .alert-info {
            background-color: #d9edf7;
            border: 1px solid #bce8f1;
            color: #31708f;
        }
        .btn {
            display: inline-block;
            padding: 6px 12px;
            margin-bottom: 0;
            font-size: 14px;
            font-weight: 400;
            line-height: 1.42857143;
            text-align: center;
            white-space: nowrap;
            vertical-align: middle;
            cursor: pointer;
            background-image: none;
            border: 1px solid transparent;
            border-radius: 4px;
            text-decoration: none;
        }
        .btn-primary {
            color: #fff;
            background-color: #337ab7;
            border-color: #2e6da4;
        }
        .btn-primary:hover {
            background-color: #286090;
            border-color: #204d74;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>فعال‌سازی افزونه SETIA Content Generator</h1>
        
        <div class="alert alert-info">
            <p>این صفحه به منظور فعال‌سازی و ایجاد تنظیمات مورد نیاز افزونه SETIA Content Generator طراحی شده است.</p>
            <p>در صورتی که با خطای "خطا در انتشار محتوا: محتوایی برای انتشار یافت نشد" مواجه شده‌اید، این فعال‌سازی مشکل را برطرف خواهد کرد.</p>
        </div>
        
        <h2>نتیجه فعال‌سازی:</h2>
        
        <?php
        // اجرای توابع فعال‌سازی
        $db_result = activate_setia_database();
        $dir_result = create_setia_directories();
        $settings_result = save_setia_default_settings();
        
        // نمایش نتیجه نهایی
        if ($db_result && $dir_result && $settings_result) {
            echo "<div style='background-color: #fcf8e3; padding: 10px; margin: 20px 0; border: 1px solid #faebcc; border-radius: 4px;'>";
            echo "<p style='color: #8a6d3b;'>همه موارد مورد نیاز از قبل فعال شده‌اند.</p>";
            echo "</div>";
        } elseif (!$db_result || !$dir_result || !$settings_result) {
            echo "<div style='background-color: #dff0d8; padding: 10px; margin: 20px 0; border: 1px solid #d6e9c6; border-radius: 4px;'>";
            echo "<p style='color: #3c763d;'>فعال‌سازی افزونه SETIA با موفقیت انجام شد. اکنون می‌توانید به داشبورد وردپرس برگردید و از افزونه استفاده کنید.</p>";
            echo "</div>";
        }
        ?>
        
        <p>
            <a href="<?php echo admin_url('admin.php?page=setia-content-generator'); ?>" class="btn btn-primary">رفتن به صفحه اصلی افزونه</a>
            <a href="<?php echo admin_url(); ?>" class="btn btn-primary">بازگشت به داشبورد</a>
        </p>
        
        <div style="margin-top: 30px; padding-top: 10px; border-top: 1px solid #eee; font-size: 12px; color: #777;">
            <p>این صفحه پس از فعال‌سازی موفق می‌تواند حذف شود.</p>
        </div>
    </div>
</body>
</html> 