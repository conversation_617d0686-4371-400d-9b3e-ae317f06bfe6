<!DOCTYPE html>
<html lang="fa" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SETIA Layout Test</title>
    <style>
        /* Simulate WordPress admin environment */
        body.wp-admin {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            background: #f0f0f1;
            margin: 0;
            padding: 0;
        }
        
        /* Load our CSS inline for testing */
        @import url('assets/css/admin-settings.css');
        
        /* Test container */
        .test-container {
            max-width: 1200px;
            margin: 20px auto;
            padding: 20px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 2px solid #ddd;
            border-radius: 8px;
        }
        
        .test-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
        }
        
        .test-description {
            font-size: 14px;
            color: #666;
            margin-bottom: 20px;
        }
        
        /* Responsive test indicators */
        .responsive-indicator {
            position: fixed;
            top: 10px;
            left: 10px;
            background: #007cba;
            color: white;
            padding: 5px 10px;
            border-radius: 4px;
            font-size: 12px;
            z-index: 10000;
        }
        
        @media (max-width: 1200px) {
            .responsive-indicator::after {
                content: " - TABLET";
            }
        }
        
        @media (max-width: 768px) {
            .responsive-indicator::after {
                content: " - MOBILE";
            }
        }
    </style>
</head>
<body class="wp-admin">
    <div class="responsive-indicator">LAYOUT TEST</div>
    
    <div class="test-container">
        <h1>SETIA Settings Layout Test</h1>
        <p>This page tests the layout fixes for form field alignment and RTL support.</p>
        
        <div class="test-section">
            <div class="test-title">1. Form Field Alignment Test</div>
            <div class="test-description">Check if form fields are properly aligned and not overlapping</div>
            
            <!-- Simulate SETIA settings structure -->
            <div class="wrap setia-settings">
                <div class="setia-settings-container">
                    <div class="setia-section setia-card">
                        <div class="setia-section-content">
                            <div class="setia-form-group">
                                <label for="test-input-1" class="setia-label">
                                    <span class="label-icon">🔑</span>
                                    کلید API تست
                                    <span class="required-indicator">*</span>
                                </label>
                                <div class="setia-input-wrapper">
                                    <input type="text" id="test-input-1" class="setia-input" placeholder="مقدار تست را وارد کنید" value="نمونه متن فارسی">
                                </div>
                            </div>
                            
                            <div class="setia-form-group">
                                <label for="test-select-1" class="setia-label">
                                    <span class="label-icon">⚙️</span>
                                    انتخاب گزینه
                                </label>
                                <div class="setia-input-wrapper">
                                    <select id="test-select-1" class="setia-select">
                                        <option value="">انتخاب کنید</option>
                                        <option value="option1">گزینه اول</option>
                                        <option value="option2">گزینه دوم</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <div class="test-title">2. RTL Layout Test</div>
            <div class="test-description">Verify Persian/Farsi text direction and alignment</div>
            
            <div class="wrap setia-settings">
                <div class="setia-settings-container">
                    <div class="setia-section setia-card">
                        <div class="setia-section-content">
                            <div class="setia-form-group">
                                <label class="setia-label">
                                    متن فارسی باید از راست به چپ نمایش داده شود
                                </label>
                                <div class="setia-input-wrapper">
                                    <input type="text" class="setia-input" value="این متن باید در سمت راست قرار گیرد">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <div class="test-title">3. Responsive Design Test</div>
            <div class="test-description">Resize browser window to test responsive behavior</div>
            
            <div class="wrap setia-settings">
                <div class="setia-settings-container">
                    <div class="setia-section setia-card">
                        <div class="setia-section-content">
                            <div class="setia-form-group">
                                <label class="setia-label">فیلد ریسپانسیو</label>
                                <div class="setia-input-wrapper">
                                    <input type="text" class="setia-input" placeholder="این فیلد باید در اندازه‌های مختلف صفحه به درستی نمایش داده شود">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // Test script to verify layout
        document.addEventListener('DOMContentLoaded', function() {
            console.log('SETIA Layout Test Loaded');
            
            // Check if CSS is loading
            const testElement = document.querySelector('.wrap.setia-settings');
            if (testElement) {
                const computedStyle = window.getComputedStyle(testElement);
                console.log('Direction:', computedStyle.direction);
                console.log('Font Family:', computedStyle.fontFamily);
                console.log('Background:', computedStyle.background);
            }
            
            // Test form field alignment
            const inputs = document.querySelectorAll('.setia-input');
            inputs.forEach((input, index) => {
                const rect = input.getBoundingClientRect();
                console.log(`Input ${index + 1} position:`, {
                    left: rect.left,
                    right: rect.right,
                    width: rect.width
                });
            });
        });
    </script>
</body>
</html>
