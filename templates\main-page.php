<?php
// امنیت: جلوگیری از دسترسی مستقیم
if (!defined('ABSPATH')) {
    exit;
}

// دریافت تنظیمات برای مقادیر پیش‌فرض تصویر
$setia_settings = get_option('setia_settings', array());
$default_image_style = isset($setia_settings['default_image_style']) ? $setia_settings['default_image_style'] : 'realistic';
$default_aspect_ratio = isset($setia_settings['default_aspect_ratio']) ? $setia_settings['default_aspect_ratio'] : '16:9';
?>
<div class="wrap setia-main-page">
    <div class="setia-main-header">
        <div class="setia-header-content">
            <div class="setia-header-icon">
                <svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M12 2L2 7L12 12L22 7L12 2Z" stroke="currentColor" stroke-width="2" stroke-linejoin="round"/>
                    <path d="M2 17L12 22L22 17" stroke="currentColor" stroke-width="2" stroke-linejoin="round"/>
                    <path d="M2 12L12 17L22 12" stroke="currentColor" stroke-width="2" stroke-linejoin="round"/>
                </svg>
            </div>
            <div class="setia-header-text">
                <h1>تولید محتوای هوشمند با هوش مصنوعی</h1>
                <p>با استفاده از قدرت هوش مصنوعی، محتوای باکیفیت و بهینه‌شده برای سئو تولید کنید</p>
            </div>
        </div>
        <div class="setia-progress-indicator">
            <div class="progress-step active" data-step="1">
                <span class="step-number">1</span>
                <span class="step-title">مشخصات محتوا</span>
            </div>
            <div class="progress-step" data-step="2">
                <span class="step-number">2</span>
                <span class="step-title">تولید محتوا</span>
            </div>
            <div class="progress-step" data-step="3">
                <span class="step-number">3</span>
                <span class="step-title">بررسی و انتشار</span>
            </div>
        </div>

        <!-- تب‌های اصلی -->
        <div class="setia-main-tabs">
            <button class="setia-main-tab-button active" data-main-tab="content">
                <span class="tab-icon">📄</span>
                تولید محتوا
            </button>
            <button class="setia-main-tab-button" data-main-tab="product">
                <span class="tab-icon">🛍️</span>
                تولید محصول
            </button>
        </div>
    </div>

    <div class="setia-container">
        <!-- محتوای تب تولید محتوا -->
        <div class="setia-main-tab-content active" id="content-main-tab">
        <div class="setia-form-container setia-card">
            <div class="setia-card-header">
                <div class="setia-card-icon">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M14 2H6C5.46957 2 4.96086 2.21071 4.58579 2.58579C4.21071 2.96086 4 3.46957 4 4V20C4 20.5304 4.21071 21.0391 4.58579 21.4142C4.96086 21.7893 5.46957 22 6 22H18C18.5304 22 19.0391 21.7893 19.4142 21.4142C19.7893 21.0391 20 20.5304 20 20V8L14 2Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        <path d="M14 2V8H20" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        <path d="M16 13H8" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        <path d="M16 17H8" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        <path d="M10 9H9H8" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                </div>
                <div class="setia-card-title">
                    <h2>مشخصات محتوا</h2>
                    <p>اطلاعات زیر را تکمیل کنید تا محتوای متناسب با نیاز شما تولید شود</p>
                </div>
            </div>
                <form id="setia-content-form" method="post">
                    <?php wp_nonce_field('setia-nonce', 'setia_nonce'); ?>

                    <div class="setia-form-grid">
                        <div class="setia-form-group">
                            <label for="setia-topic" class="setia-label">
                                <span class="label-icon">💡</span>
                                موضوع اصلی
                                <span class="required-indicator">*</span>
                            </label>
                            <div class="setia-input-wrapper">
                                <input type="text" id="setia-topic" name="topic" required placeholder="موضوع اصلی مقاله را وارد کنید" class="setia-input">
                                <div class="input-validation" id="topic-validation"></div>
                            </div>
                            <p class="setia-field-description">مثال: مزایای ورزش صبحگاهی</p>
                        </div>

                        <div class="setia-form-group">
                            <label for="setia-keywords" class="setia-label">
                                <span class="label-icon">🔍</span>
                                کلمات کلیدی
                                <span class="required-indicator">*</span>
                            </label>
                            <div class="setia-input-wrapper">
                                <input type="text" id="setia-keywords" name="keywords" required placeholder="کلمات کلیدی را با کاما جدا کنید" class="setia-input">
                                <div class="input-validation" id="keywords-validation"></div>
                            </div>
                            <p class="setia-field-description">مثال: ورزش صبحگاهی، سلامتی، تناسب اندام</p>
                            <div class="keywords-counter">
                                <span id="keywords-count">0</span> کلمه کلیدی
                            </div>
                        </div>
                    </div>

                    <div class="setia-form-grid setia-form-grid-3">
                        <div class="setia-form-group">
                            <label for="setia-tone" class="setia-label">
                                <span class="label-icon">🎭</span>
                                لحن محتوا
                            </label>
                            <select id="setia-tone" name="tone" class="setia-select">
                                <option value="عادی">عادی</option>
                                <option value="رسمی">رسمی</option>
                                <option value="دوستانه">دوستانه</option>
                                <option value="آموزشی">آموزشی</option>
                                <option value="طنز">طنز</option>
                            </select>
                        </div>

                        <div class="setia-form-group">
                            <label for="setia-category" class="setia-label">
                                <span class="label-icon">📁</span>
                                دسته‌بندی
                            </label>
                            <select id="setia-category" name="category" class="setia-select">
                                <?php
                                $categories = get_categories(array('hide_empty' => 0));
                                foreach ($categories as $category) {
                                    echo '<option value="' . esc_attr($category->term_id) . '">' . esc_html($category->name) . '</option>';
                                }
                                ?>
                            </select>
                        </div>

                        <div class="setia-form-group">
                            <label for="setia-length" class="setia-label">
                                <span class="label-icon">📏</span>
                                طول مطلب
                            </label>
                            <select id="setia-length" name="length" class="setia-select">
                                <option value="کوتاه">کوتاه (۵۰۰ کلمه)</option>
                                <option value="متوسط" selected>متوسط (۱۰۰۰ کلمه)</option>
                                <option value="بلند">بلند (۱۵۰۰ کلمه)</option>
                                <option value="خیلی بلند">خیلی بلند (۲۰۰۰ کلمه)</option>
                            </select>
                        </div>
                    </div>

                    <div class="setia-options-section">
                        <h3 class="setia-subsection-title">
                            <span class="subsection-icon">⚙️</span>
                            گزینه‌های پیشرفته
                        </h3>

                        <div class="setia-toggle-grid">
                            <div class="setia-toggle-item">
                                <div class="setia-toggle-wrapper">
                                    <input type="checkbox" id="setia-seo" name="seo" value="yes" checked class="setia-toggle">
                                    <label for="setia-seo" class="setia-toggle-label">
                                        <span class="toggle-slider"></span>
                                    </label>
                                </div>
                                <div class="setia-toggle-content">
                                    <h4 class="toggle-title">
                                        <span class="toggle-icon">🔍</span>
                                        بهینه‌سازی سئو
                                    </h4>
                                    <p class="toggle-description">تولید متاتگ‌ها و بهینه‌سازی محتوا برای موتورهای جستجو</p>
                                </div>
                            </div>

                            <div class="setia-toggle-item">
                                <div class="setia-toggle-wrapper">
                                    <input type="checkbox" id="setia-image" name="generate_image" value="yes" checked class="setia-toggle">
                                    <label for="setia-image" class="setia-toggle-label">
                                        <span class="toggle-slider"></span>
                                    </label>
                                </div>
                                <div class="setia-toggle-content">
                                    <h4 class="toggle-title">
                                        <span class="toggle-icon">🖼️</span>
                                        تولید تصویر شاخص
                                    </h4>
                                    <p class="toggle-description">تولید تصویر مرتبط با موضوع به عنوان تصویر شاخص</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div id="setia-image-options-container" class="setia-image-options-container" style="display: none;">
                        <div class="setia-image-options-header">
                            <h3 class="setia-subsection-title">
                                <span class="subsection-icon">🎨</span>
                                تنظیمات تولید تصویر
                            </h3>
                            <p class="subsection-description">تنظیمات دقیق برای تولید تصویر شاخص مقاله</p>
                        </div>

                        <div class="setia-form-grid setia-form-grid-2">
                            <div class="setia-form-group">
                                <label for="setia-image-style" class="setia-label">
                                    <span class="label-icon">🎭</span>
                                    استایل تصویر
                                </label>
                                <select id="setia-image-style" name="image_style" class="setia-select">
                                    <option value="realistic" <?php selected($default_image_style, 'realistic'); ?>>واقع‌گرایانه (Realistic)</option>
                                    <option value="anime" <?php selected($default_image_style, 'anime'); ?>>انیمه (Anime)</option>
                                    <option value="flux-schnell" <?php selected($default_image_style, 'flux-schnell'); ?>>Flux Schnell</option>
                                    <option value="flux-dev-fast" <?php selected($default_image_style, 'flux-dev-fast'); ?>>Flux Dev Fast</option>
                                    <option value="flux-dev" <?php selected($default_image_style, 'flux-dev'); ?>>Flux Dev</option>
                                    <option value="imagine-turbo" <?php selected($default_image_style, 'imagine-turbo'); ?>>Imagine Turbo</option>
                                </select>
                                <p class="setia-field-description">هر سبک برای کاربرد خاصی بهینه شده است</p>
                            </div>

                            <div class="setia-form-group">
                                <label for="setia-aspect-ratio" class="setia-label">
                                    <span class="label-icon">📐</span>
                                    ابعاد تصویر
                                </label>
                                <select id="setia-aspect-ratio" name="aspect_ratio" class="setia-select">
                                    <option value="1:1" <?php selected($default_aspect_ratio, '1:1'); ?>>مربع (1:1)</option>
                                    <option value="16:9" <?php selected($default_aspect_ratio, '16:9'); ?>>عریض (16:9)</option>
                                    <option value="9:16" <?php selected($default_aspect_ratio, '9:16'); ?>>عمودی (9:16)</option>
                                    <option value="4:3" <?php selected($default_aspect_ratio, '4:3'); ?>>تلویزیونی (4:3)</option>
                                    <option value="3:4" <?php selected($default_aspect_ratio, '3:4'); ?>>پرتره (3:4)</option>
                                </select>
                            </div>
                        </div>

                        <div class="setia-form-group">
                            <label for="setia-negative-prompt" class="setia-label">
                                <span class="label-icon">🚫</span>
                                پرامپت منفی (اختیاری)
                            </label>
                            <input type="text" id="setia-negative-prompt" name="negative_prompt" placeholder="مواردی که نمی‌خواهید در تصویر باشند" class="setia-input">
                            <p class="setia-field-description">مثال: متن، امضا، کیفیت پایین</p>
                        </div>

                        <div class="setia-form-group">
                            <label for="image_prompt_details" class="setia-label">
                                <span class="label-icon">📝</span>
                                جزئیات بیشتر برای تصویر (اختیاری)
                            </label>
                            <textarea id="image_prompt_details" name="image_prompt_details" rows="3" placeholder="جزئیات دقیق‌تری که می‌خواهید در تصویر لحاظ شود" class="setia-textarea"></textarea>
                            <p class="setia-field-description">مثال: یک مرد در حال دویدن در پارک هنگام طلوع آفتاب</p>
                        </div>
                    </div>

                    <div class="setia-form-group">
                        <label for="setia-instructions" class="setia-label">
                            <span class="label-icon">📋</span>
                            دستورالعمل‌های اضافی
                        </label>
                        <textarea id="setia-instructions" name="instructions" rows="4" placeholder="دستورالعمل‌های خاص برای تولید محتوا (اختیاری)" class="setia-textarea"></textarea>
                        <p class="setia-field-description">هر نکته‌ای که می‌خواهید در تولید محتوا رعایت شود را اینجا ذکر کنید</p>
                        <div class="character-counter">
                            <span id="instructions-count">0</span> / 500 کاراکتر
                        </div>
                    </div>

                    <div class="setia-form-actions">
                        <button type="submit" id="setia-generate-btn" name="generate_content" class="setia-button setia-button-primary setia-button-large">
                            <span class="button-icon">✨</span>
                            <span class="button-text">تولید محتوا</span>
                            <span class="button-loader" style="display: none;">
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M12 2V6M12 18V22M4.93 4.93L7.76 7.76M16.24 16.24L19.07 19.07M2 12H6M18 12H22M4.93 19.07L7.76 16.24M16.24 7.76L19.07 4.93" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                </svg>
                            </span>
                        </button>

                        <div class="setia-generation-info">
                            <div class="info-item">
                                <span class="info-icon">⏱️</span>
                                <span class="info-text">زمان تقریبی: 30-60 ثانیه</span>
                            </div>
                            <div class="info-item">
                                <span class="info-icon">🤖</span>
                                <span class="info-text">قدرت گرفته از Gemini AI</span>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <div id="setia-result-container" class="setia-result-container setia-card" style="display: none;">
            <div class="setia-result-header">
                <div class="setia-result-icon">
                    <svg width="32" height="32" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M9 12L11 14L15 10M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                </div>
                <div class="setia-result-title">
                    <h2>محتوای تولید شده</h2>
                    <p>محتوای شما با موفقیت تولید شد و آماده بررسی است</p>
                </div>
                <div class="setia-result-stats">
                    <div class="stat-item">
                        <span class="stat-value" id="content-word-count">0</span>
                        <span class="stat-label">کلمه</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-value" id="content-read-time">0</span>
                        <span class="stat-label">دقیقه مطالعه</span>
                    </div>
                </div>
            </div>

            <div class="setia-result-tabs">
                <button class="setia-tab-button active" data-tab="content">
                    <span class="tab-icon">📄</span>
                    محتوا
                </button>
                <button class="setia-tab-button" data-tab="seo">
                    <span class="tab-icon">🔍</span>
                    سئو
                </button>
                <button class="setia-tab-button" data-tab="image">
                    <span class="tab-icon">🖼️</span>
                    تصویر
                </button>
            </div>

            <div class="setia-tab-content active" id="content-tab">
                <div class="setia-optimized-title-container" style="display: none;">
                    <div class="setia-title-badge">عنوان بهینه‌سازی شده</div>
                    <h3 id="setia-optimized-title" class="setia-optimized-title"></h3>
                </div>

                <div id="setia-content-preview" class="setia-content-preview"></div>
            </div>

            <div class="setia-tab-content" id="seo-tab">
                <div class="setia-seo-preview">
                    <h3 class="seo-preview-title">پیش‌نمایش در نتایج جستجو</h3>
                    <div class="setia-seo-card">
                        <div id="setia-seo-title" class="setia-seo-title"></div>
                        <div id="setia-seo-url" class="setia-seo-url">https://yoursite.com/sample-post</div>
                        <div id="setia-seo-description" class="setia-seo-description"></div>
                    </div>

                    <div class="seo-metrics">
                        <div class="seo-metric">
                            <span class="metric-label">کلمات کلیدی:</span>
                            <div id="setia-seo-keywords" class="setia-seo-keywords"></div>
                        </div>
                        <div class="seo-metric">
                            <span class="metric-label">طول متا توضیحات:</span>
                            <div id="setia-seo-meta-length" class="setia-seo-meta-length"></div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="setia-tab-content" id="image-tab">
                <div class="setia-image-section">
                    <h3 class="image-section-title">تصویر شاخص تولید شده</h3>
                    <div id="setia-image-preview-container" class="setia-image-preview-container"></div>
                </div>
            </div>

            <div id="setia-content-result" class="setia-content-result">
                <!-- نتایج تحلیل SEO و سایر بخش‌ها اینجا اضافه می‌شوند -->
            </div>

            <div class="setia-result-actions">
                <div class="primary-actions">
                    <button id="setia-publish-btn" class="setia-button setia-button-primary" disabled>
                        <span class="button-icon">📝</span>
                        <span class="button-text">انتشار پست</span>
                    </button>
                    <button id="setia-draft-btn" class="setia-button setia-button-success" disabled>
                        <span class="button-icon">💾</span>
                        <span class="button-text">ذخیره پیش‌نویس</span>
                    </button>
                </div>
                <div class="secondary-actions">
                    <button id="setia-regenerate-btn" class="setia-button setia-button-secondary" disabled>
                        <span class="button-icon">🔄</span>
                        <span class="button-text">تولید مجدد</span>
                    </button>
                    <button id="setia-copy-btn" class="setia-button setia-button-secondary" disabled>
                        <span class="button-icon">📋</span>
                        <span class="button-text">کپی محتوا</span>
                    </button>
                    <button id="setia-export-btn" class="setia-button setia-button-secondary" disabled>
                        <span class="button-icon">📤</span>
                        <span class="button-text">خروجی Word</span>
                    </button>
                </div>
            </div>
        </div>
        </div> <!-- پایان تب تولید محتوا -->

        <!-- محتوای تب تولید محصول -->
        <div class="setia-main-tab-content" id="product-main-tab">
            <div class="setia-form-container setia-card">
                <div class="setia-card-header">
                    <div class="setia-card-icon">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M3 3H5L5.4 5M7 13H17L21 5H5.4M7 13L5.4 5M7 13L4.7 15.3C4.3 15.7 4.6 16.5 5.1 16.5H17M17 13V17C17 17.6 16.6 18 16 18H8C7.4 18 7 17.6 7 17V13M9 19.5C9.8 19.5 10.5 20.2 10.5 21S9.8 22.5 9 22.5 7.5 21.8 7.5 21 8.2 19.5 9 19.5ZM20 19.5C20.8 19.5 21.5 20.2 21.5 21S20.8 22.5 20 22.5 18.5 21.8 18.5 21 19.2 19.5 20 19.5Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    </div>
                    <div class="setia-card-title">
                        <h2>تولید محصول WooCommerce</h2>
                        <p>محصول کامل با توضیحات، تصاویر و مشخصات فنی تولید کنید</p>
                    </div>
                </div>

                <!-- بررسی فعال بودن WooCommerce -->
                <?php if (!class_exists('WooCommerce')): ?>
                <div class="setia-woocommerce-notice">
                    <div class="notice-icon">⚠️</div>
                    <div class="notice-content">
                        <h3>افزونه WooCommerce مورد نیاز است</h3>
                        <p>برای استفاده از قابلیت تولید محصول، ابتدا افزونه WooCommerce را نصب و فعال کنید.</p>
                        <a href="<?php echo admin_url('plugin-install.php?s=woocommerce&tab=search&type=term'); ?>" class="setia-button setia-button-primary">
                            نصب WooCommerce
                        </a>
                    </div>
                </div>
                <?php else: ?>

                <form id="setia-product-form" method="post">
                    <?php wp_nonce_field('setia-product-nonce', 'setia_product_nonce'); ?>

                    <div class="setia-form-grid">
                        <div class="setia-form-group">
                            <label for="setia-product-name" class="setia-label">
                                <span class="label-icon">🛍️</span>
                                نام محصول
                                <span class="required-indicator">*</span>
                            </label>
                            <div class="setia-input-wrapper">
                                <input type="text" id="setia-product-name" name="product_name" required placeholder="مثال: مانیتور ال‌جی مدل 27UP850-W" class="setia-input">
                                <div class="input-validation" id="product-name-validation"></div>
                            </div>
                            <p class="setia-field-description">نام کامل محصول را وارد کنید</p>
                        </div>
                    </div>

                    <div class="setia-form-grid setia-form-grid-3">
                        <div class="setia-form-group">
                            <label for="setia-product-category" class="setia-label">
                                <span class="label-icon">📁</span>
                                دسته‌بندی محصول
                            </label>
                            <select id="setia-product-category" name="product_category" class="setia-select">
                                <option value="">انتخاب خودکار</option>
                                <?php
                                if (taxonomy_exists('product_cat')) {
                                    $product_categories = get_terms(array(
                                        'taxonomy' => 'product_cat',
                                        'hide_empty' => false
                                    ));
                                    foreach ($product_categories as $category) {
                                        echo '<option value="' . esc_attr($category->term_id) . '">' . esc_html($category->name) . '</option>';
                                    }
                                }
                                ?>
                            </select>
                        </div>

                        <div class="setia-form-group">
                            <label for="setia-product-status" class="setia-label">
                                <span class="label-icon">📊</span>
                                وضعیت انتشار
                            </label>
                            <select id="setia-product-status" name="product_status" class="setia-select">
                                <option value="draft">پیش‌نویس</option>
                                <option value="publish">منتشر شده</option>
                            </select>
                        </div>

                        <div class="setia-form-group">
                            <label for="setia-product-images-count" class="setia-label">
                                <span class="label-icon">🖼️</span>
                                تعداد تصاویر
                            </label>
                            <select id="setia-product-images-count" name="product_images_count" class="setia-select">
                                <option value="1">1 تصویر</option>
                                <option value="2" selected>2 تصویر</option>
                                <option value="3">3 تصویر</option>
                                <option value="4">4 تصویر</option>
                                <option value="5">5 تصویر</option>
                            </select>
                        </div>
                    </div>

                    <div class="setia-options-section">
                        <h3 class="setia-subsection-title">
                            <span class="subsection-icon">⚙️</span>
                            گزینه‌های پیشرفته
                        </h3>

                        <div class="setia-toggle-grid">
                            <div class="setia-toggle-item">
                                <div class="setia-toggle-wrapper">
                                    <input type="checkbox" id="setia-product-auto-price" name="auto_price" value="yes" checked class="setia-toggle">
                                    <label for="setia-product-auto-price" class="setia-toggle-label">
                                        <span class="toggle-slider"></span>
                                    </label>
                                </div>
                                <div class="setia-toggle-content">
                                    <h4 class="toggle-title">
                                        <span class="toggle-icon">💰</span>
                                        تولید قیمت خودکار
                                    </h4>
                                    <p class="toggle-description">تولید قیمت مناسب بر اساس نوع محصول</p>
                                </div>
                            </div>

                            <div class="setia-toggle-item">
                                <div class="setia-toggle-wrapper">
                                    <input type="checkbox" id="setia-product-auto-sku" name="auto_sku" value="yes" checked class="setia-toggle">
                                    <label for="setia-product-auto-sku" class="setia-toggle-label">
                                        <span class="toggle-slider"></span>
                                    </label>
                                </div>
                                <div class="setia-toggle-content">
                                    <h4 class="toggle-title">
                                        <span class="toggle-icon">🏷️</span>
                                        تولید SKU خودکار
                                    </h4>
                                    <p class="toggle-description">ایجاد کد محصول منحصر به فرد</p>
                                </div>
                            </div>

                            <div class="setia-toggle-item">
                                <div class="setia-toggle-wrapper">
                                    <input type="checkbox" id="setia-product-auto-tags" name="auto_tags" value="yes" checked class="setia-toggle">
                                    <label for="setia-product-auto-tags" class="setia-toggle-label">
                                        <span class="toggle-slider"></span>
                                    </label>
                                </div>
                                <div class="setia-toggle-content">
                                    <h4 class="toggle-title">
                                        <span class="toggle-icon">🏷️</span>
                                        تولید برچسب‌ها
                                    </h4>
                                    <p class="toggle-description">اضافه کردن برچسب‌های مرتبط</p>
                                </div>
                            </div>

                            <div class="setia-toggle-item">
                                <div class="setia-toggle-wrapper">
                                    <input type="checkbox" id="setia-product-schema" name="product_schema" value="yes" checked class="setia-toggle">
                                    <label for="setia-product-schema" class="setia-toggle-label">
                                        <span class="toggle-slider"></span>
                                    </label>
                                </div>
                                <div class="setia-toggle-content">
                                    <h4 class="toggle-title">
                                        <span class="toggle-icon">🔍</span>
                                        Schema Markup
                                    </h4>
                                    <p class="toggle-description">تولید خودکار Schema نوع Product</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="setia-form-actions">
                        <button type="submit" id="setia-generate-product-btn" class="setia-button setia-button-primary setia-button-large">
                            <span class="button-icon">🚀</span>
                            <span class="button-text">تولید محصول</span>
                            <div class="button-loader"></div>
                        </button>
                    </div>
                </form>
                <?php endif; ?>
            </div>

            <!-- نتایج تولید محصول -->
            <div id="setia-product-results" class="setia-results-container" style="display: none;">
                <div class="setia-results-header">
                    <div class="results-icon">
                        <svg width="32" height="32" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M9 12L11 14L15 10M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    </div>
                    <div class="results-content">
                        <h2>محصول با موفقیت تولید شد!</h2>
                        <p>محصول شما آماده بررسی و انتشار است</p>
                    </div>
                </div>

                <div class="setia-result-tabs">
                    <button class="setia-tab-button active" data-tab="product-info">
                        <span class="tab-icon">📦</span>
                        اطلاعات محصول
                    </button>
                    <button class="setia-tab-button" data-tab="product-images">
                        <span class="tab-icon">🖼️</span>
                        تصاویر
                    </button>
                    <button class="setia-tab-button" data-tab="product-schema">
                        <span class="tab-icon">🔍</span>
                        Schema
                    </button>
                </div>

                <div class="setia-tab-content active" id="product-info-tab">
                    <div class="setia-product-preview">
                        <h3 class="product-preview-title">پیش‌نمایش محصول</h3>
                        <div id="setia-product-preview-container" class="setia-product-preview-container"></div>
                    </div>
                </div>

                <div class="setia-tab-content" id="product-images-tab">
                    <div class="setia-product-images-section">
                        <h3 class="images-section-title">تصاویر تولید شده</h3>
                        <div id="setia-product-images-container" class="setia-product-images-container"></div>
                    </div>
                </div>

                <div class="setia-tab-content" id="product-schema-tab">
                    <div class="setia-product-schema-section">
                        <h3 class="schema-section-title">Schema Markup تولید شده</h3>
                        <div id="setia-product-schema-container" class="setia-product-schema-container"></div>
                    </div>
                </div>

                <div class="setia-result-actions">
                    <div class="primary-actions">
                        <button id="setia-view-product-btn" class="setia-button setia-button-primary" disabled>
                            <span class="button-icon">👁️</span>
                            <span class="button-text">مشاهده محصول</span>
                        </button>
                        <button id="setia-edit-product-btn" class="setia-button setia-button-success" disabled>
                            <span class="button-icon">✏️</span>
                            <span class="button-text">ویرایش محصول</span>
                        </button>
                    </div>
                    <div class="secondary-actions">
                        <button id="setia-regenerate-product-btn" class="setia-button setia-button-secondary" disabled>
                            <span class="button-icon">🔄</span>
                            <span class="button-text">تولید مجدد</span>
                        </button>
                        <button id="setia-duplicate-product-btn" class="setia-button setia-button-secondary" disabled>
                            <span class="button-icon">📋</span>
                            <span class="button-text">کپی محصول</span>
                        </button>
                    </div>
                </div>
            </div>
        </div> <!-- پایان تب تولید محصول -->
    </div>
</div>