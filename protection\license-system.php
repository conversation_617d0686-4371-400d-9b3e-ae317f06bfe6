<?php
/**
 * SETIA License Verification System
 * Advanced licensing and anti-piracy protection
 */

if (!defined('ABSPATH')) {
    exit;
}

class SETIA_License_System {
    
    private $license_server_url = 'https://your-license-server.com/api/verify';
    private $plugin_slug = 'setia-content-generator';
    private $plugin_version = '1.0.0';
    private $license_option = 'setia_license_data';
    private $last_check_option = 'setia_last_license_check';
    
    public function __construct() {
        add_action('admin_init', array($this, 'check_license_status'));
        add_action('admin_notices', array($this, 'license_notices'));
        add_action('wp_ajax_setia_activate_license', array($this, 'activate_license'));
        add_action('wp_ajax_setia_deactivate_license', array($this, 'deactivate_license'));
        
        // Check license every 24 hours
        add_action('setia_daily_license_check', array($this, 'daily_license_check'));
        if (!wp_next_scheduled('setia_daily_license_check')) {
            wp_schedule_event(time(), 'daily', 'setia_daily_license_check');
        }
    }
    
    /**
     * Check if plugin is licensed
     */
    public function is_licensed() {
        $license_data = get_option($this->license_option, array());
        
        if (empty($license_data['license_key']) || empty($license_data['status'])) {
            return false;
        }
        
        return $license_data['status'] === 'valid';
    }
    
    /**
     * Get license data
     */
    public function get_license_data() {
        return get_option($this->license_option, array());
    }
    
    /**
     * Activate license
     */
    public function activate_license() {
        if (!wp_verify_nonce($_POST['nonce'], 'setia_license_nonce')) {
            wp_send_json_error('Security check failed');
            return;
        }
        
        $license_key = sanitize_text_field($_POST['license_key']);
        $email = sanitize_email($_POST['email']);
        
        if (empty($license_key) || empty($email)) {
            wp_send_json_error('License key and email are required');
            return;
        }
        
        $response = $this->verify_license_with_server($license_key, $email, 'activate');
        
        if ($response['success']) {
            $license_data = array(
                'license_key' => $license_key,
                'email' => $email,
                'status' => 'valid',
                'expires' => $response['expires'],
                'activations_left' => $response['activations_left'],
                'last_check' => time()
            );
            
            update_option($this->license_option, $license_data);
            update_option($this->last_check_option, time());
            
            wp_send_json_success('License activated successfully');
        } else {
            wp_send_json_error($response['message']);
        }
    }
    
    /**
     * Deactivate license
     */
    public function deactivate_license() {
        if (!wp_verify_nonce($_POST['nonce'], 'setia_license_nonce')) {
            wp_send_json_error('Security check failed');
            return;
        }
        
        $license_data = $this->get_license_data();
        
        if (!empty($license_data['license_key'])) {
            $this->verify_license_with_server($license_data['license_key'], $license_data['email'], 'deactivate');
        }
        
        delete_option($this->license_option);
        delete_option($this->last_check_option);
        
        wp_send_json_success('License deactivated successfully');
    }
    
    /**
     * Verify license with remote server
     */
    private function verify_license_with_server($license_key, $email, $action = 'check') {
        $site_url = get_site_url();
        $site_hash = hash('sha256', $site_url);
        
        $request_data = array(
            'license_key' => $license_key,
            'email' => $email,
            'site_url' => $site_url,
            'site_hash' => $site_hash,
            'plugin_slug' => $this->plugin_slug,
            'plugin_version' => $this->plugin_version,
            'action' => $action,
            'timestamp' => time()
        );
        
        // Add security signature
        $request_data['signature'] = $this->generate_request_signature($request_data);
        
        $response = wp_remote_post($this->license_server_url, array(
            'timeout' => 15,
            'body' => $request_data,
            'headers' => array(
                'User-Agent' => 'SETIA/' . $this->plugin_version . '; ' . $site_url
            )
        ));
        
        if (is_wp_error($response)) {
            return array(
                'success' => false,
                'message' => 'Unable to connect to license server: ' . $response->get_error_message()
            );
        }
        
        $body = wp_remote_retrieve_body($response);
        $data = json_decode($body, true);
        
        if (empty($data)) {
            return array(
                'success' => false,
                'message' => 'Invalid response from license server'
            );
        }
        
        return $data;
    }
    
    /**
     * Generate request signature for security
     */
    private function generate_request_signature($data) {
        $string_to_sign = '';
        ksort($data);
        
        foreach ($data as $key => $value) {
            if ($key !== 'signature') {
                $string_to_sign .= $key . '=' . $value . '&';
            }
        }
        
        $secret_key = 'SETIA_LICENSE_SECRET_2024'; // This should be unique per installation
        return hash_hmac('sha256', rtrim($string_to_sign, '&'), $secret_key);
    }
    
    /**
     * Check license status
     */
    public function check_license_status() {
        // Only check on plugin pages
        if (!$this->is_plugin_page()) {
            return;
        }
        
        $license_data = $this->get_license_data();
        
        if (empty($license_data)) {
            return;
        }
        
        $last_check = get_option($this->last_check_option, 0);
        $check_interval = 24 * 60 * 60; // 24 hours
        
        if ((time() - $last_check) > $check_interval) {
            $this->daily_license_check();
        }
    }
    
    /**
     * Daily license check
     */
    public function daily_license_check() {
        $license_data = $this->get_license_data();
        
        if (empty($license_data['license_key'])) {
            return;
        }
        
        $response = $this->verify_license_with_server(
            $license_data['license_key'],
            $license_data['email'],
            'check'
        );
        
        if ($response['success']) {
            $license_data['status'] = 'valid';
            $license_data['last_check'] = time();
            update_option($this->license_option, $license_data);
        } else {
            $license_data['status'] = 'invalid';
            $license_data['last_check'] = time();
            update_option($this->license_option, $license_data);
        }
        
        update_option($this->last_check_option, time());
    }
    
    /**
     * Check if current page is plugin page
     */
    private function is_plugin_page() {
        $screen = get_current_screen();
        return $screen && strpos($screen->id, 'setia') !== false;
    }
    
    /**
     * Display license notices
     */
    public function license_notices() {
        if (!$this->is_plugin_page()) {
            return;
        }
        
        if (!$this->is_licensed()) {
            echo '<div class="notice notice-warning is-dismissible">';
            echo '<p><strong>SETIA Content Generator:</strong> Please activate your license to use all features. ';
            echo '<a href="' . admin_url('admin.php?page=setia-settings') . '">Activate License</a></p>';
            echo '</div>';
        }
        
        $license_data = $this->get_license_data();
        if (!empty($license_data['expires']) && $license_data['expires'] < (time() + 7 * 24 * 60 * 60)) {
            echo '<div class="notice notice-warning is-dismissible">';
            echo '<p><strong>SETIA Content Generator:</strong> Your license will expire soon. Please renew to continue using the plugin.</p>';
            echo '</div>';
        }
    }
    
    /**
     * Disable plugin functionality if not licensed
     */
    public function enforce_license() {
        if (!$this->is_licensed()) {
            wp_die(
                '<h1>License Required</h1>' .
                '<p>This plugin requires a valid license to function. Please contact support to obtain a license.</p>' .
                '<p><a href="' . admin_url() . '">Return to Dashboard</a></p>',
                'License Required',
                array('response' => 403)
            );
        }
    }
    
    /**
     * Get license status for display
     */
    public function get_license_status_display() {
        $license_data = $this->get_license_data();
        
        if (empty($license_data)) {
            return array(
                'status' => 'inactive',
                'message' => 'No license activated',
                'color' => 'red'
            );
        }
        
        if ($license_data['status'] === 'valid') {
            return array(
                'status' => 'active',
                'message' => 'License active and valid',
                'color' => 'green',
                'expires' => $license_data['expires']
            );
        } else {
            return array(
                'status' => 'invalid',
                'message' => 'License invalid or expired',
                'color' => 'red'
            );
        }
    }
    
    /**
     * Generate hardware fingerprint for additional security
     */
    private function get_hardware_fingerprint() {
        $server_data = array(
            $_SERVER['SERVER_SOFTWARE'] ?? '',
            $_SERVER['SERVER_NAME'] ?? '',
            $_SERVER['HTTP_HOST'] ?? '',
            php_uname(),
            phpversion()
        );
        
        return hash('sha256', implode('|', $server_data));
    }
}
