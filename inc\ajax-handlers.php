<?php
// امنیت: جلوگیری از دسترسی مستقیم
if (!defined('ABSPATH')) {
    exit;
}

// شامل کردن مستقیم Parsedown در همین فایل
$parsedown_path = dirname(dirname(__FILE__)) . '/Parsedown.php';
error_log("SETIA AJAX: Parsedown path: " . $parsedown_path);
error_log("SETIA AJAX: Parsedown file exists: " . (file_exists($parsedown_path) ? "Yes" : "No"));

// بررسی اینکه آیا کلاس قبلاً تعریف شده است
if (class_exists('Parsedown', false)) {
    error_log('SETIA AJAX: Parsedown class already defined, skipping include');
} else {
    // بارگذاری فایل Parsedown اگر وجود دارد
    if (file_exists($parsedown_path)) {
        try {
            require_once $parsedown_path;
            if (class_exists('Parsedown', false)) {
                error_log('SETIA AJAX: Parsedown library successfully loaded');
            } else {
                error_log('SETIA AJAX: Parsedown file loaded but class not found');
                
                // نمایش محتویات فایل برای دیباگ
                $file_contents = file_get_contents($parsedown_path);
                error_log('SETIA AJAX: First 100 chars of Parsedown.php: ' . substr($file_contents, 0, 100));
            }
        } catch (Exception $e) {
            error_log('SETIA AJAX: Error loading Parsedown: ' . $e->getMessage());
        }
    } else {
        error_log('SETIA AJAX: Parsedown file not found at: ' . $parsedown_path);
    }
}

/**
 * کلاس مدیریت درخواست‌های AJAX
 */
class SETIA_Ajax_Handlers {
    
    // نمونه کلاس اصلی
    private $content_generator;
    
    // راه‌اندازی
    public function __construct($content_generator) {
        $this->content_generator = $content_generator;
        
        // ثبت اکشن‌های AJAX
        add_action('wp_ajax_setia_generate_content', array($this, 'generate_content'));
        add_action('wp_ajax_setia_publish_content', array($this, 'publish_content'));
        add_action('wp_ajax_setia_test_connection', array($this, 'test_connection'));
        add_action('wp_ajax_setia_get_content_details', array($this, 'get_content_details'));
        add_action('wp_ajax_setia_publish_from_history', array($this, 'publish_from_history'));
        add_action('wp_ajax_setia_delete_content', array($this, 'delete_content'));
        add_action('wp_ajax_setia_generate_test_image', array($this, 'generate_test_image'));
        
        // اکشن‌های AJAX برای قابلیت‌های جدید
        add_action('wp_ajax_setia_generate_serp_preview', array($this, 'generate_serp_preview'));
        add_action('wp_ajax_setia_optimize_image', array($this, 'optimize_image'));
        add_action('wp_ajax_setia_schedule_publication', array($this, 'schedule_publication'));
        add_action('wp_ajax_setia_rewrite_content', array($this, 'rewrite_content'));
        add_action('wp_ajax_setia_analyze_keyword', array($this, 'analyze_keyword'));
    }
    
    /**
     * تولید محتوا با Gemini
     */
    public function generate_content() {
        // ============== شروع تست بسیار ساده در ابتدای تابع ==============
        // error_log("SETIA DEBUG: generate_content called. Nonce: " . (isset($_POST['nonce']) ? $_POST['nonce'] : 'NOT SET'));
        // if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'setia-nonce')) {
        //     error_log("SETIA ERROR: Nonce verification failed or nonce not set.");
        //     wp_send_json_error(array(
        //         'message' => 'خطای امنیتی: توکن نامعتبر یا ارسال نشده.',
        //         'nonce_received' => (isset($_POST['nonce']) ? $_POST['nonce'] : 'NOT SET'),
        //         'action_received' => (isset($_POST['action']) ? $_POST['action'] : 'NOT SET')
        //     ));
        //     return;
        // }
        // error_log("SETIA DEBUG: Nonce verified successfully.");
        // 
        // wp_send_json_success(array(
        // 'message' => 'پاسخ تست ساده از ابتدای تابع generate_content',
        // 'test_data' => 'این یک داده تست است',
        // 'parsedown_found' => false, // مقداردهی اولیه برای جلوگیری از خطای جاوااسکریپت
        // 'raw_markdown' => 'تست مارک داون', // مقداردهی اولیه
        // 'test_html' => '<h1>تست اچ تی ام ال</h1>' // مقداردهی اولیه
        // ));
        // return; 
        // ============== پایان تست بسیار ساده در ابتدای تابع ==============

        // بررسی امنیتی
        if (!wp_verify_nonce($_POST['nonce'], 'setia-nonce')) {
            wp_send_json_error(array('message' => 'خطای امنیتی: توکن نامعتبر'));
            return;
        }
        
        // Initialize potentially missing response fields
        $parsedown_found = false;
        $generated_text_markdown = ''; // Initialize as empty string
        $generated_text_html = '';    // Initialize as empty string
        
        // دریافت داده‌های فرم
        parse_str($_POST['form_data'], $form_data);
        
        // بررسی داده‌های ورودی
        if (empty($form_data['topic']) || empty($form_data['keywords'])) {
            wp_send_json_error(array('message' => 'لطفاً موضوع و کلمات کلیدی را وارد کنید'));
        }
        
        // ساخت پرامپت برای Gemini
        $prompt = $this->build_content_prompt($form_data);
        
        // تنظیم پارامترهای تولید متن
        $length_params = $this->get_length_params($form_data['length']);
        
        // ارسال درخواست به Gemini
        $response = $this->content_generator->generate_text($prompt, $length_params);
        
        if (!$response['success']) {
            wp_send_json_error(array('message' => $response['error']));
            return; // Return here to avoid further processing on error
        }
        
        // متن تولید شده خام از Gemini
        $generated_text_markdown = $response['text'];
        $generated_text_html = $generated_text_markdown; // Fallback if Parsedown fails or not found
        
        // بررسی وجود کلاس Parsedown و تبدیل مارک‌داون به HTML
        $parsedown_found = class_exists('Parsedown');
        if ($parsedown_found) {
            try {
                $parsedown = new Parsedown();
                $generated_text_html = $parsedown->text($generated_text_markdown);
                error_log("SETIA DEBUG: Markdown successfully converted with Parsedown. HTML output begins with: " . substr($generated_text_html, 0, 100));
            } catch (Exception $e) {
                error_log("SETIA ERROR: Exception when using Parsedown: " . $e->getMessage());
                $parsedown_found = false; // Reset to false since we failed to use it
                $generated_text_html = $generated_text_markdown; // Fallback to raw text
            }
        } else {
            error_log('SETIA ERROR: Parsedown class not found. Using raw markdown.');
            $generated_text_html = wpautop($generated_text_markdown); // Use wpautop instead of raw text
        }

        // For debugging
        error_log("SETIA DEBUG: Final HTML for response (first 100 chars): " . substr($generated_text_html, 0, 100));
        error_log("SETIA DEBUG: Raw markdown for response (first 100 chars): " . substr($generated_text_markdown, 0, 100));
        error_log("SETIA DEBUG: Parsedown found status: " . ($parsedown_found ? 'true' : 'false'));
        
        // تولید تصویر اگر درخواست شده باشد
        $image_url = null;
        if (isset($form_data['generate_image']) && $form_data['generate_image'] === 'yes') {
            $image_prompt = 'تصویر برای موضوع: ' . $form_data['topic'] . ' با کلمات کلیدی: ' . $form_data['keywords'];
            $image_response = $this->content_generator->generate_image($image_prompt);
             
            if ($image_response['success']) {
                $image_url = $image_response['image_url'];
                // آزمایش دسترسی به تصویر برای اطمینان از صحت
                $image_test = wp_remote_head($image_url);
                if (is_wp_error($image_test) || wp_remote_retrieve_response_code($image_test) !== 200) {
                    // اگر URL تصویر قابل دسترسی نیست، از URL پیش‌فرض استفاده می‌کنیم
                    error_log('SETIA: تصویر تولید شده قابل دسترسی نیست: ' . $image_url);
                    $text = urlencode(mb_substr($form_data['topic'], 0, 50));
                    $image_url = "https://via.placeholder.com/800x450?text={$text}";
                }
            }
        }
        
        // تولید متا تگ‌های سئو
        $seo_meta = array();
        if (isset($form_data['seo']) && $form_data['seo'] === 'yes') {
            // اطمینان حاصل کنید که متن مارک‌داون خام به تابع سئو ارسال می‌شود، نه HTML
            $seo_meta = $this->generate_seo_meta($form_data['topic'], $form_data['keywords'], $generated_text_markdown);
        }
        
        // ذخیره در دیتابیس - اینجا متن اصلی مارک‌داون را در دیتابیس ذخیره می‌کنیم، نه HTML را
        $content_id = $this->save_generated_content($form_data, $generated_text_markdown, $image_url, $seo_meta);
        
        // برای اطمینان از اینکه HTML فیلتر نمی‌شود
        $hardcoded_test_html = "<h2>این یک تست HTML سخت‌کد شده است</h2><p>این <strong>پاراگراف</strong> <em>تست</em> می‌باشد.</p><ul><li>آیتم ۱</li><li>آیتم ۲</li></ul>";

        wp_send_json_success(array(
            'message' => 'محتوا با موفقیت تولید شد',
            'generated_text' => $generated_text_html, // متن HTML شده برای نمایش
            'image_url' => $image_url,
            'seo_meta' => $seo_meta,
            'content_id' => $content_id,
            'edit_url' => ($content_id && !is_wp_error($content_id)) ? admin_url('admin.php?page=setia-history&action=edit&id=' . $content_id) : null,
            // Debug information
            'parsedown_found' => $parsedown_found,
            'raw_markdown' => $generated_text_markdown, // متن مارک‌داون خام
            'test_html' => $hardcoded_test_html // نمونه HTML سخت‌کد شده
        ));
    }
    
    /**
     * انتشار محتوای تولید شده به عنوان پست
     */
    public function publish_content() {
        // بررسی امنیتی
        if (!wp_verify_nonce($_POST['nonce'], 'setia-nonce')) {
            wp_send_json_error(array('message' => 'خطای امنیتی: توکن نامعتبر'));
            return;
        }
        
        // دریافت آخرین محتوای تولید شده
        global $wpdb;
        $table_name = $wpdb->prefix . 'setia_generated_content';
        
        $content = $wpdb->get_row("SELECT * FROM $table_name ORDER BY id DESC LIMIT 1");
        
        if (!$content) {
            wp_send_json_error(array('message' => 'محتوایی برای انتشار یافت نشد'));
        }
        
        // ایجاد پست
        $status = sanitize_text_field($_POST['status'] ?? 'draft');
        $result = $this->create_post_from_content($content, $status);
        
        if (!$result['success']) {
            wp_send_json_error(array('message' => $result['error']));
        }
        
        // بروزرسانی رکورد در دیتابیس
        $wpdb->update(
            $table_name,
            array('post_id' => $result['post_id']),
            array('id' => $content->id),
            array('%d'),
            array('%d')
        );
        
        wp_send_json_success(array(
            'post_id' => $result['post_id'],
            'edit_url' => $result['edit_url']
        ));
    }
    
    /**
     * تست اتصال به API‌ها
     */
    public function test_connection() {
        // بررسی امنیتی
        if (!wp_verify_nonce($_POST['nonce'], 'setia_test_connection')) {
            wp_send_json_error(array('message' => 'خطای امنیتی: توکن نامعتبر'));
            return;
        }
        
        // دریافت کلید API
        $gemini_api_key = sanitize_text_field($_POST['gemini_api_key']);
        
        $result = array(
            'gemini_success' => false,
            'gemini_message' => ''
        );
        
        // بررسی وجود کلید API
        if (empty($gemini_api_key)) {
            $result['gemini_message'] = 'کلید API Google AI وارد نشده است';
            wp_send_json_error($result);
            return;
        }
        
        // تست 1: اتصال به Gemini برای تولید متن
        $gemini_url = "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=$gemini_api_key";
        $gemini_body = json_encode([
            "contents" => [
                ["parts" => [["text" => "سلام"]]]
            ],
            "generationConfig" => [
                "maxOutputTokens" => 50
            ]
        ]);
        
        $gemini_response = wp_remote_post($gemini_url, [
            'headers' => [
                'Content-Type' => 'application/json'
            ],
            'body' => $gemini_body,
            'timeout' => 15
        ]);
        
        // بررسی پاسخ Gemini
        if (is_wp_error($gemini_response)) {
            $result['gemini_message'] = 'خطا در اتصال به Gemini: ' . $gemini_response->get_error_message();
        } else {
            $gemini_code = wp_remote_retrieve_response_code($gemini_response);
            if ($gemini_code != 200) {
                $response_body = wp_remote_retrieve_body($gemini_response);
                $result['gemini_message'] = 'خطای Gemini: ' . $gemini_code . ' - ' . $response_body;
            } else {
                $result['gemini_success'] = true;
                $result['gemini_message'] = 'اتصال به سرویس Gemini موفقیت‌آمیز است';
            }
        }
        
        // بازگرداندن نتیجه
        if ($result['gemini_success']) {
            wp_send_json_success($result);
        } else {
            wp_send_json_error($result);
        }
    }
    
    // توابع دیگر AJAX
    public function get_content_details() {
        // بررسی امنیتی
        if (!wp_verify_nonce($_POST['nonce'], 'setia_content_nonce')) {
            wp_send_json_error(array('message' => 'خطای امنیتی: توکن نامعتبر'));
            return;
        }
        
        $content_id = intval($_POST['content_id']);
        
        if (!$content_id) {
            wp_send_json_error(array('message' => 'شناسه محتوا نامعتبر است'));
        }
        
        // دریافت محتوا از دیتابیس
        global $wpdb;
        $table_name = $wpdb->prefix . 'setia_generated_content';
        
        $content = $wpdb->get_row($wpdb->prepare("SELECT * FROM $table_name WHERE id = %d", $content_id));
        
        if (!$content) {
            wp_send_json_error(array('message' => 'محتوا یافت نشد'));
        }
        
        // آماده‌سازی داده‌های سئو
        $seo_meta = json_decode($content->seo_meta, true);
        
        wp_send_json_success(array(
            'topic' => $content->topic,
            'content' => wpautop($content->generated_text),
            'image_url' => $content->generated_image_url,
            'seo' => $seo_meta
        ));
    }
    
    public function publish_from_history() {
        // بررسی امنیتی
        if (!wp_verify_nonce($_POST['nonce'], 'setia_content_nonce')) {
            wp_send_json_error(array('message' => 'خطای امنیتی: توکن نامعتبر'));
            return;
        }
        
        $content_id = intval($_POST['content_id']);
        
        if (!$content_id) {
            wp_send_json_error(array('message' => 'شناسه محتوا نامعتبر است'));
        }
        
        // دریافت محتوا از دیتابیس
        global $wpdb;
        $table_name = $wpdb->prefix . 'setia_generated_content';
        
        $content = $wpdb->get_row($wpdb->prepare("SELECT * FROM $table_name WHERE id = %d", $content_id));
        
        if (!$content) {
            wp_send_json_error(array('message' => 'محتوا یافت نشد'));
        }
        
        // تعیین وضعیت انتشار (پیش‌نویس یا منتشر شده)
        $status = 'draft';
        if (isset($_POST['status']) && $_POST['status'] === 'publish') {
            $status = 'publish';
        }
        
        // ایجاد پست
        $result = $this->create_post_from_content($content, $status);
        
        if (!$result['success']) {
            wp_send_json_error(array('message' => $result['error']));
        }
        
        // بروزرسانی رکورد در دیتابیس
        $wpdb->update(
            $table_name,
            array('post_id' => $result['post_id']),
            array('id' => $content->id),
            array('%d'),
            array('%d')
        );
        
        wp_send_json_success(array(
            'post_id' => $result['post_id'],
            'edit_url' => $result['edit_url'],
            'status' => $status,
            'message' => ($status === 'publish') ? 'محتوا با موفقیت منتشر شد' : 'محتوا با موفقیت به صورت پیش‌نویس ذخیره شد'
        ));
    }
    
    public function delete_content() {
        // بررسی امنیتی
        if (!wp_verify_nonce($_POST['nonce'], 'setia_content_nonce')) {
            wp_send_json_error(array('message' => 'خطای امنیتی: توکن نامعتبر'));
            return;
        }
        
        $content_id = intval($_POST['content_id']);
        
        if (!$content_id) {
            wp_send_json_error(array('message' => 'شناسه محتوا نامعتبر است'));
        }
        
        // حذف از دیتابیس
        global $wpdb;
        $table_name = $wpdb->prefix . 'setia_generated_content';
        
        $result = $wpdb->delete(
            $table_name,
            array('id' => $content_id),
            array('%d')
        );
        
        if ($result === false) {
            wp_send_json_error(array('message' => 'خطا در حذف محتوا'));
        }
        
        wp_send_json_success(array('message' => 'محتوا با موفقیت حذف شد'));
    }
    
    /**
     * تولید تصویر تست برای صفحه تنظیمات
     */
    public function generate_test_image() {
        // بررسی امنیتی
        if (!wp_verify_nonce($_POST['nonce'], 'setia_test_connection')) {
            wp_send_json_error(array('message' => 'خطای امنیتی: توکن نامعتبر'));
            return;
        }
        
        // دریافت پرامپت
        $prompt = sanitize_text_field($_POST['prompt']); 
        
        // ثبت لاگ برای دیباگ
        error_log('SETIA Test Image Generation: Prompt="' . $prompt . '"');
        
        if (empty($prompt)) {
            wp_send_json_error(array('message' => 'لطفا یک پرامپت برای تولید تصویر وارد کنید'));
            return;
        }
        
        // تولید تصویر با استفاده از متد generate_image کلاس اصلی
        $image_response = $this->content_generator->generate_image($prompt);
        
        if (!$image_response['success']) {
            error_log('SETIA Test Image Error: ' . (isset($image_response['error']) ? $image_response['error'] : 'خطای نامشخص'));
            wp_send_json_error(array('message' => $image_response['error']));
            return;
        }
        
        // ثبت URL تصویر تولید شده برای دیباگ
        error_log('SETIA Test Image Success: URL="' . $image_response['image_url'] . '"');
        
        // برگرداندن URL تصویر
        wp_send_json_success(array(
            'image_url' => $image_response['image_url']
        ));
    }
    
    // توابع کمکی
    private function build_content_prompt($form_data) {
        $topic = sanitize_text_field($form_data['topic']);
        $keywords = sanitize_text_field($form_data['keywords']);
        $tone = sanitize_text_field($form_data['tone']);
        $length = sanitize_text_field($form_data['length']);
        $instructions = sanitize_textarea_field($form_data['instructions'] ?? '');
        
        $prompt = "لطفاً یک مقاله در مورد «{$topic}» بنویس با کلمات کلیدی: {$keywords}.\n";
        $prompt .= "لحن مقاله باید {$tone} باشد.\n";
        
        // اضافه کردن طول مطلب
        switch ($length) {
            case 'کوتاه':
                $prompt .= "مقاله باید کوتاه (حدود ۵۰۰ کلمه) باشد.\n";
                break;
            case 'متوسط':
                $prompt .= "مقاله باید متوسط (حدود ۱۰۰۰ کلمه) باشد.\n";
                break;
            case 'بلند':
                $prompt .= "مقاله باید بلند (حدود ۱۵۰۰ کلمه) باشد.\n";
                break;
            case 'خیلی بلند':
                $prompt .= "مقاله باید خیلی بلند (حدود ۲۰۰۰ کلمه) باشد.\n";
                break;
        }
        
        if (!empty($instructions)) {
            $prompt .= "دستورالعمل‌های اضافی: {$instructions}\n";
        }
        
        // دستورالعمل‌های مربوط به SEO
        $prompt .= "\n\nدستورالعمل‌های بهینه‌سازی SEO:";
        $prompt .= "\n1. کلمه کلیدی اصلی ({$keywords}) را در پاراگراف اول متن استفاده کن.";
        $prompt .= "\n2. تراکم کلمه کلیدی را بهینه نگه دار (بین 0.5% تا 2.5% از کل محتوا).";
        $prompt .= "\n3. حداقل 2 لینک داخلی (با فرمت [متن لینک](/sample-page)) به مطالب مرتبط اضافه کن.";
        $prompt .= "\n4. حداقل 2 لینک خارجی (با فرمت [متن لینک](https://example.com)) به منابع معتبر اضافه کن.";
        $prompt .= "\n5. از زیرعنوان‌های متنوع استفاده کن و در حدود 50% از آنها از کلمه کلیدی استفاده کن (نه بیشتر).";
        $prompt .= "\n6. از پاراگراف‌های کوتاه (کمتر از 150 کاراکتر) استفاده کن.";
        $prompt .= "\n7. از جملات کوتاه و قابل فهم استفاده کن.";
        $prompt .= "\n8. از کلمات ربط مناسب برای اتصال جملات و پاراگراف‌ها استفاده کن.";
        $prompt .= "\n9. از فرمت‌های متنوع مانند لیست‌ها، نقل قول‌ها و تأکیدها استفاده کن.";
        $prompt .= "\n10. یک جمع‌بندی کامل در انتهای مقاله بنویس.";
        
        // دستورالعمل‌های قالب‌بندی متن
        $prompt .= "\n\nدستورالعمل‌های قالب‌بندی متن:";
        $prompt .= "\n1. ساختار مقاله باید شامل عناوین و زیر عنوان‌ها باشد. برای عنوان اصلی از #، برای زیرعنوان‌ها از ## و ### و به همین ترتیب استفاده کن. هر عنوان باید در یک خط جداگانه باشد.";
        $prompt .= "\n2. کلمات کلیدی و عبارات مهم را با **دو ستاره در دو طرف** برای بولد کردن، و با *یک ستاره در دو طرف* برای ایتالیک کردن مشخص کن.";
        $prompt .= "\n3. در صورت نیاز به لیست، از لیست‌های نشانه‌دار (مانند - آیتم اول) یا شماره‌دار (مانند 1. آیتم اول) استفاده کن. هر آیتم لیست باید در یک خط جداگانه باشد.";
        $prompt .= "\n4. برای نقل قول مستقیم، پاراگراف را با علامت < در ابتدای خط شروع کن.";
        $prompt .= "\n5. اگر نیاز به درج لینک بود، از فرمت [متن لینک](آدرس URL) استفاده کن.";
        $prompt .= "\n6. مقاله باید دارای مقدمه، بدنه و نتیجه‌گیری باشد.";
        
        return $prompt;
    }
    
    private function get_length_params($length) {
        $params = array();
        
        switch ($length) {
            case 'کوتاه':
                $params['max_tokens'] = 1000;
                $params['temperature'] = 0.6;
                break;
            case 'متوسط':
                $params['max_tokens'] = 2000;
                $params['temperature'] = 0.7;
                break;
            case 'بلند':
                $params['max_tokens'] = 3000;
                $params['temperature'] = 0.75;
                break;
            case 'خیلی بلند':
                $params['max_tokens'] = 4000;
                $params['temperature'] = 0.8;
                break;
        }
        
        return $params;
    }
    
    private function generate_seo_meta($topic, $keywords, $content) {
        $keywords_array = array_map('trim', explode(',', $keywords));
        $primary_keyword = $keywords_array[0] ?? '';
        
        // حذف HTML tags و تبدیل به متن ساده
        $clean_content = wp_strip_all_tags($content);
        
        // ایجاد توضیحات متا با طول مناسب (بین 120 تا 156 کاراکتر)
        $meta_description = mb_substr($clean_content, 0, 156);
        if (mb_strlen($meta_description) >= 120) {
            $meta_description = mb_substr($meta_description, 0, mb_strrpos($meta_description, ' ')) . '...';
        }
        
        // ایجاد عنوان سئو با فرمت مناسب
        $seo_title = $topic;
        if (mb_strlen($seo_title) > 60) {
            $seo_title = mb_substr($seo_title, 0, 57) . '...';
        }
        
        // محاسبه تخمین زمان مطالعه
        $reading_time = ceil(str_word_count($clean_content) / 200); // تخمین زمان مطالعه بر اساس 200 کلمه در دقیقه
        
        // تحلیل محتوا برای امتیازدهی SEO
        $seo_score = $this->calculate_seo_score($content, $primary_keyword);
        $readability_score = $this->calculate_readability_score($content);
        
        // ایجاد متادیتای Yoast SEO
        return array(
            'title' => $seo_title,
            'description' => $meta_description,
            'keywords' => implode(', ', $keywords_array),
            'focus_keyword' => $primary_keyword,
            '_yoast_wpseo_title' => $seo_title,
            '_yoast_wpseo_metadesc' => $meta_description,
            '_yoast_wpseo_focuskw' => $primary_keyword,
            '_yoast_wpseo_meta-robots-noindex' => '0',
            '_yoast_wpseo_meta-robots-nofollow' => '0',
            '_yoast_wpseo_meta-robots-adv' => 'none',
            '_yoast_wpseo_linkdex' => $seo_score, // امتیاز SEO (از 100)
            '_yoast_wpseo_content_score' => $readability_score, // امتیاز خوانایی (از 100)
            '_yoast_wpseo_is_cornerstone' => '0',
            '_yoast_wpseo_estimated-reading-time-minutes' => $reading_time,
            
            // متادیتای اضافی برای بهینه‌سازی لینک‌ها
            '_yoast_wpseo_internal_linking' => '{"count":2}', // تعداد لینک‌های داخلی پیشنهادی
            '_yoast_wpseo_outbound_linking' => '{"count":2}', // تعداد لینک‌های خارجی پیشنهادی
            
            // متادیتای مربوط به تصاویر
            '_yoast_wpseo_has_image' => '1', // نشان‌دهنده وجود تصویر در محتوا
            '_yoast_wpseo_image_alt_tags' => '1', // نشان‌دهنده استفاده از تگ alt برای تصاویر
            
            // متادیتای مربوط به ساختار محتوا
            '_yoast_wpseo_subheading_distribution' => '{"count":5}', // تعداد زیرعنوان‌ها
            '_yoast_wpseo_text_length' => '{"raw":"long"}', // طول متن
            '_yoast_wpseo_keyword_density' => '{"raw":"good"}', // تراکم کلمه کلیدی
            
            // متادیتای مربوط به خوانایی
            '_yoast_wpseo_flesch_reading_ease' => '{"raw":"good"}', // سهولت خواندن متن
            '_yoast_wpseo_paragraph_length' => '{"raw":"good"}', // طول پاراگراف‌ها
            '_yoast_wpseo_sentence_length' => '{"raw":"good"}', // طول جملات
            '_yoast_wpseo_consecutive_sentences' => '{"raw":"good"}', // تنوع جملات
            '_yoast_wpseo_passive_voice' => '{"raw":"good"}', // استفاده از جملات معلوم/مجهول
            '_yoast_wpseo_transition_words' => '{"raw":"good"}', // استفاده از کلمات ربط
        );
    }
    
    /**
     * محاسبه امتیاز SEO بر اساس فاکتورهای مختلف
     */
    private function calculate_seo_score($content, $focus_keyword) {
        $score = 70; // امتیاز پایه
        
        // بررسی وجود کلمه کلیدی در محتوا
        if (stripos($content, $focus_keyword) !== false) {
            $score += 5;
        }
        
        // بررسی تراکم کلمه کلیدی (بین 0.5% تا 2.5% ایده‌آل است)
        $keyword_count = substr_count(strtolower($content), strtolower($focus_keyword));
        $word_count = str_word_count(strip_tags($content));
        
        if ($word_count > 0) {
            $keyword_density = ($keyword_count / $word_count) * 100;
            
            if ($keyword_density >= 0.5 && $keyword_density <= 2.5) {
                $score += 5;
            } else if ($keyword_density > 2.5) {
                $score -= 5; // تراکم بیش از حد
            }
        }
        
        // بررسی وجود کلمه کلیدی در عناوین
        if (preg_match('/#+ .*' . preg_quote($focus_keyword, '/') . '.*$/mi', $content)) {
            $score += 5;
        }
        
        // بررسی وجود لینک‌های داخلی و خارجی
        if (preg_match('/\[.*\]\(https?:\/\//', $content)) {
            $score += 5; // وجود لینک
        }
        
        // بررسی طول محتوا
        if ($word_count >= 300) {
            $score += 5;
        }
        if ($word_count >= 600) {
            $score += 5;
        }
        
        // محدود کردن امتیاز بین 0 تا 100
        return min(100, max(0, $score));
    }
    
    /**
     * محاسبه امتیاز خوانایی بر اساس فاکتورهای مختلف
     */
    private function calculate_readability_score($content) {
        $score = 70; // امتیاز پایه
        
        // بررسی وجود پاراگراف‌های کوتاه (کمتر از 150 کاراکتر)
        $paragraphs = explode("\n\n", $content);
        $short_paragraphs = 0;
        
        foreach ($paragraphs as $paragraph) {
            if (strlen(trim($paragraph)) > 0 && strlen(trim($paragraph)) < 150) {
                $short_paragraphs++;
            }
        }
        
        // امتیاز برای پاراگراف‌های کوتاه
        if (count($paragraphs) > 0) {
            $short_paragraph_ratio = $short_paragraphs / count($paragraphs);
            if ($short_paragraph_ratio >= 0.5) {
                $score += 5;
            }
        }
        
        // بررسی وجود زیرعنوان‌ها
        $subheadings = preg_match_all('/^#{2,3} .+$/m', $content, $matches);
        if ($subheadings >= 3) {
            $score += 5;
        }
        if ($subheadings >= 6) {
            $score += 5;
        }
        
        // بررسی وجود لیست‌ها
        if (preg_match('/^[*\-+] .+$/m', $content) || preg_match('/^\d+\. .+$/m', $content)) {
            $score += 5;
        }
        
        // بررسی وجود تصاویر (در مارک‌داون)
        if (preg_match('/!\[.*\]\(.*\)/', $content)) {
            $score += 5;
        }
        
        // بررسی طول جملات (تقریبی)
        $sentences = preg_split('/[.!?]+/', $content);
        $short_sentences = 0;
        
        foreach ($sentences as $sentence) {
            if (str_word_count(trim($sentence)) < 20 && str_word_count(trim($sentence)) > 0) {
                $short_sentences++;
            }
        }
        
        // امتیاز برای جملات کوتاه
        if (count($sentences) > 0) {
            $short_sentence_ratio = $short_sentences / count($sentences);
            if ($short_sentence_ratio >= 0.7) {
                $score += 5;
            }
        }
        
        // محدود کردن امتیاز بین 0 تا 100
        return min(100, max(0, $score));
    }
    
    private function save_generated_content($form_data, $text, $image_url, $seo_meta) {
        global $wpdb;
        $table_name = $wpdb->prefix . 'setia_generated_content';
        
        $data = array(
            'topic' => sanitize_text_field($form_data['topic']),
            'keywords' => sanitize_text_field($form_data['keywords']),
            'tone' => sanitize_text_field($form_data['tone']),
            'category' => sanitize_text_field($form_data['category']),
            'length' => sanitize_text_field($form_data['length']),
            'generated_text' => $text,
            'generated_image_url' => $image_url,
            'seo_meta' => json_encode($seo_meta),
            'created_at' => current_time('mysql')
        );
        
        $wpdb->insert($table_name, $data);
        
        return $wpdb->insert_id;
    }
    
    private function create_post_from_content($content, $status = 'draft') {
        // آماده‌سازی داده‌های سئو
        $seo_meta = json_decode($content->seo_meta, true);
        
        // ایجاد پست
        $result = $this->content_generator->create_wordpress_post(
            $content->topic,
            $content->generated_text,
            $content->category,
            $seo_meta,
            $content->generated_image_url
        );
        
        if (!$result['success']) {
            return $result;
        }
        
        // اگر وضعیت انتشار درخواست شده باشد
        if ($status === 'publish') {
            wp_update_post(array(
                'ID' => $result['post_id'],
                'post_status' => 'publish'
            ));
        }
        
        return $result;
    }
    
    /**
     * تولید پیش‌نمایش نتایج گوگل
     */
    public function generate_serp_preview() {
        // بررسی امنیتی
        if (!wp_verify_nonce($_POST['nonce'], 'setia-nonce')) {
            wp_send_json_error(array('message' => 'خطای امنیتی: توکن نامعتبر'));
            return;
        }
        
        $title = sanitize_text_field($_POST['title']);
        $description = sanitize_text_field($_POST['description']);
        $url = esc_url_raw($_POST['url']);
        
        // استفاده از تابع generate_serp_preview در کلاس اصلی
        $preview = $this->content_generator->generate_serp_preview($title, $description, $url);
        
        wp_send_json_success($preview);
    }
    
    /**
     * بهینه‌سازی تصویر برای SEO
     */
    public function optimize_image() {
        // بررسی امنیتی
        if (!wp_verify_nonce($_POST['nonce'], 'setia-nonce')) {
            wp_send_json_error(array('message' => 'خطای امنیتی: توکن نامعتبر'));
            return;
        }
        
        $post_id = intval($_POST['post_id']);
        $alt_text = sanitize_text_field($_POST['alt_text']);
        $caption = sanitize_text_field($_POST['caption']);
        $focus_keyword = sanitize_text_field($_POST['focus_keyword']);
        
        // دریافت شناسه تصویر شاخص
        $featured_image_id = get_post_thumbnail_id($post_id);
        
        if (!$featured_image_id) {
            wp_send_json_error(array('message' => 'تصویر شاخصی برای این پست یافت نشد'));
            return;
        }
        
        // بروزرسانی متادیتای تصویر
        update_post_meta($featured_image_id, '_wp_attachment_image_alt', $alt_text);
        
        // بروزرسانی پست تصویر
        wp_update_post(array(
            'ID' => $featured_image_id,
            'post_excerpt' => $caption
        ));
        
        // استفاده از تابع optimize_images_for_seo در کلاس اصلی
        $result = $this->content_generator->optimize_images_for_seo($post_id, $featured_image_id, $focus_keyword);
        
        if ($result) {
            wp_send_json_success(array('message' => 'تصویر با موفقیت بهینه‌سازی شد'));
        } else {
            wp_send_json_error(array('message' => 'خطا در بهینه‌سازی تصویر'));
        }
    }
    
    /**
     * برنامه‌ریزی زمانی برای انتشار محتوا
     */
    public function schedule_publication() {
        // بررسی امنیتی
        if (!wp_verify_nonce($_POST['nonce'], 'setia-nonce')) {
            wp_send_json_error(array('message' => 'خطای امنیتی: توکن نامعتبر'));
            return;
        }
        
        $content_id = intval($_POST['content_id']);
        $publish_date = sanitize_text_field($_POST['publish_date']);
        $social_share = isset($_POST['social_share']) ? (bool)$_POST['social_share'] : false;
        $status = sanitize_text_field($_POST['status']);
        
        // دریافت محتوا از دیتابیس
        global $wpdb;
        $table_name = $wpdb->prefix . 'setia_generated_content';
        $content_row = $wpdb->get_row($wpdb->prepare("SELECT * FROM $table_name WHERE id = %d", $content_id));
        
        if (!$content_row) {
            wp_send_json_error(array('message' => 'محتوای مورد نظر یافت نشد'));
            return;
        }
        
        // ایجاد پست وردپرس
        $post_data = array(
            'post_title' => $content_row->topic,
            'post_content' => $content_row->generated_text,
            'post_status' => 'future',
            'post_date' => date('Y-m-d H:i:s', strtotime($publish_date)),
            'post_author' => get_current_user_id(),
            'post_category' => array(intval($content_row->category))
        );
        
        // ایجاد پست
        $post_id = wp_insert_post($post_data);
        
        if (is_wp_error($post_id)) {
            wp_send_json_error(array('message' => $post_id->get_error_message()));
            return;
        }
        
        // اضافه کردن متادیتای سئو
        if (!empty($content_row->seo_meta)) {
            $seo_meta = json_decode($content_row->seo_meta, true);
            
            foreach ($seo_meta as $key => $value) {
                update_post_meta($post_id, $key, $value);
            }
        }
        
        // تنظیم تصویر شاخص
        if (!empty($content_row->generated_image_url)) {
            $this->set_featured_image($post_id, $content_row->generated_image_url);
        }
        
        // استفاده از تابع schedule_content_publication در کلاس اصلی
        $schedule_data = array(
            'publish_date' => $publish_date,
            'social_share' => $social_share
        );
        
        $result = $this->content_generator->schedule_content_publication($post_id, $schedule_data);
        
        if ($result) {
            wp_send_json_success(array(
                'message' => 'محتوا با موفقیت برای انتشار برنامه‌ریزی شد',
                'post_id' => $post_id,
                'edit_url' => get_edit_post_link($post_id, '')
            ));
        } else {
            wp_send_json_error(array('message' => 'خطا در برنامه‌ریزی انتشار محتوا'));
        }
    }
    
    /**
     * بازنویسی خودکار محتوا
     */
    public function rewrite_content() {
        // بررسی امنیتی
        if (!wp_verify_nonce($_POST['nonce'], 'setia-nonce')) {
            wp_send_json_error(array('message' => 'خطای امنیتی: توکن نامعتبر'));
            return;
        }
        
        $content = sanitize_textarea_field($_POST['content']);
        $rewrite_type = sanitize_text_field($_POST['rewrite_type']);
        
        // استفاده از تابع rewrite_content در کلاس اصلی
        $response = $this->content_generator->rewrite_content($content, $rewrite_type);
        
        if ($response['success']) {
            // تبدیل متن مارک‌داون به HTML
            $html_content = $response['text'];
            
            // بررسی وجود کلاس Parsedown و تبدیل مارک‌داون به HTML
            if (class_exists('Parsedown')) {
                try {
                    $parsedown = new Parsedown();
                    $html_content = $parsedown->text($response['text']);
                } catch (Exception $e) {
                    error_log('SETIA: خطا در تبدیل مارک‌داون به HTML: ' . $e->getMessage());
                    $html_content = wpautop($response['text']);
                }
            } else {
                $html_content = wpautop($response['text']);
            }
            
            wp_send_json_success(array(
                'message' => 'محتوا با موفقیت بازنویسی شد',
                'content' => $html_content,
                'raw_content' => $response['text']
            ));
        } else {
            wp_send_json_error(array('error' => $response['error']));
        }
    }
    
    /**
     * تحلیل رقابتی کلمات کلیدی
     */
    public function analyze_keyword() {
        // بررسی امنیتی
        if (!wp_verify_nonce($_POST['nonce'], 'setia-nonce')) {
            wp_send_json_error(array('message' => 'خطای امنیتی: توکن نامعتبر'));
            return;
        }
        
        $keyword = sanitize_text_field($_POST['keyword']);
        
        // استفاده از تابع analyze_keyword_competition در کلاس اصلی
        $response = $this->content_generator->analyze_keyword_competition($keyword);
        
        if ($response['success']) {
            wp_send_json_success($response['data']);
        } else {
            wp_send_json_error(array('error' => $response['error']));
        }
    }
    
    /**
     * تنظیم تصویر شاخص برای پست
     */
    private function set_featured_image($post_id, $image_url) {
        // بررسی URL تصویر
        if (empty($image_url)) {
            return false;
        }
        
        // روش مستقیم برای دانلود و الحاق تصویر از URL به پست
        require_once(ABSPATH . 'wp-admin/includes/media.php');
        require_once(ABSPATH . 'wp-admin/includes/file.php');
        require_once(ABSPATH . 'wp-admin/includes/image.php');
        
        // دانلود مستقیم تصویر با استفاده از API وردپرس
        $tmp = download_url($image_url);
        
        if (is_wp_error($tmp)) {
            error_log('SETIA: خطا در دانلود تصویر: ' . $tmp->get_error_message());
            return false;
        }
        
        // فایل دانلود شده را به آرایه‌ی مورد نیاز media_handle_sideload تبدیل کنید
        $file_array = array(
            'name' => basename($image_url),
            'tmp_name' => $tmp
        );
        
        // در صورتی که نام فایل معتبر نیست، یک نام تصادفی ایجاد کنید
        if (empty($file_array['name']) || strlen($file_array['name']) < 5) {
            $file_array['name'] = 'setia-featured-image-' . time() . '.jpg';
        }
        
        // تصویر را به کتابخانه رسانه اضافه و به پست الحاق کنید
        $attachment_id = media_handle_sideload($file_array, $post_id);
        
        // فایل موقت را حذف کنید
        @unlink($tmp);
        
        if (is_wp_error($attachment_id)) {
            error_log('SETIA: خطا در الحاق تصویر به پست: ' . $attachment_id->get_error_message());
            return false;
        }
        
        // تصویر را به عنوان تصویر شاخص تنظیم کنید
        return set_post_thumbnail($post_id, $attachment_id);
    }
} 