<?php // Scheduler class file

/**
 * SETIA Content Scheduler
 * سیستم زمانبندی برای تولید خودکار محتوا
 */

// امنیت: جلوگیری از دسترسی مستقیم
if (!defined('ABSPATH')) {
    exit;
}

class SETIA_Scheduler {
    
    // نمونه کلاس اصلی
    private $content_generator;
    
    // زمان‌های پیش‌فرض
    private $default_schedules = array(
        'hourly' => 'هر ساعت',
        'twicedaily' => 'روزانه دو بار',
        'daily' => 'روزانه',
        'weekly' => 'هفتگی',
        'monthly' => 'ماهانه'
    );
    
    /**
     * راه‌اندازی کلاس زمانبندی
     */
    public function __construct($content_generator) {
        $this->content_generator = $content_generator;
        
        // افزودن زمانبندی‌های سفارشی
        add_filter('cron_schedules', array($this, 'add_custom_schedules'));
        
        // ثبت اکشن‌های کرون
        add_action('setia_scheduled_content_generation', array($this, 'generate_scheduled_content'));
        
        // منوی تنظیمات زمانبندی
        add_action('admin_init', array($this, 'register_scheduler_settings'));
        
        // افزودن منوی زمانبندی
        add_action('admin_menu', array($this, 'add_scheduler_menu'), 20);
        
        // اجکس برای مدیریت زمانبندی‌ها
        add_action('wp_ajax_setia_save_schedule', array($this, 'save_schedule'));
        add_action('wp_ajax_setia_delete_schedule', array($this, 'delete_schedule'));
        add_action('wp_ajax_setia_get_schedules', array($this, 'get_schedules'));
        
        // اجکس برای اجرای کرون داخلی
        add_action('wp_ajax_setia_run_admin_cron', array($this, 'run_admin_cron'));
        add_action('wp_ajax_nopriv_setia_run_admin_cron', array($this, 'run_admin_cron'));
        
        // بارگذاری اسکریپت کرون داخلی در صفحه مدیریت
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_cron_script'));
    }
    
    /**
     * بارگذاری اسکریپت کرون داخلی در صفحه مدیریت
     */
    public function enqueue_admin_cron_script() {
        // بارگذاری اسکریپت فقط در صفحات مدیریت وردپرس
        wp_enqueue_script(
            'setia-admin-cron',
            plugins_url('/assets/js/admin-cron.js', dirname(__FILE__)),
            array('jquery'),
            filemtime(plugin_dir_path(dirname(__FILE__)) . 'assets/js/admin-cron.js'),
            true
        );
        
        // انتقال متغیرها به اسکریپت
        wp_localize_script('setia-admin-cron', 'setia_admin_cron', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('setia-admin-cron-nonce'),
            'interval' => get_option('setia_admin_cron_interval', 15) // پیش‌فرض: 15 دقیقه
        ));
    }
    
    /**
     * اجرای کرون داخلی از طریق اجکس
     */
    public function run_admin_cron() {
        // بررسی امنیتی
        if (!wp_verify_nonce($_POST['nonce'], 'setia-admin-cron-nonce')) {
            wp_send_json_error(array('message' => 'خطای امنیتی: توکن نامعتبر'));
            return;
        }
        
        // دریافت زمانبندی‌های فعال
        $schedules = get_option('setia_content_schedules', array());
        $now = current_time('timestamp');
        $executed = array();
        
        foreach ($schedules as $schedule_id => $schedule) {
            // بررسی فقط زمانبندی‌های فعال
            if ($schedule['status'] !== 'active') {
                continue;
            }
            
            // تبدیل زمان آخرین اجرا به timestamp
            $last_run = !empty($schedule['last_run']) ? strtotime($schedule['last_run']) : 0;
            
            // محاسبه فاصله زمانی بر اساس تناوب
            $interval = 3600; // پیش‌فرض: هر ساعت (3600 ثانیه)
            
            switch ($schedule['frequency']) {
                case 'minutely':
                    $interval = 60; // هر دقیقه
                    break;
                case 'every5minutes':
                    $interval = 300; // هر 5 دقیقه
                    break;
                case 'every15minutes':
                    $interval = 900; // هر 15 دقیقه
                    break;
                case 'every30minutes':
                    $interval = 1800; // هر 30 دقیقه
                    break;
                case 'hourly':
                    $interval = 3600; // هر ساعت
                    break;
                case 'twicedaily':
                    $interval = 43200; // دو بار در روز (هر 12 ساعت)
                    break;
                case 'daily':
                    $interval = 86400; // روزانه
                    break;
                case 'weekly':
                    $interval = 604800; // هفتگی
                    break;
                case 'monthly':
                    $interval = 2592000; // ماهانه (30 روز)
                    break;
            }
            
            // بررسی آیا زمان اجرای زمانبندی رسیده است
            if ($now - $last_run >= $interval) {
                // اجرای زمانبندی
                $this->generate_scheduled_content($schedule_id);
                
                // افزودن به لیست زمانبندی‌های اجرا شده
                $executed[] = array(
                    'id' => $schedule_id,
                    'title' => $schedule['title'],
                    'frequency' => $schedule['frequency']
                );
            }
        }
        
        // بروزرسانی زمان آخرین بررسی
        update_option('setia_admin_cron_last_check', $now);
        
        // ارسال پاسخ
        if (!empty($executed)) {
            wp_send_json_success(array(
                'message' => 'کرون داخلی با موفقیت اجرا شد',
                'executed' => $executed,
                'count' => count($executed)
            ));
        } else {
            wp_send_json_success(array(
                'message' => 'هیچ زمانبندی برای اجرا یافت نشد',
                'executed' => array(),
                'count' => 0
            ));
        }
    }
    
    /**
     * افزودن زمانبندی‌های سفارشی به وردپرس
     */
    public function add_custom_schedules($schedules) {
        // زمانبندی هر دقیقه
        if (!isset($schedules['minutely'])) {
            $schedules['minutely'] = array(
                'interval' => 60, // هر دقیقه
                'display' => 'هر دقیقه'
            );
        }
        
        // زمانبندی هر 5 دقیقه
        if (!isset($schedules['every5minutes'])) {
            $schedules['every5minutes'] = array(
                'interval' => 300, // هر 5 دقیقه
                'display' => 'هر 5 دقیقه'
            );
        }
        
        // زمانبندی هر 15 دقیقه
        if (!isset($schedules['every15minutes'])) {
            $schedules['every15minutes'] = array(
                'interval' => 900, // هر 15 دقیقه
                'display' => 'هر 15 دقیقه'
            );
        }
        
        // زمانبندی هر 30 دقیقه
        if (!isset($schedules['every30minutes'])) {
            $schedules['every30minutes'] = array(
                'interval' => 1800, // هر 30 دقیقه
                'display' => 'هر 30 دقیقه'
            );
        }
        
        // زمانبندی هفتگی
        if (!isset($schedules['weekly'])) {
            $schedules['weekly'] = array(
                'interval' => 604800, // 7 روز
                'display' => 'هفتگی'
            );
        }
        
        // زمانبندی ماهانه
        if (!isset($schedules['monthly'])) {
            $schedules['monthly'] = array(
                'interval' => 2592000, // 30 روز
                'display' => 'ماهانه'
            );
        }
        
        return $schedules;
    }
    
    /**
     * ثبت تنظیمات زمانبندی
     */
    public function register_scheduler_settings() {
        register_setting('setia_scheduler_settings', 'setia_scheduler_settings');
    }
    
    /**
     * افزودن منوی زمانبندی
     */
    public function add_scheduler_menu() {
        add_submenu_page(
            'setia-content-generator',
            'زمانبندی تولید محتوا',
            'زمانبندی محتوا',
            'manage_options',
            'setia-scheduler',
            array($this, 'scheduler_page')
        );
        
        // اضافه کردن منوی تنظیمات کرون سرور
        add_submenu_page(
            'setia-content-generator',
            'تنظیمات کرون سرور',
            'تنظیمات کرون سرور',
            'manage_options',
            'setia-cron-settings',
            array($this, 'cron_settings_page')
        );
        
        // اضافه کردن منوی تنظیمات کرون داخلی
        add_submenu_page(
            'setia-content-generator',
            'تنظیمات کرون داخلی',
            'کرون داخلی',
            'manage_options',
            'setia-internal-cron',
            array($this, 'internal_cron_settings_page')
        );
    }
    
    /**
     * صفحه زمانبندی
     */
    public function scheduler_page() {
        // بارگذاری قالب صفحه زمانبندی
        include_once(plugin_dir_path(dirname(__FILE__)) . 'templates/scheduler-page.php');
    }
    
    /**
     * صفحه تنظیمات کرون سرور
     */
    public function cron_settings_page() {
        // بارگذاری قالب صفحه تنظیمات کرون
        include_once(plugin_dir_path(dirname(__FILE__)) . 'templates/cron-settings.php');
    }
    
    /**
     * صفحه تنظیمات کرون داخلی
     */
    public function internal_cron_settings_page() {
        // بارگذاری قالب صفحه تنظیمات کرون داخلی
        include_once(plugin_dir_path(dirname(__FILE__)) . 'templates/internal-cron-settings.php');
    }
    
    /**
     * ذخیره زمانبندی جدید
     */
    public function save_schedule() {
        // دیباگ: لاگ کردن داده‌های دریافتی
        error_log('SETIA DEBUG - داده‌های دریافتی: ' . print_r($_POST, true));
        
        // بررسی امنیتی
        if (!wp_verify_nonce($_POST['nonce'], 'setia-nonce') || !current_user_can('manage_options')) {
            error_log('SETIA DEBUG - خطای امنیتی: توکن نامعتبر');
            wp_send_json_error(array('message' => 'خطای امنیتی: توکن نامعتبر'));
            return;
        }
        
        $schedule_id = isset($_POST['schedule_id']) ? intval($_POST['schedule_id']) : 0;
        $title = isset($_POST['title']) ? sanitize_text_field($_POST['title']) : '';
        $topic = isset($_POST['topic']) ? sanitize_text_field($_POST['topic']) : '';
        $keywords = isset($_POST['keywords']) ? sanitize_text_field($_POST['keywords']) : '';
        $category = isset($_POST['category']) ? intval($_POST['category']) : 0;
        $tone = isset($_POST['tone']) ? sanitize_text_field($_POST['tone']) : '';
        $length = isset($_POST['length']) ? sanitize_text_field($_POST['length']) : '';
        $frequency = isset($_POST['frequency']) ? sanitize_text_field($_POST['frequency']) : '';
        $status = isset($_POST['status']) ? sanitize_text_field($_POST['status']) : '';
        $daily_limit = isset($_POST['daily_limit']) ? intval($_POST['daily_limit']) : 0;
        $generate_image = isset($_POST['generate_image']) ? 'yes' : (isset($_POST['ai_generated']) && $_POST['ai_generated'] === '1' ? 'yes' : 'no');
        
        // دیباگ: لاگ کردن مقادیر استخراج شده
        error_log("SETIA DEBUG - مقادیر استخراج شده: title=$title, topic=$topic, keywords=$keywords, frequency=$frequency");
        
        // بررسی اطلاعات ضروری
        if (empty($title) || empty($topic) || empty($keywords) || empty($frequency)) {
            $missing = [];
            if (empty($title)) $missing[] = 'عنوان';
            if (empty($topic)) $missing[] = 'موضوع';
            if (empty($keywords)) $missing[] = 'کلمات کلیدی';
            if (empty($frequency)) $missing[] = 'تناوب زمانی';
            
            $missing_fields = implode(', ', $missing);
            error_log("SETIA DEBUG - فیلدهای خالی: $missing_fields");
            
            wp_send_json_error(array('message' => 'لطفاً تمام فیلدهای ضروری را پر کنید. فیلدهای خالی: ' . $missing_fields));
            return;
        }
        
        // دریافت زمانبندی‌های موجود
        $schedules = get_option('setia_content_schedules', array());
        
        // ایجاد زمانبندی جدید یا ویرایش موجود
        $schedule = array(
            'title' => $title,
            'topic' => $topic,
            'keywords' => $keywords,
            'category' => $category,
            'tone' => $tone,
            'length' => $length,
            'frequency' => $frequency,
            'status' => $status,
            'generate_image' => $generate_image,
            'daily_limit' => $daily_limit,
            'daily_count' => isset($schedule['daily_count']) ? $schedule['daily_count'] : 0,
            'daily_count_date' => isset($schedule['daily_count_date']) ? $schedule['daily_count_date'] : '',
            'last_run' => '',
            'created_at' => current_time('mysql')
        );
        
        if ($schedule_id > 0 && isset($schedules[$schedule_id])) {
            // ویرایش زمانبندی موجود
            $schedule['last_run'] = $schedules[$schedule_id]['last_run'];
            $schedule['daily_count'] = $schedules[$schedule_id]['daily_count'];
            $schedule['daily_count_date'] = $schedules[$schedule_id]['daily_count_date'];
            $schedules[$schedule_id] = $schedule;
            
            // حذف کرون قبلی
            $hook = 'setia_scheduled_content_generation';
            $args = array('schedule_id' => $schedule_id);
            wp_clear_scheduled_hook($hook, $args);
        } else {
            // ایجاد زمانبندی جدید
            $schedule_id = time() . rand(100, 999);
            $schedules[$schedule_id] = $schedule;
        }
        
        // ذخیره زمانبندی‌ها
        update_option('setia_content_schedules', $schedules);
        
        // زمانبندی کرون اگر فعال است
        if ($status === 'active') {
            $hook = 'setia_scheduled_content_generation';
            $args = array('schedule_id' => $schedule_id);
            
            if (!wp_next_scheduled($hook, $args)) {
                wp_schedule_event(time(), $frequency, $hook, $args);
            }
        }
        
        error_log('SETIA DEBUG - زمانبندی با موفقیت ذخیره شد. ID: ' . $schedule_id);
        wp_send_json_success(array(
            'message' => 'زمانبندی با موفقیت ذخیره شد',
            'schedule_id' => $schedule_id
        ));
    }
    
    /**
     * حذف زمانبندی
     */
    public function delete_schedule() {
        // بررسی امنیتی
        if (!wp_verify_nonce($_POST['nonce'], 'setia-nonce') || !current_user_can('manage_options')) {
            wp_send_json_error(array('message' => 'خطای امنیتی: توکن نامعتبر'));
            return;
        }
        
        $schedule_id = isset($_POST['schedule_id']) ? sanitize_text_field($_POST['schedule_id']) : '';
        
        if (empty($schedule_id)) {
            wp_send_json_error(array('message' => 'شناسه زمانبندی نامعتبر است'));
            return;
        }
        
        // دریافت زمانبندی‌های موجود
        $schedules = get_option('setia_content_schedules', array());
        
        // حذف زمانبندی
        if (isset($schedules[$schedule_id])) {
            unset($schedules[$schedule_id]);
            update_option('setia_content_schedules', $schedules);
            
            // حذف کرون
            $hook = 'setia_scheduled_content_generation';
            $args = array('schedule_id' => $schedule_id);
            wp_clear_scheduled_hook($hook, $args);
            
            wp_send_json_success(array('message' => 'زمانبندی با موفقیت حذف شد'));
        } else {
            wp_send_json_error(array('message' => 'زمانبندی مورد نظر یافت نشد'));
        }
    }
    
    /**
     * دریافت لیست زمانبندی‌ها
     */
    public function get_schedules() {
        // بررسی امنیتی
        if (!wp_verify_nonce($_GET['nonce'], 'setia-nonce') || !current_user_can('manage_options')) {
            wp_send_json_error(array('message' => 'خطای امنیتی: توکن نامعتبر'));
            return;
        }
        
        $schedules = get_option('setia_content_schedules', array());
        
        // اطلاعات تکمیلی برای هر زمانبندی
        foreach ($schedules as $id => &$schedule) {
            // نام دسته‌بندی
            $category_name = 'بدون دسته';
            if (!empty($schedule['category'])) {
                $category = get_term($schedule['category'], 'category');
                if (!is_wp_error($category) && $category) {
                    $category_name = $category->name;
                }
            }
            $schedule['category_name'] = $category_name;
            
            // زمان اجرای بعدی
            $next_run = 'غیرفعال';
            if ($schedule['status'] === 'active') {
                $hook = 'setia_scheduled_content_generation';
                $args = array('schedule_id' => $id);
                $next_timestamp = wp_next_scheduled($hook, $args);
                
                if ($next_timestamp) {
                    $next_run = setia_format_jalali_date(date('Y-m-d H:i:s', $next_timestamp));
                } else {
                    $next_run = 'زمانبندی نشده';
                }
            }
            $schedule['next_run'] = $next_run;
            
            // نگاشت وضعیت تولید تصویر شاخص برای سازگاری با رابط کاربری
            $schedule['ai_generated'] = ($schedule['generate_image'] ?? 'no') === 'yes' ? '1' : '0';
        }
        
        wp_send_json_success(array('schedules' => $schedules));
    }
    
    /**
     * تولید محتوای زمانبندی شده
     */
    public function generate_scheduled_content($schedule_id) {
        // دریافت زمانبندی‌ها
        $schedules = get_option('setia_content_schedules', array());
        
        if (!isset($schedules[$schedule_id])) {
            error_log("SETIA SCHEDULER: زمانبندی با شناسه {$schedule_id} یافت نشد");
            return;
        }
        
        $schedule = $schedules[$schedule_id];
        
        // بررسی محدودیت روزانه (در صورت تعریف)
        $current_date = date('Y-m-d', current_time('timestamp'));
        if (!empty($schedule['daily_limit']) && intval($schedule['daily_limit']) > 0) {
            // اگر تاریخ ثبت شمارنده متفاوت است، ریست شود
            if (empty($schedule['daily_count_date']) || $schedule['daily_count_date'] !== $current_date) {
                $schedule['daily_count'] = 0;
                $schedule['daily_count_date'] = $current_date;
            }
            if ($schedule['daily_count'] >= intval($schedule['daily_limit'])) {
                error_log("SETIA SCHEDULER: Daily limit reached for schedule {$schedule_id}. Skipping generation.");
                // به‌روزرسانی زمان آخرین بررسی تا از اجرای مکرر جلوگیری شود
                $schedules[$schedule_id] = $schedule;
                update_option('setia_content_schedules', $schedules);
                return;
            }
        }
        
        try {
            // ساخت داده‌های فرم از زمانبندی
            $form_data = array(
                'topic' => $schedule['topic'],
                'keywords' => $schedule['keywords'],
                'tone' => $schedule['tone'],
                'length' => $schedule['length'],
                'category' => $schedule['category'],
                'generate_image' => $schedule['generate_image']
            );
            
            // بهینه‌سازی عنوان پست
            $optimized_title = $this->optimize_post_title($schedule['topic'], $schedule['keywords']);
            $form_data['optimized_title'] = $optimized_title;
            
            // ساخت پرامپت برای تولید محتوا
            $prompt = $this->build_content_prompt($form_data);
            
            // تنظیم پارامترهای تولید متن
            $params = $this->get_length_params($schedule['length']);
            
            // تولید محتوا
            $response = $this->content_generator->generate_text($prompt, $params);
            
            if (!$response['success']) {
                error_log("SETIA SCHEDULER: خطا در تولید محتوا: " . ($response['error'] ?? 'خطای نامشخص'));
                return;
            }
            
            // تولید تصویر اگر فعال است
            $image_url = null;
            if ($schedule['generate_image'] === 'yes') {
                $image_prompt = "یک تصویر فوق‌العاده با کیفیت UHD 4K و نورپردازی سینمایی از «{$schedule['topic']}»، سبک واقع‌گرایانه، رنگ‌های زنده، عمق میدان، ترکیب‌بندی چشم‌نواز. حتماً کلمات کلیدی زیر در ایده تصویر لحاظ شوند: {$schedule['keywords']}. نسبت تصویر 16:9.";
                $image_response = $this->content_generator->generate_image($image_prompt);
                
                if ($image_response['success']) {
                    $image_url = $image_response['image_url'];
                }
            }
            
            // تولید متا تگ‌های سئو
            $seo_meta = $this->generate_seo_meta($schedule['topic'], $schedule['keywords'], $response['text']);
            
            // ایجاد پست وردپرس
            $post_data = array(
                'post_title' => $optimized_title,
                'post_content' => $response['text'],
                'post_status' => 'draft', // پیش‌نویس
                'post_author' => 1, // نویسنده پیش‌فرض
                'post_category' => array($schedule['category']),
                'meta_input' => array(
                    '_setia_generated' => true,
                    '_setia_schedule_id' => $schedule_id,
                    '_setia_keywords' => $schedule['keywords'],
                    '_setia_seo_title' => $seo_meta['title'],
                    '_setia_seo_description' => $seo_meta['description'],
                    '_setia_seo_focus_keyword' => $seo_meta['focus_keyword']
                )
            );
            
            // اضافه کردن متادیتای Yoast SEO اگر موجود باشد
            if (isset($seo_meta['_yoast_wpseo_title'])) {
                $post_data['meta_input']['_yoast_wpseo_title'] = $seo_meta['_yoast_wpseo_title'];
            }
            if (isset($seo_meta['_yoast_wpseo_metadesc'])) {
                $post_data['meta_input']['_yoast_wpseo_metadesc'] = $seo_meta['_yoast_wpseo_metadesc'];
            }
            if (isset($seo_meta['_yoast_wpseo_focuskw'])) {
                $post_data['meta_input']['_yoast_wpseo_focuskw'] = $seo_meta['_yoast_wpseo_focuskw'];
            }
            if (isset($seo_meta['_yoast_wpseo_linkdex'])) {
                $post_data['meta_input']['_yoast_wpseo_linkdex'] = $seo_meta['_yoast_wpseo_linkdex'];
            }
            if (isset($seo_meta['_yoast_wpseo_content_score'])) {
                $post_data['meta_input']['_yoast_wpseo_content_score'] = $seo_meta['_yoast_wpseo_content_score'];
            }
            if (isset($seo_meta['_yoast_wpseo_estimated-reading-time-minutes'])) {
                $post_data['meta_input']['_yoast_wpseo_estimated-reading-time-minutes'] = $seo_meta['_yoast_wpseo_estimated-reading-time-minutes'];
            }
            
            // درج پست
            $post_id = wp_insert_post($post_data);
            
            if (is_wp_error($post_id)) {
                error_log("SETIA SCHEDULER: خطا در ایجاد پست: " . $post_id->get_error_message());
                return;
            }
            
            // تنظیم تصویر شاخص
            if ($image_url) {
                $this->set_featured_image($post_id, $image_url);
            }
            
            // ذخیره محتوا در جدول تاریخچه
            $this->save_to_history($schedule, $response['text'], $image_url, $seo_meta, $post_id);
            
            // بروزرسانی زمان اجرای آخرین زمانبندی و شمارنده روزانه
            $schedule['last_run'] = current_time('mysql');
            // افزایش شمارنده روزانه
            if (!isset($schedule['daily_count'])) {
                $schedule['daily_count'] = 0;
            }
            if (empty($schedule['daily_count_date']) || $schedule['daily_count_date'] !== $current_date) {
                $schedule['daily_count'] = 0;
                $schedule['daily_count_date'] = $current_date;
            }
            $schedule['daily_count'] += 1;
            $schedules[$schedule_id] = $schedule;
            update_option('setia_content_schedules', $schedules);
            
            error_log("SETIA SCHEDULER: محتوا با موفقیت تولید شد. شناسه پست: {$post_id}");
            
        } catch (Exception $e) {
            error_log("SETIA SCHEDULER: خطای غیرمنتظره در تولید محتوا: " . $e->getMessage());
        }
    }
    
    /**
     * بهینه‌سازی عنوان پست
     */
    private function optimize_post_title($topic, $keywords) {
        // جمع‌آوری کلید های API از تنظیمات
        $settings = get_option('setia_settings', array());
        $gemini_api_key = isset($settings['gemini_api_key']) ? $settings['gemini_api_key'] : '';
        
        // اگر کلید API تنظیم نشده است، از روش ساده استفاده می‌کنیم
        if (empty($gemini_api_key)) {
            return $this->fallback_optimize_post_title($topic, $keywords);
        }
        
        try {
            // ساخت پرامپت برای تولید عنوان بهینه
            $prompt = "تو یک متخصص سئو هستی. لطفاً با توجه به موضوع و کلمات کلیدی زیر، یک عنوان بهینه برای وبلاگ تولید کن: \n";
            $prompt .= "موضوع: {$topic}\n";
            $prompt .= "کلمات کلیدی: {$keywords}\n";
            $prompt .= "قوانین مهم برای عنوان:\n";
            $prompt .= "1. عنوان باید بین 50 تا 60 کاراکتر باشد\n";
            $prompt .= "2. از کلمه کلیدی اصلی در ابتدای عنوان استفاده کن\n";
            $prompt .= "3. عنوان باید جذاب، خلاصه و مستقیم باشد\n";
            $prompt .= "4. از علامت‌های سوال یا تعجب در صورت مناسب بودن استفاده کن\n";
            $prompt .= "5. با استفاده از اعداد یا آمار عنوان را قوی‌تر کن\n";
            $prompt .= "فقط عنوان را برگردان، بدون هیچ توضیح اضافی یا مقدمه.";
            
            // ارسال درخواست به Gemini API
            $url = "https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key={$gemini_api_key}";
            $data = [
                "contents" => [
                    [
                        "parts" => [
                            [
                                "text" => $prompt
                            ]
                        ]
                    ]
                ],
                "generationConfig" => [
                    "temperature" => 0.7,
                    "topK" => 40,
                    "topP" => 0.95,
                    "maxOutputTokens" => 100
                ]
            ];
            
            $response = wp_remote_post($url, [
                'headers' => [
                    'Content-Type' => 'application/json'
                ],
                'body' => json_encode($data),
                'timeout' => 15
            ]);
            
            if (is_wp_error($response)) {
                error_log('SETIA: خطا در تولید عنوان با هوش مصنوعی: ' . $response->get_error_message());
                return $this->fallback_optimize_post_title($topic, $keywords);
            }
            
            $response_code = wp_remote_retrieve_response_code($response);
            if ($response_code !== 200) {
                error_log('SETIA: خطا در دریافت پاسخ از API (کد ' . $response_code . ')');
                return $this->fallback_optimize_post_title($topic, $keywords);
            }
            
            $response_body = wp_remote_retrieve_body($response);
            $result = json_decode($response_body, true);
            
            // بررسی وجود پاسخ معتبر
            if (isset($result['candidates'][0]['content']['parts'][0]['text'])) {
                $ai_title = trim($result['candidates'][0]['content']['parts'][0]['text']);
                
                // حذف نقل قول‌ها از عنوان اگر وجود داشته باشد
                $ai_title = str_replace(array('"', "'", "«", "»"), "", $ai_title);
                
                // بررسی طول عنوان
                if (mb_strlen($ai_title) > 0 && mb_strlen($ai_title) <= 70) {
                    error_log('SETIA: عنوان جدید با AI تولید شد: ' . $ai_title);
                    return $ai_title;
                }
            }
            
            // اگر به هر دلیلی عنوان معتبر از AI دریافت نشد، از روش پشتیبان استفاده می‌کنیم
            return $this->fallback_optimize_post_title($topic, $keywords);
            
        } catch (Exception $e) {
            error_log('SETIA: خطا در فرایند تولید عنوان: ' . $e->getMessage());
            return $this->fallback_optimize_post_title($topic, $keywords);
        }
    }
    
    /**
     * روش پشتیبان برای بهینه‌سازی عنوان
     */
    private function fallback_optimize_post_title($topic, $keywords) {
        // استخراج کلیدواژه اصلی
        $main_keyword = trim(explode(',', $keywords)[0]);
        
        // بررسی اینکه آیا کلیدواژه اصلی در عنوان وجود دارد
        if (stripos($topic, $main_keyword) !== false) {
            // اگر کلیدواژه در عنوان وجود دارد، فقط بهینه‌سازی ساختار
            $optimized_title = $topic;
        } else {
            // بهترین روش: قرار دادن کلیدواژه در ابتدای عنوان برای SEO بهتر
            $optimized_title = $main_keyword . ': ' . $topic;
            
            // روش جایگزین اگر عنوان خیلی طولانی شود
            if (mb_strlen($optimized_title) > 60) {
                $optimized_title = $topic . ' | ' . $main_keyword;
            }
        }
        
        // محدود کردن طول عنوان به 60 کاراکتر (بهینه برای نمایش در نتایج گوگل)
        if (mb_strlen($optimized_title) > 60) {
            // برش عنوان با حفظ کلمات کامل
            $optimized_title = mb_substr($optimized_title, 0, 57);
            // برش تا آخرین فاصله برای جلوگیری از قطع شدن کلمات
            $last_space = mb_strrpos($optimized_title, ' ');
            if ($last_space !== false) {
                $optimized_title = mb_substr($optimized_title, 0, $last_space);
            }
            $optimized_title .= '...';
        }
        
        return $optimized_title;
    }
    
    /**
     * ساخت پرامپت برای تولید محتوا
     */
    private function build_content_prompt($form_data) {
        $topic = $form_data['topic'];
        $keywords = $form_data['keywords'];
        $tone = $form_data['tone'];
        $length = $form_data['length'];
        $optimized_title = $form_data['optimized_title'] ?? $topic;
        
        // استخراج کلیدواژه اصلی
        $main_keyword = trim(explode(',', $keywords)[0]);
        
        $prompt = "لطفاً یک مقاله با عنوان «{$optimized_title}» در مورد «{$topic}» بنویس با کلمات کلیدی: {$keywords}.\n";
        $prompt .= "لحن مقاله باید {$tone} باشد.\n";
        
        // اضافه کردن طول مطلب
        switch ($length) {
            case 'کوتاه':
                $prompt .= "مقاله باید کوتاه (حدود ۵۰۰ کلمه) باشد.\n";
                break;
            case 'متوسط':
                $prompt .= "مقاله باید متوسط (حدود ۱۰۰۰ کلمه) باشد.\n";
                break;
            case 'بلند':
                $prompt .= "مقاله باید بلند (حدود ۱۵۰۰ کلمه) باشد.\n";
                break;
            case 'خیلی بلند':
                $prompt .= "مقاله باید خیلی بلند (حدود ۲۰۰۰ کلمه) باشد.\n";
                break;
        }
        
        // دستورالعمل‌های مربوط به SEO (بر اساس بهترین شیوه‌های Yoast SEO)
        $prompt .= "\n\nدستورالعمل‌های بهینه‌سازی SEO (بسیار مهم):";
        
        // کلمه کلیدی در مقدمه
        $prompt .= "\n1. بسیار مهم: کلمه کلیدی اصلی ({$main_keyword}) را در 10% ابتدایی متن و ترجیحاً در جمله اول پاراگراف اول استفاده کن. این به موتورهای جستجو نشان می‌دهد که متن دقیقاً درباره چیست.";
        
        // توزیع کلمه کلیدی
        $prompt .= "\n2. توزیع کلمات کلیدی را در کل متن به صورت طبیعی و یکنواخت انجام بده. کلمه کلیدی باید در ابتدا، وسط و انتهای متن ظاهر شود. حداقل 30% از پاراگراف‌ها باید شامل کلمه کلیدی یا مترادف‌های آن باشند.";
        
        // تراکم کلمه کلیدی
        $prompt .= "\n3. تراکم کلمه کلیدی را بین 1% تا 2.5% حفظ کن. یعنی در یک متن 1000 کلمه‌ای، کلمه کلیدی اصلی باید بین 10 تا 25 بار تکرار شود. بیشتر از این مقدار می‌تواند به عنوان کلمه‌چینی (keyword stuffing) شناخته شود.";
        
        // استفاده از مترادف‌ها
        $prompt .= "\n4. از مترادف‌ها و کلمات مرتبط با کلمه کلیدی اصلی استفاده کن. به جای تکرار دقیقاً همان عبارت کلیدی، از واریانت‌های مختلف و مترادف‌های آن استفاده کن تا متن طبیعی‌تر به نظر برسد.";
        
        // کلمه کلیدی در زیرعنوان‌ها
        $prompt .= "\n5. حتماً در زیرعنوان‌های H2 و H3 از کلمات کلیدی یا مترادف آنها استفاده کن. حداقل 50٪ از زیرعنوان‌ها باید شامل کلمات کلیدی یا مترادف آنها باشند. ساختار زیرعنوان‌ها باید منطقی و سلسله مراتبی باشد.";
        
        // ساختار لینک‌ها
        $prompt .= "\n6. حداقل 2 لینک داخلی به مطالب مرتبط اضافه کن. متن لینک (anchor text) باید شامل کلمات کلیدی مرتبط باشد و توصیف دقیقی از صفحه مقصد ارائه دهد. از فرمت [متن لینک مرتبط با کلمه کلیدی](/sample-page) استفاده کن.";
        
        // لینک‌های خارجی
        $prompt .= "\n7. حداقل 2 لینک خارجی به منابع معتبر و مرتبط اضافه کن. این لینک‌ها باید به سایت‌های با اعتبار بالا اشاره کنند و از فرمت [متن لینک مرتبط](https://example.com) استفاده کنند.";
        
        // دستورالعمل‌های قالب‌بندی متن
        $prompt .= "\n\nدستورالعمل‌های قالب‌بندی متن:";
        $prompt .= "\n1. ساختار مقاله باید شامل عناوین و زیر عنوان‌ها باشد. برای عنوان اصلی از #، برای زیرعنوان‌ها از ## و ### و به همین ترتیب استفاده کن. هر عنوان باید در یک خط جداگانه باشد.";
        $prompt .= "\n2. کلمات کلیدی و عبارات مهم را با **دو ستاره در دو طرف** برای بولد کردن، و با *یک ستاره در دو طرف* برای ایتالیک کردن مشخص کن.";
        $prompt .= "\n3. در صورت نیاز به لیست، از لیست‌های نشانه‌دار (مانند - آیتم اول) یا شماره‌دار (مانند 1. آیتم اول) استفاده کن. هر آیتم لیست باید در یک خط جداگانه باشد.";
        $prompt .= "\n4. برای نقل قول مستقیم، پاراگراف را با علامت < در ابتدای خط شروع کن.";
        $prompt .= "\n5. اگر نیاز به درج لینک بود، از فرمت [متن لینک](آدرس URL) استفاده کن.";
        $prompt .= "\n6. مقاله باید دارای مقدمه، بدنه و نتیجه‌گیری باشد.";
        
        // راهنمای کیفیت و تخصصی بودن مقاله
        $prompt .= "\n\nراهنمای پیشرفته کیفیت محتوا:";
        $prompt .= "\n8. از داده‌ها و آمار مستند استفاده کن و برای هر آمار یک منبع در قالب [منبع] ذکر کن.";
        $prompt .= "\n9. حداقل دو مثال واقعی (ترجیحاً مرتبط با بازار ایران) ارائه کن تا موضوع ملموس‌تر شود.";
        $prompt .= "\n10. از لحن فعال، جملات کوتاه و واضح استفاده کن و از واژه‌های تخصصی حوزه مربوطه بهره ببر.";
        $prompt .= "\n11. در صورت امکان از ساختار 'هرم معکوس' برای ارائه مهم‌ترین اطلاعات در ابتدا استفاده کن.";
        $prompt .= "\n12. در انتهای مقاله یک بخش \"جمع‌بندی و اقدامات پیشنهادی\" ارائه کن که خواننده بتواند فوراً اجرا کند.";
        $prompt .= "\n13. کیفیت نگارش باید در حد یک نویسنده سنیور سئو با سابقه 10 سال باشد؛ خطاهای نگارشی یا تکرار مجاز نیست.";
        $prompt .= "\n14. از Markdown برای فرمت‌بندی استفاده کن و از جداول در صورت نیاز برای ارائه داده‌ها بهره ببر.";
        
        // اضافه کردن دستورالعمل ضد تکرار
        $prompt .= "\n\nمهم: این مقاله باید حداقل ۷۰٪ متفاوت از مقالات قبلی تولید شده در همین موضوع باشد. زاویه جدیدی انتخاب کن، مثال‌ها و نکات تازه ارائه بده و از تکرار ساختار یا جملات قبلی خودداری کن.";
        
        // افزودن شناسه یکتا بر اساس زمان برای افزایش تنوع پاسخ مدل
        $prompt .= "\nشناسه_یکتا: " . current_time('timestamp');
        
        return $prompt;
    }
    
    /**
     * تنظیم پارامترهای تولید متن
     */
    private function get_length_params($length) {
        $params = array();
        
        switch ($length) {
            case 'کوتاه':
                $params['max_tokens'] = 1000;
                $params['temperature'] = 0.7; // افزایش تنوع
                break;
            case 'متوسط':
                $params['max_tokens'] = 2000;
                $params['temperature'] = 0.8; // افزایش تنوع
                break;
            case 'بلند':
                $params['max_tokens'] = 3000;
                $params['temperature'] = 0.85; // افزایش تنوع
                break;
            case 'خیلی بلند':
                $params['max_tokens'] = 4000;
                $params['temperature'] = 0.9; // افزایش تنوع
                break;
        }
        
        return $params;
    }
    
    /**
     * تولید متا تگ‌های سئو
     */
    private function generate_seo_meta($topic, $keywords, $content) {
        $keywords_array = array_map('trim', explode(',', $keywords));
        $primary_keyword = $keywords_array[0] ?? '';
        
        // حذف HTML tags و تبدیل به متن ساده
        $clean_content = wp_strip_all_tags($content);
        
        // ایجاد توضیحات متا با طول مناسب (حداکثر 155 کاراکتر طبق توصیه Yoast)
        $meta_description = mb_substr($clean_content, 0, 150);
        
        // اطمینان از وجود کلمه کلیدی در متا
        if (stripos($meta_description, $primary_keyword) === false) {
            // اضافه کردن کلمه کلیدی به ابتدای متا
            $meta_description = $primary_keyword . ': ' . $meta_description;
        }
        
        // محدود کردن طول متا به حداکثر 150 کاراکتر با حفظ کلمات کامل
        if (mb_strlen($meta_description) > 150) {
            $meta_description = mb_substr($meta_description, 0, 110);
            $last_space = mb_strrpos($meta_description, ' ');
            if ($last_space !== false) {
                $meta_description = mb_substr($meta_description, 0, $last_space);
            }
            $meta_description .= '...';
        }
        
        // ایجاد عنوان سئو با فرمت مناسب
        $seo_title = $topic;
        if (mb_strlen($seo_title) > 60) {
            $seo_title = mb_substr($seo_title, 0, 57) . '...';
        }
        
        // محاسبه تخمین زمان مطالعه
        $reading_time = ceil(str_word_count($clean_content) / 200); // تخمین زمان مطالعه بر اساس 200 کلمه در دقیقه
        
        // ایجاد متادیتای Yoast SEO
        return array(
            'title' => $seo_title,
            'description' => $meta_description,
            'keywords' => implode(', ', $keywords_array),
            'focus_keyword' => $primary_keyword,
            '_yoast_wpseo_title' => $seo_title,
            '_yoast_wpseo_metadesc' => $meta_description,
            '_yoast_wpseo_focuskw' => $primary_keyword,
            '_yoast_wpseo_meta-robots-noindex' => '0',
            '_yoast_wpseo_meta-robots-nofollow' => '0',
            '_yoast_wpseo_meta-robots-adv' => 'none',
            '_yoast_wpseo_linkdex' => 70, // امتیاز SEO (از 100)
            '_yoast_wpseo_content_score' => 80, // امتیاز خوانایی (از 100)
            '_yoast_wpseo_is_cornerstone' => '0',
            '_yoast_wpseo_estimated-reading-time-minutes' => $reading_time
        );
    }
    
    /**
     * ذخیره محتوای تولید شده در جدول تاریخچه
     */
    private function save_to_history($schedule, $text, $image_url, $seo_meta, $post_id) {
        global $wpdb;
        $table_name = $wpdb->prefix . 'setia_generated_content';
        
        $data = array(
            'post_id' => $post_id,
            'topic' => sanitize_text_field($schedule['topic']),
            'keywords' => sanitize_text_field($schedule['keywords']),
            'tone' => sanitize_text_field($schedule['tone']),
            'category' => sanitize_text_field($schedule['category']),
            'length' => sanitize_text_field($schedule['length']),
            'generated_text' => $text,
            'generated_image_url' => $image_url,
            'seo_meta' => json_encode($seo_meta),
            'created_at' => current_time('mysql')
        );
        
        $result = $wpdb->insert($table_name, $data);
        
        if ($result === false) {
            error_log("SETIA SCHEDULER: خطا در ذخیره محتوا در جدول تاریخچه: " . $wpdb->last_error);
            return false;
        }
        
        return $wpdb->insert_id;
    }
    
    /**
     * تنظیم تصویر شاخص
     */
    private function set_featured_image($post_id, $image_url) {
        // دانلود تصویر
        $upload_dir = wp_upload_dir();
        $image_data = file_get_contents($image_url);
        
        if ($image_data === false) {
            error_log("SETIA SCHEDULER: خطا در دانلود تصویر از URL: {$image_url}");
            return false;
        }
        
        $filename = basename($image_url);
        
        // اگر نام فایل مشخص نیست، یک نام تصادفی ایجاد کنید
        if (empty($filename) || strpos($filename, '?') !== false || strlen($filename) > 60) {
            $filename = 'setia-image-' . time() . '.jpg';
        }
        
        $file_path = $upload_dir['path'] . '/' . $filename;
        file_put_contents($file_path, $image_data);
        
        // بررسی نوع فایل
        $wp_filetype = wp_check_filetype($filename, null);
        
        // آرایه اطلاعات پیوست
        $attachment = array(
            'post_mime_type' => $wp_filetype['type'],
            'post_title' => sanitize_file_name($filename),
            'post_content' => '',
            'post_status' => 'inherit'
        );
        
        // درج تصویر به عنوان پیوست
        $attach_id = wp_insert_attachment($attachment, $file_path, $post_id);
        
        if (is_wp_error($attach_id)) {
            error_log("SETIA SCHEDULER: خطا در درج پیوست: " . $attach_id->get_error_message());
            return false;
        }
        
        // تولید متادیتای تصویر
        require_once(ABSPATH . 'wp-admin/includes/image.php');
        $attach_data = wp_generate_attachment_metadata($attach_id, $file_path);
        wp_update_attachment_metadata($attach_id, $attach_data);
        
        // تنظیم به عنوان تصویر شاخص
        set_post_thumbnail($post_id, $attach_id);
        
        return true;
    }
    
    /**
     * دریافت گزینه‌های تناوب زمانی
     */
    public function get_frequency_options() {
        return array(
            'minutely' => 'هر دقیقه',
            'every5minutes' => 'هر 5 دقیقه',
            'every15minutes' => 'هر 15 دقیقه',
            'every30minutes' => 'هر 30 دقیقه',
            'hourly' => 'هر ساعت',
            'twicedaily' => 'دو بار در روز',
            'daily' => 'روزانه',
            'weekly' => 'هفتگی',
            'monthly' => 'ماهانه'
        );
    }

    public function display_schedule_form() {
        $schedule_id = isset($_GET['schedule_id']) ? sanitize_text_field($_GET['schedule_id']) : '';
        $schedules = get_option('setia_content_schedules', array());
        $schedule = array();
        
        if (!empty($schedule_id) && isset($schedules[$schedule_id])) {
            $schedule = $schedules[$schedule_id];
        }
        
        $title = !empty($schedule_id) ? 'ویرایش زمانبندی' : 'افزودن زمانبندی جدید';
        $button_text = !empty($schedule_id) ? 'بروزرسانی زمانبندی' : 'ذخیره زمانبندی';
        $frequency_options = $this->get_frequency_options();
        
        ?>
        <div class="wrap">
            <h1><?php echo esc_html($title); ?></h1>
            
            <form method="post" action="<?php echo esc_url(admin_url('admin-post.php')); ?>">
                <input type="hidden" name="action" value="setia_save_schedule">
                <?php wp_nonce_field('setia_save_schedule', 'setia_schedule_nonce'); ?>
                
                <?php if (!empty($schedule_id)): ?>
                    <input type="hidden" name="schedule_id" value="<?php echo esc_attr($schedule_id); ?>">
                <?php endif; ?>
                
                <table class="form-table">
                    <tr>
                        <th scope="row"><label for="schedule_name">نام زمانبندی</label></th>
                        <td>
                            <input type="text" name="schedule_name" id="schedule_name" class="regular-text" 
                                value="<?php echo esc_attr(isset($schedule['name']) ? $schedule['name'] : ''); ?>" required>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row"><label for="schedule_frequency">تناوب زمانی</label></th>
                        <td>
                            <select name="schedule_frequency" id="schedule_frequency">
                                <?php foreach ($frequency_options as $value => $label): ?>
                                    <option value="<?php echo esc_attr($value); ?>" 
                                        <?php selected(isset($schedule['frequency']) ? $schedule['frequency'] : 'daily', $value); ?>>
                                        <?php echo esc_html($label); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row"><label for="prompt">پرامپت</label></th>
                        <td>
                            <textarea name="prompt" id="prompt" class="large-text" rows="5" required><?php 
                                echo esc_textarea(isset($schedule['prompt']) ? $schedule['prompt'] : ''); 
                            ?></textarea>
                            <p class="description">پرامپت برای تولید محتوا را وارد کنید.</p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row"><label for="post_title">عنوان نوشته</label></th>
                        <td>
                            <input type="text" name="post_title" id="post_title" class="regular-text" 
                                value="<?php echo esc_attr(isset($schedule['post_title']) ? $schedule['post_title'] : ''); ?>">
                            <p class="description">اختیاری - اگر خالی باشد، عنوان به صورت خودکار تولید می‌شود.</p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row"><label for="post_type">نوع نوشته</label></th>
                        <td>
                            <select name="post_type" id="post_type">
                                <?php 
                                $post_types = get_post_types(array('public' => true), 'objects');
                                foreach ($post_types as $post_type): 
                                    if ($post_type->name == 'attachment') continue;
                                ?>
                                    <option value="<?php echo esc_attr($post_type->name); ?>" 
                                        <?php selected(isset($schedule['post_type']) ? $schedule['post_type'] : 'post', $post_type->name); ?>>
                                        <?php echo esc_html($post_type->labels->singular_name); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row"><label for="post_status">وضعیت نوشته</label></th>
                        <td>
                            <select name="post_status" id="post_status">
                                <option value="publish" <?php selected(isset($schedule['post_status']) ? $schedule['post_status'] : 'draft', 'publish'); ?>>منتشر شده</option>
                                <option value="draft" <?php selected(isset($schedule['post_status']) ? $schedule['post_status'] : 'draft', 'draft'); ?>>پیش‌نویس</option>
                                <option value="pending" <?php selected(isset($schedule['post_status']) ? $schedule['post_status'] : 'draft', 'pending'); ?>>در انتظار بررسی</option>
                            </select>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row"><label for="category">دسته‌بندی</label></th>
                        <td>
                            <?php 
                            $categories = get_categories(array('hide_empty' => false));
                            if (!empty($categories)): 
                            ?>
                                <select name="category" id="category">
                                    <option value="">-- انتخاب دسته‌بندی --</option>
                                    <?php foreach ($categories as $category): ?>
                                        <option value="<?php echo esc_attr($category->term_id); ?>" 
                                            <?php selected(isset($schedule['category']) ? $schedule['category'] : '', $category->term_id); ?>>
                                            <?php echo esc_html($category->name); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            <?php else: ?>
                                <p>هیچ دسته‌بندی یافت نشد.</p>
                            <?php endif; ?>
                        </td>
                    </tr>
                </table>
                
                <p class="submit">
                    <input type="submit" name="submit" id="submit" class="button button-primary" value="<?php echo esc_attr($button_text); ?>">
                </p>
            </form>
        </div>
        <?php
    }
}
